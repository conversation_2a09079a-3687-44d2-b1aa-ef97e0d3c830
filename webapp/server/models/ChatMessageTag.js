import db from '../db.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Model for chat message tags
 */
class ChatMessageTag {
  /**
   * Add a tag to a message
   * @param {Object} tag - Tag data
   * @param {string} tag.message_id - Message ID
   * @param {string} tag.tag - Tag text
   * @param {string} tag.created_by - User ID who created the tag
   * @returns {Promise<Object>} Created tag
   */
  static async create(tag) {
    const id = uuidv4();
    const { message_id, tag: tagText, created_by } = tag;

    const query = `
      INSERT INTO chat_message_tags (id, message_id, tag, created_by)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (message_id, tag) DO NOTHING
      RETURNING *
    `;

    const result = await db.query(query, [id, message_id, tagText, created_by]);
    return result.rows[0];
  }

  /**
   * Get all tags for a message
   * @param {string} messageId - Message ID
   * @returns {Promise<Array>} Tags
   */
  static async getByMessageId(messageId) {
    const query = `
      SELECT t.*, u.first_name, u.last_name, u.email
      FROM chat_message_tags t
      LEFT JOIN users u ON t.created_by = u.id
      WHERE t.message_id = $1
      ORDER BY t.created_at ASC
    `;

    const result = await db.query(query, [messageId]);
    return result.rows;
  }

  /**
   * Remove a tag from a message
   * @param {string} messageId - Message ID
   * @param {string} tag - Tag text
   * @returns {Promise<boolean>} Success
   */
  static async remove(messageId, tag) {
    const query = `
      DELETE FROM chat_message_tags
      WHERE message_id = $1 AND tag = $2
      RETURNING id
    `;

    const result = await db.query(query, [messageId, tag]);
    return result.rowCount > 0;
  }

  /**
   * Search messages by tag
   * @param {string} tag - Tag to search for
   * @param {string} userId - User ID (to limit search to accessible conversations)
   * @returns {Promise<Array>} Matching messages
   */
  static async searchByTag(tag, userId) {
    const query = `
      SELECT m.*, 
             c.name as conversation_name,
             c.type as conversation_type,
             u.first_name as sender_first_name, 
             u.last_name as sender_last_name
      FROM chat_messages m
      JOIN chat_message_tags t ON m.id = t.message_id
      JOIN chat_conversations c ON m.conversation_id = c.id
      JOIN chat_conversation_participants cp ON c.id = cp.conversation_id
      LEFT JOIN users u ON m.sender_id = u.id
      WHERE cp.user_id = $1
      AND t.tag = $2
      ORDER BY m.created_at DESC
      LIMIT 100
    `;

    const result = await db.query(query, [userId, tag]);
    return result.rows;
  }
}

export default ChatMessageTag;
