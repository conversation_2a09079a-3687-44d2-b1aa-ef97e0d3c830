import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const StripeFinancialConnection = defineModel('StripeFinancialConnection', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false
  },
  stripe_session_id: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  stripe_account_id: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  institution_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  last_successful_update: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.STRING(50),
    defaultValue: 'active'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'stripe_financial_connections',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default StripeFinancialConnection;