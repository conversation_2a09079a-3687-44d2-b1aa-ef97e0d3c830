import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const ServiceProvider = defineModel('ServiceProvider', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  service_type: {
    type: DataTypes.ENUM('agronomy', 'custom_application', 'harvesting', 'planting', 'soil_testing', 'transportation', 'veterinary', 'maintenance', 'other'),
    allowNull: false,
    defaultValue: 'other'
  },
  contact_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  website: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  service_area: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Geographic area where services are provided'
  },
  rates: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Pricing information'
  },
  availability: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Availability information'
  },
  certifications: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Professional certifications'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 5
    },
    comment: 'Average rating (0-5)'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_verified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the provider has been verified'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'service_providers',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default ServiceProvider;