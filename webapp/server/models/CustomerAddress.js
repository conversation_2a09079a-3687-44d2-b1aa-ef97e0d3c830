import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Customer from './Customer.js';
import Farm from './Farm.js';

const CustomerAddress = defineModel('CustomerAddress', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Customer,
      key: 'id'
    },
    comment: 'The customer this address belongs to'
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'The farm this address is associated with'
  },
  farm_alias: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Custom name/alias for the farm from the customer perspective'
  },
  address: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  state: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  zip_code: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  country: {
    type: DataTypes.STRING(100),
    defaultValue: 'USA'
  },
  delivery_instructions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Special instructions for delivery to this address'
  },
  access_code: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Access code for gated communities or secured buildings'
  },
  contact_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Contact person at this address'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Phone number for the contact person'
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this is the default address for the customer'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'customer_addresses',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Note: Associations will be defined in associations.js

export default CustomerAddress;