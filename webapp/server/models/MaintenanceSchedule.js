import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Equipment from './Equipment.js';
import dotenv from 'dotenv';

dotenv.config();

const MaintenanceSchedule = defineModel('MaintenanceSchedule', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Equipment,
      key: 'id'
    }
  },
  maintenance_type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  frequency_type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  frequency_value: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  last_performed_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  last_performed_hours: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  next_due_date: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  next_due_hours: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  alert_threshold: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'maintenance_schedules',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default MaintenanceSchedule;