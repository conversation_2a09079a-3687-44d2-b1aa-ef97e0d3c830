import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import Farm from './Farm.js';
import Crop from './Crop.js';
import dotenv from 'dotenv';
import { defineModel } from '../utils/modelUtils.js';

dotenv.config();

const Field = defineModel('Field', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  size: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  size_unit: {
    type: DataTypes.STRING(50),
    defaultValue: 'acres'
  },
  area: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Field area (mirrors size column)'
  },
  area_unit: {
    type: DataTypes.STRING(50),
    defaultValue: 'acres',
    comment: 'Unit of measurement for area (mirrors size_unit column)'
  },
  field_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  crop_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  status: {
    type: DataTypes.STRING(50),
    defaultValue: 'active'
  },
  location_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'GeoJSON for field boundaries'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'fields',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default Field;
