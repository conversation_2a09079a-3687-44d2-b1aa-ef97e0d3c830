import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import User from './User.js';
import Farm from './Farm.js';

dotenv.config();

const DocumentFolder = defineModel('DocumentFolder', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  parent_folder_id: {
    type: DataTypes.UUID,
    allowNull: false
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  is_secure: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_folders',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_folders_name_idx',
      fields: ['name']
    },
    {
      name: 'document_folders_parent_folder_id_idx',
      fields: ['parent_folder_id']
    },
    {
      name: 'document_folders_farm_id_idx',
      fields: ['farm_id']
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DocumentFolder;
