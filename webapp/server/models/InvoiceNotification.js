import { DataTypes } from 'sequelize';
import { defineModel } from '../utils/modelUtils.js';
import Invoice from './Invoice.js';
import User from './User.js';
import Farm from './Farm.js';

const InvoiceNotification = defineModel('InvoiceNotification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Invoice,
      key: 'id'
    },
    comment: 'The invoice this notification relates to'
  },
  recipient_user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'Specific user to notify (null for farm-wide notifications)'
  },
  recipient_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Farm to notify'
  },
  sender_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Farm that triggered the notification'
  },
  notification_type: {
    type: DataTypes.ENUM(
      'invoice_received',
      'invoice_viewed',
      'invoice_paid',
      'invoice_overdue',
      'invoice_disputed',
      'dispute_message',
      'dispute_resolved',
      'payment_received',
      'reminder_sent'
    ),
    allowNull: false,
    comment: 'Type of notification'
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Notification title'
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Notification message content'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium',
    comment: 'Priority level of the notification'
  },
  channels: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['in_app'],
    comment: 'Array of notification channels (in_app, email, sms, push)'
  },
  read: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether the notification has been read'
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the notification was read'
  },
  action_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'URL for action to take on this notification'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata for the notification'
  },
  email_sent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether email notification was sent'
  },
  email_sent_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When email notification was sent'
  },
  sms_sent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether SMS notification was sent'
  },
  sms_sent_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When SMS notification was sent'
  },
  push_sent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether push notification was sent'
  },
  push_sent_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When push notification was sent'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoice_notifications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'invoice_notifications_invoice_id_idx',
      fields: ['invoice_id']
    },
    {
      name: 'invoice_notifications_recipient_user_id_idx',
      fields: ['recipient_user_id']
    },
    {
      name: 'invoice_notifications_recipient_farm_id_idx',
      fields: ['recipient_farm_id']
    },
    {
      name: 'invoice_notifications_notification_type_idx',
      fields: ['notification_type']
    },
    {
      name: 'invoice_notifications_read_idx',
      fields: ['read']
    },
    {
      name: 'invoice_notifications_created_at_idx',
      fields: ['created_at']
    }
  ]
});

export default InvoiceNotification;
