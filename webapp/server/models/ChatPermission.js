import db from '../db.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Model for chat permissions
 */
class ChatPermission {
  /**
   * Create or update chat permissions for a farm role
   * @param {Object} permission - Permission data
   * @param {string} permission.farm_id - Farm ID
   * @param {string} permission.role - Role name (owner, manager, etc.)
   * @param {boolean} permission.can_use_chat - Whether the role can use chat
   * @param {boolean} permission.can_create_group_chats - Whether the role can create group chats
   * @param {boolean} permission.can_share_files - Whether the role can share files
   * @param {boolean} permission.can_create_tasks_from_chat - Whether the role can create tasks from chat
   * @returns {Promise<Object>} Created or updated permission
   */
  static async createOrUpdate(permission) {
    const { 
      farm_id, 
      role, 
      can_use_chat = true, 
      can_create_group_chats = true, 
      can_share_files = true, 
      can_create_tasks_from_chat = true,
      can_chat_with_associated_farms = role === 'owner' || role === 'manager'
    } = permission;

    // Check if permission already exists
    const existingPermission = await this.getByFarmAndRole(farm_id, role);

    if (existingPermission) {
      // Update existing permission
      return this.update(existingPermission.id, {
        can_use_chat,
        can_create_group_chats,
        can_share_files,
        can_create_tasks_from_chat
      });
    }

    // Create new permission
    const id = uuidv4();
    const query = `
      INSERT INTO chat_permissions (
        id, farm_id, role, can_use_chat, can_create_group_chats, 
        can_share_files, can_create_tasks_from_chat
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const result = await db.query(query, [
      id, farm_id, role, can_use_chat, can_create_group_chats, 
      can_share_files, can_create_tasks_from_chat
    ]);

    return result.rows[0];
  }

  /**
   * Get chat permissions by ID
   * @param {string} id - Permission ID
   * @returns {Promise<Object>} Permission
   */
  static async getById(id) {
    const query = `
      SELECT * FROM chat_permissions
      WHERE id = $1
    `;

    const result = await db.query(query, [id]);
    return result.rows[0];
  }

  /**
   * Get chat permissions by farm ID and role
   * @param {string} farmId - Farm ID
   * @param {string} role - Role name
   * @returns {Promise<Object>} Permission
   */
  static async getByFarmAndRole(farmId, role) {
    const query = `
      SELECT * FROM chat_permissions
      WHERE farm_id = $1 AND role = $2
    `;

    const result = await db.query(query, [farmId, role]);
    return result.rows[0];
  }

  /**
   * Get all chat permissions for a farm
   * @param {string} farmId - Farm ID
   * @returns {Promise<Array>} Permissions
   */
  static async getByFarmId(farmId) {
    const query = `
      SELECT * FROM chat_permissions
      WHERE farm_id = $1
      ORDER BY role
    `;

    const result = await db.query(query, [farmId]);
    return result.rows;
  }

  /**
   * Update chat permissions
   * @param {string} id - Permission ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Updated permission
   */
  static async update(id, updates) {
    const allowedFields = [
      'can_use_chat', 
      'can_create_group_chats', 
      'can_share_files', 
      'can_create_tasks_from_chat',
      'can_chat_with_associated_farms'
    ];

    const fields = Object.keys(updates).filter(field => allowedFields.includes(field));

    if (fields.length === 0) {
      return this.getById(id);
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');
    const values = fields.map(field => updates[field]);

    const query = `
      UPDATE chat_permissions
      SET ${setClause}
      WHERE id = $1
      RETURNING *
    `;

    const result = await db.query(query, [id, ...values]);
    return result.rows[0];
  }

  /**
   * Delete chat permissions
   * @param {string} id - Permission ID
   * @returns {Promise<boolean>} Success
   */
  static async delete(id) {
    const query = `
      DELETE FROM chat_permissions
      WHERE id = $1
      RETURNING id
    `;

    const result = await db.query(query, [id]);
    return result.rowCount > 0;
  }

  /**
   * Check if a user has permission to use chat
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {Promise<boolean>} Has permission
   */
  static async canUseChat(userId, farmId) {
    const query = `
      SELECT cp.can_use_chat
      FROM chat_permissions cp
      JOIN user_farms uf ON cp.farm_id = uf.farm_id AND cp.role = uf.role
      WHERE uf.user_id = $1 AND uf.farm_id = $2
    `;

    const result = await db.query(query, [userId, farmId]);

    if (result.rowCount === 0) {
      // If no specific permission is set, default to true
      return true;
    }

    return result.rows[0].can_use_chat;
  }

  /**
   * Check if a user has permission to create group chats
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {Promise<boolean>} Has permission
   */
  static async canCreateGroupChats(userId, farmId) {
    const query = `
      SELECT cp.can_create_group_chats
      FROM chat_permissions cp
      JOIN user_farms uf ON cp.farm_id = uf.farm_id AND cp.role = uf.role
      WHERE uf.user_id = $1 AND uf.farm_id = $2
    `;

    const result = await db.query(query, [userId, farmId]);

    if (result.rowCount === 0) {
      // If no specific permission is set, default to true
      return true;
    }

    return result.rows[0].can_create_group_chats;
  }

  /**
   * Check if a user has permission to share files
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {Promise<boolean>} Has permission
   */
  static async canShareFiles(userId, farmId) {
    const query = `
      SELECT cp.can_share_files
      FROM chat_permissions cp
      JOIN user_farms uf ON cp.farm_id = uf.farm_id AND cp.role = uf.role
      WHERE uf.user_id = $1 AND uf.farm_id = $2
    `;

    const result = await db.query(query, [userId, farmId]);

    if (result.rowCount === 0) {
      // If no specific permission is set, default to true
      return true;
    }

    return result.rows[0].can_share_files;
  }

  /**
   * Check if a user has permission to create tasks from chat
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {Promise<boolean>} Has permission
   */
  static async canCreateTasksFromChat(userId, farmId) {
    const query = `
      SELECT cp.can_create_tasks_from_chat
      FROM chat_permissions cp
      JOIN user_farms uf ON cp.farm_id = uf.farm_id AND cp.role = uf.role
      WHERE uf.user_id = $1 AND uf.farm_id = $2
    `;

    const result = await db.query(query, [userId, farmId]);

    if (result.rowCount === 0) {
      // If no specific permission is set, default to true
      return true;
    }

    return result.rows[0].can_create_tasks_from_chat;
  }

  /**
   * Check if a user has permission to chat with associated farms
   * @param {string} userId - User ID
   * @param {string} farmId - Farm ID
   * @returns {Promise<boolean>} Has permission
   */
  static async canChatWithAssociatedFarms(userId, farmId) {
    const query = `
      SELECT cp.can_chat_with_associated_farms
      FROM chat_permissions cp
      JOIN user_farms uf ON cp.farm_id = uf.farm_id AND cp.role = uf.role
      WHERE uf.user_id = $1 AND uf.farm_id = $2
    `;

    const result = await db.query(query, [userId, farmId]);

    if (result.rowCount === 0) {
      // If no specific permission is set, default to false for security
      return false;
    }

    return result.rows[0].can_chat_with_associated_farms;
  }

  /**
   * Get all permissions for a user across all farms
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Permissions by farm
   */
  static async getUserPermissions(userId) {
    const query = `
      SELECT 
        f.id as farm_id,
        f.name as farm_name,
        uf.role,
        COALESCE(cp.can_use_chat, TRUE) as can_use_chat,
        COALESCE(cp.can_create_group_chats, TRUE) as can_create_group_chats,
        COALESCE(cp.can_share_files, TRUE) as can_share_files,
        COALESCE(cp.can_create_tasks_from_chat, TRUE) as can_create_tasks_from_chat,
        COALESCE(cp.can_chat_with_associated_farms, uf.role IN ('owner', 'manager')) as can_chat_with_associated_farms
      FROM user_farms uf
      JOIN farms f ON uf.farm_id = f.id
      LEFT JOIN chat_permissions cp ON uf.farm_id = cp.farm_id AND uf.role = cp.role
      WHERE uf.user_id = $1
    `;

    const result = await db.query(query, [userId]);
    return result.rows;
  }

  /**
   * Initialize default permissions for a farm
   * @param {string} farmId - Farm ID
   * @returns {Promise<Array>} Created permissions
   */
  static async initializeDefaultPermissions(farmId) {
    // Define default roles and their permissions
    const defaultPermissions = [
      {
        role: 'owner',
        can_use_chat: true,
        can_create_group_chats: true,
        can_share_files: true,
        can_create_tasks_from_chat: true,
        can_chat_with_associated_farms: true
      },
      {
        role: 'manager',
        can_use_chat: true,
        can_create_group_chats: true,
        can_share_files: true,
        can_create_tasks_from_chat: true,
        can_chat_with_associated_farms: true
      },
      {
        role: 'employee',
        can_use_chat: true,
        can_create_group_chats: false,
        can_share_files: true,
        can_create_tasks_from_chat: false,
        can_chat_with_associated_farms: false
      },
      {
        role: 'accountant',
        can_use_chat: true,
        can_create_group_chats: false,
        can_share_files: true,
        can_create_tasks_from_chat: false,
        can_chat_with_associated_farms: false
      },
      {
        role: 'viewer',
        can_use_chat: true,
        can_create_group_chats: false,
        can_share_files: false,
        can_create_tasks_from_chat: false,
        can_chat_with_associated_farms: false
      }
    ];

    const createdPermissions = [];

    for (const perm of defaultPermissions) {
      const created = await this.createOrUpdate({
        farm_id: farmId,
        ...perm
      });

      createdPermissions.push(created);
    }

    return createdPermissions;
  }
}

export default ChatPermission;
