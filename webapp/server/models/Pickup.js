import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';
import Driver from './Driver.js';
import Supplier from './Supplier.js';
import Customer from './Customer.js';
import Order from './Order.js';

dotenv.config();

const Pickup = defineModel('Pickup', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  driver_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Driver,
      key: 'id'
    },
    comment: 'The driver assigned to this pickup'
  },
  supplier_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Supplier,
      key: 'id'
    },
    comment: 'The supplier for this pickup'
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Customer,
      key: 'id'
    },
    comment: 'The customer for this pickup (if applicable)'
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Order,
      key: 'id'
    },
    comment: 'The order associated with this pickup'
  },
  pickup_type: {
    type: DataTypes.ENUM('product', 'equipment', 'return', 'other', 'customer_pickup'),
    allowNull: false,
    defaultValue: 'product'
  },
  status: {
    type: DataTypes.ENUM('scheduled', 'in_progress', 'completed', 'failed', 'cancelled'),
    allowNull: false,
    defaultValue: 'scheduled'
  },
  scheduled_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  estimated_completion: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_pickup_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  pickup_address: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  pickup_city: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  pickup_state: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  pickup_zip: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  pickup_country: {
    type: DataTypes.STRING(100),
    allowNull: false,
    defaultValue: 'USA'
  },
  pickup_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  confirmation_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  confirmation_signature: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Path to the confirmation signature image file'
  },
  proof_of_pickup_image: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Path to the proof of pickup image file'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'pickups',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default Pickup;
