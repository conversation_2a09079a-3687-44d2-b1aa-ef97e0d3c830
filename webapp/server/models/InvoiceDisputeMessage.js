import { DataTypes } from 'sequelize';
import { defineModel } from '../utils/modelUtils.js';
import InvoiceDispute from './InvoiceDispute.js';
import User from './User.js';
import Farm from './Farm.js';

const InvoiceDisputeMessage = defineModel('InvoiceDisputeMessage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  dispute_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: InvoiceDispute,
      key: 'id'
    },
    comment: 'The dispute this message belongs to'
  },
  sender_user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User who sent the message'
  },
  sender_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Farm the sender represents'
  },
  message_type: {
    type: DataTypes.ENUM(
      'message',
      'status_update',
      'resolution_proposal',
      'document_upload',
      'system_note'
    ),
    allowNull: false,
    defaultValue: 'message',
    comment: 'Type of message'
  },
  subject: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Message subject (optional)'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Message content'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of attachment URLs and metadata'
  },
  is_internal: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Whether this is an internal note (not visible to other party)'
  },
  read_by: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Array of user IDs who have read this message'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoice_dispute_messages',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'invoice_dispute_messages_dispute_id_idx',
      fields: ['dispute_id']
    },
    {
      name: 'invoice_dispute_messages_sender_user_id_idx',
      fields: ['sender_user_id']
    },
    {
      name: 'invoice_dispute_messages_created_at_idx',
      fields: ['created_at']
    }
  ]
});

export default InvoiceDisputeMessage;
