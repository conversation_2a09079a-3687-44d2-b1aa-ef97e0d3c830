import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Equipment from './Equipment.js';
import dotenv from 'dotenv';

dotenv.config();

const IsobusData = defineModel('IsobusData', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Equipment,
      key: 'id'
    }
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  // ISOBUS message data
  message_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Type of ISOBUS message (e.g., PGN, DTC)'
  },
  pgn: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: 'Parameter Group Number - identifies the type of data'
  },
  source_address: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Source address of the ISOBUS device'
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Priority of the message (0-7)'
  },
  data_length: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Length of the data in bytes'
  },
  raw_data: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Raw hexadecimal data from the ISOBUS message'
  },
  // Parsed data fields
  parsed_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Parsed data from the ISOBUS message in JSON format'
  },
  // Implement-specific data
  implement_type: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Type of implement (e.g., planter, sprayer, harvester)'
  },
  implement_manufacturer: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Manufacturer of the implement'
  },
  implement_model: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Model of the implement'
  },
  // Task data
  task_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'ID of the task being performed'
  },
  task_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Name of the task being performed'
  },
  field_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'ID of the field where the task is being performed'
  },
  // Status and metadata
  status: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Status of the ISOBUS connection or task'
  },
  error_code: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Error code if there was an issue'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Error message if there was an issue'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'isobus_data',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default IsobusData;