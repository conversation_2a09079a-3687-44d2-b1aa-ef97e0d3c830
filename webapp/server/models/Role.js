import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const Role = defineModel('Role', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'If null, this is a global role. If set, this is a farm-specific role.'
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Name of the role'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Description of the role'
  },
  is_system_role: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system-defined role that cannot be deleted'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'roles',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'roles_farm_name_idx',
      fields: ['farm_id', 'name'],
      unique: true
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default Role;