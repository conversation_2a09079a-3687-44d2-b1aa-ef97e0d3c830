import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import Farm from './Farm.js';

dotenv.config();

const FarmStorageUsage = defineModel('FarmStorageUsage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    unique: true
  },
  total_bytes_used: {
    type: DataTypes.BIGINT,
    allowNull: false,
    defaultValue: 0
  },
  document_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  external_document_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  last_calculated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farm_storage_usage',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'farm_storage_usage_farm_id_idx',
      fields: ['farm_id'],
      unique: true
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default FarmStorageUsage;