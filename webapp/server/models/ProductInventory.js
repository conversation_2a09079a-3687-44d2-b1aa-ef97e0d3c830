import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Product from './Product.js';
import InventoryItem from './InventoryItem.js';
import dotenv from 'dotenv';

dotenv.config();

const ProductInventory = defineModel('ProductInventory', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Product,
      key: 'id'
    }
  },
  inventory_item_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: InventoryItem,
      key: 'id'
    }
  },
  quantity_per_unit: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 1.00,
    comment: 'How many inventory units are used for one product unit'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'product_inventory',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default ProductInventory;