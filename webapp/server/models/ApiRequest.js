import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import ApiEndpoint from './ApiEndpoint.js';
import Farm from './Farm.js';
import Field from './Field.js';
import User from './User.js';
import dotenv from 'dotenv';

dotenv.config();

const ApiRequest = defineModel('ApiRequest', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  endpoint_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: ApiEndpoint,
      key: 'id'
    }
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Field,
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  request_params: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  request_headers: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  request_body: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  response_status: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  response_headers: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  response_body: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  request_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  response_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  duration_ms: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  cache_hit: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'api_requests',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default ApiRequest;