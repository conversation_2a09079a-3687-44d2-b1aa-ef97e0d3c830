import express from 'express';
import {
  getAlerts,
  createAlert,
  markAlertAsRead,
  markAllAlertsAsRead,
  getAlertRules,
  createAlertRule,
  updateAlertRule,
  deleteAlertRule,
  testAlertRule,
  processAlertRules
} from '../controllers/alertController.js';

const router = express.Router();

// Alert routes
// router.get('/', authenticate, getAlerts);
router.get('/', getAlerts);

// router.post('/', authenticate, createAlert);
router.post('/', createAlert);

// router.put('/:alertId/read', authenticate, markAlertAsRead);
router.put('/:alertId/read', markAlertAsRead);

// router.put('/read-all', authenticate, markAllAlertsAsRead);
router.put('/read-all', markAllAlertsAsRead);

// Alert rule routes
// router.get('/rules', authenticate, getAlertRules);
router.get('/rules', getAlertRules);

// router.post('/rules', authenticate, createAlertRule);
router.post('/rules', createAlertRule);

// router.put('/rules/:ruleId', authenticate, updateAlertRule);
router.put('/rules/:ruleId', updateAlertRule);

// router.delete('/rules/:ruleId', authenticate, deleteAlertRule);
router.delete('/rules/:ruleId', deleteAlertRule);

// router.post('/rules/test', authenticate, testAlertRule);
router.post('/rules/test', testAlertRule);

// Process alert rules (would typically be called by a scheduled job)
// router.post('/process', authenticate, processAlertRules);
router.post('/process', processAlertRules);

export default router;