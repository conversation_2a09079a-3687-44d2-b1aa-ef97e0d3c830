import express from 'express';
import {
  getPayStubs,
  getPayStubById,
  createPayStub,
  updatePayStub,
  deletePayStub,
  getPayStubsByEmployee,
  markPayStubAsViewed,
  getPayStubSummary
} from '../controllers/payStubController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all pay stubs with optional filtering
router.get('/', getPayStubs);

// Get pay stub summary
router.get('/summary', getPayStubSummary);

// Get pay stubs by employee ID
router.get('/employee/:employeeId', getPayStubsByEmployee);

// Get pay stub by ID
router.get('/:payStubId', getPayStubById);

// Create a new pay stub
router.post('/', createPayStub);

// Update a pay stub
router.put('/:payStubId', updatePayStub);

// Delete a pay stub
router.delete('/:payStubId', deletePayStub);

// Mark pay stub as viewed
router.put('/:payStubId/view', markPayStubAsViewed);

export default router;