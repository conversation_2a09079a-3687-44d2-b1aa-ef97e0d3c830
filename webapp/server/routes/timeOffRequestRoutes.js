import express from 'express';
import {
  getTimeOffRequests,
  getTimeOffRequestById,
  createTimeOffRequest,
  updateTimeOffRequest,
  deleteTimeOffRequest,
  getTimeOffRequestsByEmployee,
  reviewTimeOffRequest,
  getTimeOffSummary
} from '../controllers/timeOffRequestController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all time off requests with optional filtering
router.get('/', getTimeOffRequests);

// Get time off summary
router.get('/summary', getTimeOffSummary);

// Get time off requests by employee ID
router.get('/employee/:employeeId', getTimeOffRequestsByEmployee);

// Get time off request by ID
router.get('/:requestId', getTimeOffRequestById);

// Create a new time off request
router.post('/', createTimeOffRequest);

// Update a time off request
router.put('/:requestId', updateTimeOffRequest);

// Delete a time off request
router.delete('/:requestId', deleteTimeOffRequest);

// Review a time off request (approve or deny)
router.put('/:requestId/review', reviewTimeOffRequest);

export default router;