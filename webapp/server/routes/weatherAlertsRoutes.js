import express from 'express';
import { protect } from '../middleware/authMiddleware.js';
import {
  getFarmAlerts,
  getFieldAlerts
} from '../controllers/weatherAlertsController.js';

const router = express.Router();

// Protect all routes
router.use(protect);

// GET /api/weather-alerts/farm/:farmId - Get weather alerts for a farm
router.get('/farm/:farmId', getFarmAlerts);

// GET /api/weather-alerts/field/:fieldId - Get weather alerts for a field
router.get('/field/:fieldId', getFieldAlerts);

export default router;