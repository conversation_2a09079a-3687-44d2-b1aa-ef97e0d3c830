import express from 'express';
import {
  getFarmHistoricalTemperature,
  getFieldHistoricalTemperature,
  getFarmHistoricalPrecipitation,
  getFieldHistoricalPrecipitation,
  getFarmClimateNormals,
  getFieldClimateNormals,
  getFarmGrowingDegreeDays,
  getFieldGrowingDegreeDays,
  getFarmFrostFreezeDates,
  getFieldFrostFreezeDates,
  getFarmExtremeWeatherEvents,
  getAllFarmClimateData
} from '../controllers/nceiController.js';

const router = express.Router();

// Historical temperature data endpoints
router.get('/temperature/farm/:farmId', getFarmHistoricalTemperature);
router.get('/temperature/field/:fieldId', getFieldHistoricalTemperature);

// Historical precipitation data endpoints
router.get('/precipitation/farm/:farmId', getFarmHistoricalPrecipitation);
router.get('/precipitation/field/:fieldId', getFieldHistoricalPrecipitation);

// Climate normals endpoints
router.get('/normals/farm/:farmId', getFarmClimateNormals);
router.get('/normals/field/:fieldId', getFieldClimateNormals);

// Growing degree days endpoints
router.get('/gdd/farm/:farmId', getFarmGrowingDegreeDays);
router.get('/gdd/field/:fieldId', getFieldGrowingDegreeDays);

// Frost/freeze dates endpoints
router.get('/frost-freeze/farm/:farmId', getFarmFrostFreezeDates);
router.get('/frost-freeze/field/:fieldId', getFieldFrostFreezeDates);

// Extreme weather events endpoints
router.get('/extreme-events/farm/:farmId', getFarmExtremeWeatherEvents);

// Comprehensive climate data endpoint
router.get('/all/farm/:farmId', getAllFarmClimateData);

export default router;