import express from 'express';
import { 
  getUserSessions, 
  terminateSession, 
  terminateAllSessions,
  trustDevice,
  untrustDevice
} from '../controllers/sessionController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Get all active sessions for the current user
router.get('/', getUserSessions);

// Terminate a specific session
router.delete('/:sessionId', terminateSession);

// Terminate all sessions except the current one
router.delete('/', terminateAllSessions);

// Trust a device
router.post('/trust/:deviceFingerprint', async (req, res) => {
  try {
    const { deviceFingerprint } = req.params;
    const userId = req.user.id;

    const result = await trustDevice(userId, deviceFingerprint);

    if (result) {
      return res.status(200).json({ message: 'Device trusted successfully' });
    } else {
      return res.status(500).json({ error: 'Failed to trust device' });
    }
  } catch (error) {
    console.error('Error trusting device:', error);
    return res.status(500).json({ error: 'Failed to trust device' });
  }
});

// Untrust a device
router.post('/untrust/:deviceFingerprint', async (req, res) => {
  try {
    const { deviceFingerprint } = req.params;
    const userId = req.user.id;

    const result = await untrustDevice(userId, deviceFingerprint);

    if (result) {
      return res.status(200).json({ message: 'Device untrusted successfully' });
    } else {
      return res.status(500).json({ error: 'Failed to untrust device' });
    }
  } catch (error) {
    console.error('Error untrusting device:', error);
    return res.status(500).json({ error: 'Failed to untrust device' });
  }
});

export default router;
