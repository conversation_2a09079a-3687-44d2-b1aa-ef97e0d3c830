import express from 'express';
import {
  getEnvironmentVariables,
  getFrontendEnvironmentVariables
} from '../controllers/environmentController.js';
import { authenticate, isGlobalAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Restrict access to global admin users only
router.get('/variables', authenticate, isGlobalAdmin, getEnvironmentVariables);
router.get('/frontend-variables', authenticate, isGlobalAdmin, getFrontendEnvironmentVariables);

export default router;
