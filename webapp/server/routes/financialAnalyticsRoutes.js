import express from 'express';
import { getFinancialAnalytics, getCashFlowProjections } from '../controllers/financialAnalyticsController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get financial analytics for a farm
router.get('/farms/:farmId/financial-analytics', checkFarmAccess, getFinancialAnalytics);

// Get cash flow projections for a farm
router.get('/farms/:farmId/cash-flow-projections', checkFarmAccess, getCashFlowProjections);

export default router;