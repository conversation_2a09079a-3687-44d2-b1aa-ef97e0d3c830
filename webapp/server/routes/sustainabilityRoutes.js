import express from 'express';
const router = express.Router();
import { check } from 'express-validator';
import * as sustainabilityController from '../controllers/sustainabilityController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Carbon Footprint Calculator Routes
router.post(
  '/carbon-footprint',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('calculationDate', 'Calculation date is required').not().isEmpty(),
    check('fuelEmissions', 'Fuel emissions must be a number').optional().isNumeric(),
    check('electricityEmissions', 'Electricity emissions must be a number').optional().isNumeric(),
    check('fertilizerEmissions', 'Fertilizer emissions must be a number').optional().isNumeric(),
    check('livestockEmissions', 'Livestock emissions must be a number').optional().isNumeric(),
    check('wasteEmissions', 'Waste emissions must be a number').optional().isNumeric(),
  ],
  sustainabilityController.calculateCarbonFootprint
);

router.get('/carbon-footprint', sustainabilityController.getCarbonFootprints);
router.get('/carbon-footprint/:id', sustainabilityController.getCarbonFootprint);

router.put(
  '/carbon-footprint/:id',
  [
    check('calculationDate', 'Calculation date is required').not().isEmpty(),
    check('fuelEmissions', 'Fuel emissions must be a number').optional().isNumeric(),
    check('electricityEmissions', 'Electricity emissions must be a number').optional().isNumeric(),
    check('fertilizerEmissions', 'Fertilizer emissions must be a number').optional().isNumeric(),
    check('livestockEmissions', 'Livestock emissions must be a number').optional().isNumeric(),
    check('wasteEmissions', 'Waste emissions must be a number').optional().isNumeric(),
  ],
  sustainabilityController.updateCarbonFootprint
);

router.delete('/carbon-footprint/:id', sustainabilityController.deleteCarbonFootprint);

// Sustainable Practices Routes
router.post(
  '/practices',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('name', 'Name is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty(),
    check('implementationDate', 'Implementation date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['planned', 'in-progress', 'implemented']),
    check('impact', 'Impact is required').not().isEmpty(),
  ],
  sustainabilityController.createSustainablePractice
);

router.get('/practices', sustainabilityController.getSustainablePractices);
router.get('/practices/:id', sustainabilityController.getSustainablePractice);

router.put(
  '/practices/:id',
  [
    check('name', 'Name is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty(),
    check('implementationDate', 'Implementation date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['planned', 'in-progress', 'implemented']),
    check('impact', 'Impact is required').not().isEmpty(),
  ],
  sustainabilityController.updateSustainablePractice
);

router.delete('/practices/:id', sustainabilityController.deleteSustainablePractice);

// Certifications Routes
router.post(
  '/certifications',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('name', 'Name is required').not().isEmpty(),
    check('certifyingBody', 'Certifying body is required').not().isEmpty(),
    check('issueDate', 'Issue date is required').not().isEmpty(),
    check('expirationDate', 'Expiration date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['pending', 'active', 'expired']),
  ],
  sustainabilityController.createCertification
);

router.get('/certifications', sustainabilityController.getCertifications);
router.get('/certifications/:id', sustainabilityController.getCertification);

router.put(
  '/certifications/:id',
  [
    check('name', 'Name is required').not().isEmpty(),
    check('certifyingBody', 'Certifying body is required').not().isEmpty(),
    check('issueDate', 'Issue date is required').not().isEmpty(),
    check('expirationDate', 'Expiration date is required').not().isEmpty(),
    check('status', 'Status is required').isIn(['pending', 'active', 'expired']),
  ],
  sustainabilityController.updateCertification
);

router.delete('/certifications/:id', sustainabilityController.deleteCertification);

// Environmental Impact Reports Routes
router.post(
  '/impact-reports',
  [
    check('farmId', 'Farm ID is required').not().isEmpty(),
    check('reportDate', 'Report date is required').not().isEmpty(),
    check('reportType', 'Report type is required').not().isEmpty(),
    check('metrics', 'Metrics are required').not().isEmpty(),
    check('metrics.waterUsage', 'Water usage must be a number').isNumeric(),
    check('metrics.soilHealth', 'Soil health must be a number').isNumeric(),
    check('metrics.biodiversity', 'Biodiversity must be a number').isNumeric(),
    check('metrics.wasteReduction', 'Waste reduction must be a number').isNumeric(),
    check('metrics.energyEfficiency', 'Energy efficiency must be a number').isNumeric(),
    check('summary', 'Summary is required').not().isEmpty(),
    check('recommendations', 'Recommendations are required').not().isEmpty(),
  ],
  sustainabilityController.generateEnvironmentalImpactReport
);

router.get('/impact-reports', sustainabilityController.getEnvironmentalImpactReports);
router.get('/impact-reports/:id', sustainabilityController.getEnvironmentalImpactReport);

router.put(
  '/impact-reports/:id',
  [
    check('reportDate', 'Report date is required').not().isEmpty(),
    check('reportType', 'Report type is required').not().isEmpty(),
    check('metrics', 'Metrics are required').not().isEmpty(),
    check('metrics.waterUsage', 'Water usage must be a number').isNumeric(),
    check('metrics.soilHealth', 'Soil health must be a number').isNumeric(),
    check('metrics.biodiversity', 'Biodiversity must be a number').isNumeric(),
    check('metrics.wasteReduction', 'Waste reduction must be a number').isNumeric(),
    check('metrics.energyEfficiency', 'Energy efficiency must be a number').isNumeric(),
    check('summary', 'Summary is required').not().isEmpty(),
    check('recommendations', 'Recommendations are required').not().isEmpty(),
  ],
  sustainabilityController.updateEnvironmentalImpactReport
);

router.delete('/impact-reports/:id', sustainabilityController.deleteEnvironmentalImpactReport);

export default router;
