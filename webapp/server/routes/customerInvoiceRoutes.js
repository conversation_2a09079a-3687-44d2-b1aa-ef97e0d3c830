import express from 'express';
import {
  getCustomerInvoices,
  getCustomerInvoiceById,
  payInvoice,
  createPaymentIntent,
  getInvoiceQuestions,
  addInvoiceQuestion
} from '../controllers/customerInvoiceController.js';
import { authenticateCustomer } from '../middleware/customerAuthMiddleware.js';

const router = express.Router();

// All routes require customer authentication
router.use(authenticateCustomer);

// Get all invoices for the authenticated customer
router.get('/', getCustomerInvoices);

// Get a single invoice by ID for the authenticated customer
router.get('/:invoiceId', getCustomerInvoiceById);

// Pay an invoice
router.post('/:invoiceId/pay', payInvoice);

// Create a payment intent for Stripe
router.post('/:invoiceId/payment-intent', createPaymentIntent);

// Get all questions for an invoice
router.get('/:invoiceId/questions', getInvoiceQuestions);

// Add a question to an invoice
router.post('/:invoiceId/questions', addInvoiceQuestion);

export default router;
