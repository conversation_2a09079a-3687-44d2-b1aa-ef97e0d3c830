import express from 'express';
import matrixChatController from '../controllers/matrixChatController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all conversations for the current user
router.get('/conversations', matrixChatController.getUserConversations);

// Get a conversation by ID
router.get('/conversations/:id', matrixChatController.getConversation);

// Create a new conversation
router.post('/conversations', matrixChatController.createConversation);

// Get messages for a conversation
router.get('/conversations/:id/messages', matrixChatController.getMessages);

// Send a message
router.post('/conversations/:id/messages', matrixChatController.sendMessage);

// Get associated farms that the user can chat with
router.get('/associated-farms', matrixChatController.getChattableAssociatedFarms);

// Get Matrix users for a farm
router.get('/users', matrixChatController.getMatrixUsers);

export default router;
