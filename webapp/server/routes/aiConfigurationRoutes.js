import express from 'express';
import { body } from 'express-validator';
import { authenticate } from '../middleware/index.js';
import { 
  getProviders,
  getProviderById,
  createProvider,
  updateProvider,
  deleteProvider,
  getModels,
  getModelsByProvider,
  getModelById,
  createModel,
  updateModel,
  deleteModel,
  getConfigurations,
  getConfigurationById,
  createConfiguration,
  updateConfiguration,
  deleteConfiguration,
  getInstructions,
  getInstructionsByTaskType,
  getInstructionById,
  createInstruction,
  updateInstruction,
  deleteInstruction,
  testConfiguration
} from '../controllers/aiConfigurationController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @route GET /api/ai-configuration/providers
 * @desc Get all AI providers
 * @access Private
 */
router.get('/providers', getProviders);

/**
 * @route GET /api/ai-configuration/providers/:id
 * @desc Get an AI provider by ID
 * @access Private
 */
router.get('/providers/:id', getProviderById);

/**
 * @route POST /api/ai-configuration/providers
 * @desc Create a new AI provider
 * @access Private
 */
router.post(
  '/providers',
  [
    body('name').notEmpty().withMessage('Provider name is required'),
    body('description').optional(),
    body('api_base_url').optional(),
    body('auth_type').optional()
  ],
  createProvider
);

/**
 * @route PUT /api/ai-configuration/providers/:id
 * @desc Update an AI provider
 * @access Private
 */
router.put(
  '/providers/:id',
  [
    body('name').notEmpty().withMessage('Provider name is required'),
    body('description').optional(),
    body('api_base_url').optional(),
    body('auth_type').optional(),
    body('is_enabled').isBoolean().optional()
  ],
  updateProvider
);

/**
 * @route DELETE /api/ai-configuration/providers/:id
 * @desc Delete an AI provider
 * @access Private
 */
router.delete('/providers/:id', deleteProvider);

/**
 * @route GET /api/ai-configuration/models
 * @desc Get all AI models
 * @access Private
 */
router.get('/models', getModels);

/**
 * @route GET /api/ai-configuration/providers/:providerId/models
 * @desc Get AI models by provider ID
 * @access Private
 */
router.get('/providers/:providerId/models', getModelsByProvider);

/**
 * @route GET /api/ai-configuration/models/:id
 * @desc Get an AI model by ID
 * @access Private
 */
router.get('/models/:id', getModelById);

/**
 * @route POST /api/ai-configuration/models
 * @desc Create a new AI model
 * @access Private
 */
router.post(
  '/models',
  [
    body('provider_id').notEmpty().withMessage('Provider ID is required'),
    body('name').notEmpty().withMessage('Model name is required'),
    body('model_identifier').notEmpty().withMessage('Model identifier is required'),
    body('description').optional(),
    body('capabilities').optional().isArray(),
    body('max_tokens').optional().isNumeric()
  ],
  createModel
);

/**
 * @route PUT /api/ai-configuration/models/:id
 * @desc Update an AI model
 * @access Private
 */
router.put(
  '/models/:id',
  [
    body('name').notEmpty().withMessage('Model name is required'),
    body('model_identifier').notEmpty().withMessage('Model identifier is required'),
    body('description').optional(),
    body('capabilities').optional().isArray(),
    body('max_tokens').optional().isNumeric(),
    body('is_enabled').isBoolean().optional()
  ],
  updateModel
);

/**
 * @route DELETE /api/ai-configuration/models/:id
 * @desc Delete an AI model
 * @access Private
 */
router.delete('/models/:id', deleteModel);

/**
 * @route GET /api/ai-configuration/configurations
 * @desc Get all AI configurations
 * @access Private
 */
router.get('/configurations', getConfigurations);

/**
 * @route GET /api/ai-configuration/configurations/:id
 * @desc Get an AI configuration by ID
 * @access Private
 */
router.get('/configurations/:id', getConfigurationById);

/**
 * @route POST /api/ai-configuration/configurations
 * @desc Create a new AI configuration
 * @access Private
 */
router.post(
  '/configurations',
  [
    body('name').notEmpty().withMessage('Configuration name is required'),
    body('provider_id').notEmpty().withMessage('Provider ID is required'),
    body('description').optional(),
    body('default_model_id').optional(),
    body('api_key').optional(),
    body('additional_settings').optional(),
    body('is_global').optional().isBoolean()
  ],
  createConfiguration
);

/**
 * @route PUT /api/ai-configuration/configurations/:id
 * @desc Update an AI configuration
 * @access Private
 */
router.put(
  '/configurations/:id',
  [
    body('name').notEmpty().withMessage('Configuration name is required'),
    body('description').optional(),
    body('default_model_id').optional(),
    body('api_key').optional(),
    body('additional_settings').optional(),
    body('is_global').optional().isBoolean(),
    body('is_enabled').optional().isBoolean()
  ],
  updateConfiguration
);

/**
 * @route DELETE /api/ai-configuration/configurations/:id
 * @desc Delete an AI configuration
 * @access Private
 */
router.delete('/configurations/:id', deleteConfiguration);

/**
 * @route GET /api/ai-configuration/instructions
 * @desc Get all AI instructions
 * @access Private
 */
router.get('/instructions', getInstructions);

/**
 * @route GET /api/ai-configuration/instructions/task/:taskType
 * @desc Get AI instructions by task type
 * @access Private
 */
router.get('/instructions/task/:taskType', getInstructionsByTaskType);

/**
 * @route GET /api/ai-configuration/instructions/:id
 * @desc Get an AI instruction by ID
 * @access Private
 */
router.get('/instructions/:id', getInstructionById);

/**
 * @route POST /api/ai-configuration/instructions
 * @desc Create a new AI instruction
 * @access Private
 */
router.post(
  '/instructions',
  [
    body('name').notEmpty().withMessage('Instruction name is required'),
    body('task_type').notEmpty().withMessage('Task type is required'),
    body('instructions').notEmpty().withMessage('Instructions are required'),
    body('description').optional(),
    body('example_input').optional(),
    body('example_output').optional()
  ],
  createInstruction
);

/**
 * @route PUT /api/ai-configuration/instructions/:id
 * @desc Update an AI instruction
 * @access Private
 */
router.put(
  '/instructions/:id',
  [
    body('name').notEmpty().withMessage('Instruction name is required'),
    body('task_type').notEmpty().withMessage('Task type is required'),
    body('instructions').notEmpty().withMessage('Instructions are required'),
    body('description').optional(),
    body('example_input').optional(),
    body('example_output').optional(),
    body('is_enabled').isBoolean().optional()
  ],
  updateInstruction
);

/**
 * @route DELETE /api/ai-configuration/instructions/:id
 * @desc Delete an AI instruction
 * @access Private
 */
router.delete('/instructions/:id', deleteInstruction);

/**
 * @route POST /api/ai-configuration/test
 * @desc Test an AI configuration with a prompt
 * @access Private
 */
router.post(
  '/test',
  [
    body('configuration_id').notEmpty().withMessage('Configuration ID is required'),
    body('prompt').notEmpty().withMessage('Prompt is required'),
    body('model_id').optional()
  ],
  testConfiguration
);

export default router;
