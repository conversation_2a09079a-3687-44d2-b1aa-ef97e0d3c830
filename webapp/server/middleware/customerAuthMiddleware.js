import jwt from 'jsonwebtoken';
import Customer from '../models/Customer.js';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';

// Middleware to authenticate customer JWT tokens
export const authenticateCustomer = async (req, res, next) => {
  // Skip authentication for OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    return next();
  }

  try {
    // Get token from header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if it's a customer token
    if (decoded.type !== 'customer') {
      return res.status(403).json({ error: 'Invalid token type' });
    }

    // Find customer
    const customer = await Customer.findByPk(decoded.id);

    if (!customer) {
      return res.status(401).json({ error: 'Customer not found' });
    }

    // Check if customer has portal access
    if (!customer.portal_access) {
      return res.status(403).json({ error: 'Portal access not granted' });
    }

    // Add customer to request
    req.customer = customer;
    req.farmId = decoded.farmId;

    next();
  } catch (error) {
    // Handle token expiration without logging to console
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired', tokenExpired: true });
    }

    // Provide detailed error message for debugging
    const errorReason = error.message || error.name || 'Unknown error';
    console.error('Customer authentication error:', errorReason);

    return res.status(401).json({ 
      error: 'Authentication failed', 
      details: process.env.NODE_ENV === 'production' ? undefined : errorReason 
    });
  }
};

// Export protect as an alias for authenticateCustomer middleware
export const protectCustomer = authenticateCustomer;

// Middleware for optional customer authentication (for store subdomain that can be enhanced with customer data)
export const optionalAuthenticateCustomer = async (req, res, next) => {
  // Skip authentication for OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    return next();
  }

  try {
    // Get token from header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // If no token is found, continue without authentication
      req.customer = null;
      return next();
    }

    const token = authHeader.split(' ')[1];

    if (!token) {
      // If no token is found, continue without authentication
      req.customer = null;
      return next();
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if it's a customer token
    if (decoded.type !== 'customer') {
      // If not a customer token, continue without authentication
      req.customer = null;
      return next();
    }

    // Find customer
    const customer = await Customer.findByPk(decoded.id);

    // If customer is found, add to request
    if (customer) {
      req.customer = customer;
      req.farmId = decoded.farmId;
    } else {
      req.customer = null;
    }

    next();
  } catch (error) {
    // If authentication fails, continue without customer
    req.customer = null;
    next();
  }
};
