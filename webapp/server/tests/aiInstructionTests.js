// AI Instruction CRUD Operations Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

let createdInstructionId;

// Test data
const testInstruction = {
  name: 'Test Instruction',
  description: 'An instruction for testing purposes',
  task_type: 'crop_rotation_analysis',
  instructions: 'Analyze crop rotation patterns and provide recommendations for optimal crop sequences.',
  example_input: 'Field ID: 123, Previous crops: Corn (2020), Soybeans (2021), Wheat (2022)',
  example_output: 'Recommended next crop: Alfalfa. Reasons: Breaks pest cycles, adds nitrogen to soil.',
  is_enabled: true
};

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Test functions
const testGetInstructions = async () => {
  console.log('Testing GET /api/ai-configuration/instructions');
  const instructions = await makeRequest('GET', '/api/ai-configuration/instructions');
  assert(Array.isArray(instructions), 'Response should be an array');
  console.log(`Found ${instructions.length} instructions`);
  return instructions;
};

const testGetInstructionsByTaskType = async () => {
  console.log(`Testing GET /api/ai-configuration/instructions/task-type/${testInstruction.task_type}`);
  const instructions = await makeRequest('GET', `/api/ai-configuration/instructions/task-type/${testInstruction.task_type}`);
  assert(Array.isArray(instructions), 'Response should be an array');
  console.log(`Found ${instructions.length} instructions for task type ${testInstruction.task_type}`);
  return instructions;
};

const testCreateInstruction = async () => {
  console.log('Testing POST /api/ai-configuration/instructions');
  const instruction = await makeRequest('POST', '/api/ai-configuration/instructions', testInstruction);
  assert(instruction.id, 'Created instruction should have an ID');
  assert(instruction.name === testInstruction.name, 'Created instruction should have the correct name');
  assert(instruction.task_type === testInstruction.task_type, 'Created instruction should have the correct task type');
  console.log(`Created instruction with ID: ${instruction.id}`);
  createdInstructionId = instruction.id;
  return instruction;
};

const testGetInstructionById = async () => {
  console.log(`Testing GET /api/ai-configuration/instructions/${createdInstructionId}`);
  const instruction = await makeRequest('GET', `/api/ai-configuration/instructions/${createdInstructionId}`);
  assert(instruction.id === createdInstructionId, 'Instruction ID should match');
  assert(instruction.name === testInstruction.name, 'Instruction name should match');
  console.log(`Retrieved instruction: ${instruction.name}`);
  return instruction;
};

const testUpdateInstruction = async () => {
  console.log(`Testing PUT /api/ai-configuration/instructions/${createdInstructionId}`);
  const updatedData = {
    ...testInstruction,
    name: 'Updated Test Instruction',
    description: 'An updated instruction for testing purposes',
    instructions: 'Updated instructions for analyzing crop rotation patterns.'
  };
  const instruction = await makeRequest('PUT', `/api/ai-configuration/instructions/${createdInstructionId}`, updatedData);
  assert(instruction.id === createdInstructionId, 'Instruction ID should match');
  assert(instruction.name === updatedData.name, 'Instruction name should be updated');
  assert(instruction.description === updatedData.description, 'Instruction description should be updated');
  assert(instruction.instructions === updatedData.instructions, 'Instruction content should be updated');
  console.log(`Updated instruction: ${instruction.name}`);
  return instruction;
};

const testDeleteInstruction = async () => {
  console.log(`Testing DELETE /api/ai-configuration/instructions/${createdInstructionId}`);
  const result = await makeRequest('DELETE', `/api/ai-configuration/instructions/${createdInstructionId}`);
  assert(result.message, 'Response should have a message');
  console.log(`Deleted instruction: ${result.message}`);
  
  // Verify the instruction is deleted
  try {
    await makeRequest('GET', `/api/ai-configuration/instructions/${createdInstructionId}`);
    assert(false, 'Instruction should not exist after deletion');
  } catch (error) {
    assert(error.response.status === 404, 'Should get a 404 error');
    console.log('Verified instruction is deleted');
  }
  
  return result;
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Instruction CRUD tests...');
    
    // Get initial instructions
    await testGetInstructions();
    
    // Create a new instruction
    await testCreateInstruction();
    
    // Get the instruction by ID
    await testGetInstructionById();
    
    // Get instructions by task type
    await testGetInstructionsByTaskType();
    
    // Update the instruction
    await testUpdateInstruction();
    
    // Delete the instruction
    await testDeleteInstruction();
    
    // Verify instructions list again
    await testGetInstructions();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    
    // Try to clean up even if tests fail
    try {
      if (createdInstructionId) {
        await makeRequest('DELETE', `/api/ai-configuration/instructions/${createdInstructionId}`);
      }
    } catch (cleanupError) {
      console.error('Error during cleanup:', cleanupError);
    }
    
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testGetInstructions,
  testGetInstructionsByTaskType,
  testCreateInstruction,
  testGetInstructionById,
  testUpdateInstruction,
  testDeleteInstruction
};