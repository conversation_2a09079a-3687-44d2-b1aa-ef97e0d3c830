// AI Crop Rotation Analysis Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

// We need a farm ID and field ID for testing
let farmId;
let fieldId;
let createdAnalysisId;

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Setup: Get a farm and field for testing
const setupFarmAndField = async () => {
  console.log('Setting up a farm and field for testing');
  
  // Try to get existing farms
  const farms = await makeRequest('GET', '/api/farms');
  
  if (farms.length > 0) {
    // Use the first farm
    farmId = farms[0].id;
    console.log(`Using existing farm with ID: ${farmId}`);
    
    // Try to get fields for this farm
    const fields = await makeRequest('GET', `/api/fields/farm/${farmId}`);
    
    if (fields.length > 0) {
      // Use the first field
      fieldId = fields[0].id;
      console.log(`Using existing field with ID: ${fieldId}`);
      return;
    }
  }
  
  console.log('No suitable farm or field found for testing. Please create a farm and field first.');
  throw new Error('No suitable farm or field found for testing');
};

// Test functions
const testGenerateCropRotationAnalysis = async () => {
  console.log('Testing POST /api/ai-analysis/crop-rotation');
  
  const testData = {
    farmId: farmId,
    fieldId: fieldId,
    cropData: {
      currentCrop: 'Corn',
      previousCrops: [
        { crop_name: 'Soybeans', planting_date: '2022-04-15' },
        { crop_name: 'Wheat', planting_date: '2021-04-10' }
      ]
    }
  };
  
  const result = await makeRequest('POST', '/api/ai-analysis/crop-rotation', testData);
  
  assert(result.analysis, 'Response should have an analysis field');
  assert(result.analysis.id, 'Analysis should have an ID');
  assert(result.analysis.farm_id === farmId, 'Analysis should have the correct farm ID');
  assert(result.analysis.field_id === fieldId, 'Analysis should have the correct field ID');
  
  console.log(`Generated crop rotation analysis with ID: ${result.analysis.id}`);
  createdAnalysisId = result.analysis.id;
  
  return result.analysis;
};

const testGetLatestCropRotationAnalysis = async () => {
  console.log(`Testing GET /api/ai-analysis/crop-rotation/${farmId}`);
  
  const result = await makeRequest('GET', `/api/ai-analysis/crop-rotation/${farmId}?fieldId=${fieldId}`);
  
  assert(result.analysis, 'Response should have an analysis field');
  assert(result.analysis.farm_id === farmId, 'Analysis should have the correct farm ID');
  assert(result.analysis.field_id === fieldId, 'Analysis should have the correct field ID');
  
  console.log(`Retrieved latest crop rotation analysis for field: ${result.analysis.id}`);
  
  return result.analysis;
};

const testGetAllCropRotationAnalyses = async () => {
  console.log(`Testing GET /api/ai-analysis/crop-rotation/${farmId}/all`);
  
  const result = await makeRequest('GET', `/api/ai-analysis/crop-rotation/${farmId}/all`);
  
  assert(Array.isArray(result.analyses), 'Response should have an analyses array');
  assert(result.analyses.length > 0, 'There should be at least one analysis');
  
  console.log(`Retrieved ${result.analyses.length} crop rotation analyses for farm`);
  
  return result.analyses;
};

const testApplyCropRotationAnalysis = async () => {
  console.log(`Testing PUT /api/ai-analysis/crop-rotation/${createdAnalysisId}`);
  
  const result = await makeRequest('PUT', `/api/ai-analysis/crop-rotation/${createdAnalysisId}`, {
    isApplied: true
  });
  
  assert(result.analysis, 'Response should have an analysis field');
  assert(result.analysis.id === createdAnalysisId, 'Analysis ID should match');
  assert(result.analysis.is_applied === true, 'Analysis should be marked as applied');
  
  console.log(`Applied crop rotation analysis: ${result.analysis.id}`);
  
  return result.analysis;
};

const testRunCropRotationAnalysisForFarm = async () => {
  console.log(`Testing POST /api/ai-analysis/crop-rotation/${farmId}/run`);
  
  const result = await makeRequest('POST', `/api/ai-analysis/crop-rotation/${farmId}/run`);
  
  assert(result.message, 'Response should have a message field');
  assert(result.analyses, 'Response should have an analyses field');
  assert(Array.isArray(result.analyses), 'Analyses should be an array');
  
  console.log(`Run crop rotation analysis for farm: ${result.message}`);
  
  return result;
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Crop Rotation Analysis tests...');
    
    // Setup: Get a farm and field
    await setupFarmAndField();
    
    // Generate a crop rotation analysis
    await testGenerateCropRotationAnalysis();
    
    // Get the latest crop rotation analysis
    await testGetLatestCropRotationAnalysis();
    
    // Get all crop rotation analyses
    await testGetAllCropRotationAnalyses();
    
    // Apply the crop rotation analysis
    await testApplyCropRotationAnalysis();
    
    // Run crop rotation analysis for the farm
    await testRunCropRotationAnalysisForFarm();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testGenerateCropRotationAnalysis,
  testGetLatestCropRotationAnalysis,
  testGetAllCropRotationAnalyses,
  testApplyCropRotationAnalysis,
  testRunCropRotationAnalysisForFarm,
  setupFarmAndField
};