// AI Provider CRUD Operations Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

// Test data
const testProvider = {
  name: 'Test Provider',
  description: 'A provider for testing purposes',
  api_base_url: 'https://api.testprovider.com',
  auth_type: 'api_key'
};

let createdProviderId;

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Test functions
const testGetProviders = async () => {
  console.log('Testing GET /api/ai-configuration/providers');
  const providers = await makeRequest('GET', '/api/ai-configuration/providers');
  assert(Array.isArray(providers), 'Response should be an array');
  console.log(`Found ${providers.length} providers`);
  return providers;
};

const testCreateProvider = async () => {
  console.log('Testing POST /api/ai-configuration/providers');
  const provider = await makeRequest('POST', '/api/ai-configuration/providers', testProvider);
  assert(provider.id, 'Created provider should have an ID');
  assert(provider.name === testProvider.name, 'Created provider should have the correct name');
  console.log(`Created provider with ID: ${provider.id}`);
  createdProviderId = provider.id;
  return provider;
};

const testGetProviderById = async () => {
  console.log(`Testing GET /api/ai-configuration/providers/${createdProviderId}`);
  const provider = await makeRequest('GET', `/api/ai-configuration/providers/${createdProviderId}`);
  assert(provider.id === createdProviderId, 'Provider ID should match');
  assert(provider.name === testProvider.name, 'Provider name should match');
  console.log(`Retrieved provider: ${provider.name}`);
  return provider;
};

const testUpdateProvider = async () => {
  console.log(`Testing PUT /api/ai-configuration/providers/${createdProviderId}`);
  const updatedData = {
    ...testProvider,
    name: 'Updated Test Provider',
    description: 'An updated provider for testing purposes'
  };
  const provider = await makeRequest('PUT', `/api/ai-configuration/providers/${createdProviderId}`, updatedData);
  assert(provider.id === createdProviderId, 'Provider ID should match');
  assert(provider.name === updatedData.name, 'Provider name should be updated');
  assert(provider.description === updatedData.description, 'Provider description should be updated');
  console.log(`Updated provider: ${provider.name}`);
  return provider;
};

const testDeleteProvider = async () => {
  console.log(`Testing DELETE /api/ai-configuration/providers/${createdProviderId}`);
  const result = await makeRequest('DELETE', `/api/ai-configuration/providers/${createdProviderId}`);
  assert(result.message, 'Response should have a message');
  console.log(`Deleted provider: ${result.message}`);
  
  // Verify the provider is deleted
  try {
    await makeRequest('GET', `/api/ai-configuration/providers/${createdProviderId}`);
    assert(false, 'Provider should not exist after deletion');
  } catch (error) {
    assert(error.response.status === 404, 'Should get a 404 error');
    console.log('Verified provider is deleted');
  }
  
  return result;
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Provider CRUD tests...');
    
    // Get initial providers
    await testGetProviders();
    
    // Create a new provider
    await testCreateProvider();
    
    // Get the provider by ID
    await testGetProviderById();
    
    // Update the provider
    await testUpdateProvider();
    
    // Delete the provider
    await testDeleteProvider();
    
    // Verify providers list again
    await testGetProviders();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testGetProviders,
  testCreateProvider,
  testGetProviderById,
  testUpdateProvider,
  testDeleteProvider
};