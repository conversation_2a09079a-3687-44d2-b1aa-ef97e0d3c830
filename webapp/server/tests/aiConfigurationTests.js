// AI Configuration CRUD Operations Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

// We need a provider ID for testing configurations
let providerId;
let modelId;
let createdConfigurationId;

// Test data
const testConfiguration = {
  name: 'Test Configuration',
  description: 'A configuration for testing purposes',
  api_key: 'test-api-key-123456',
  additional_settings: {
    temperature: 0.7,
    top_p: 1.0,
    frequency_penalty: 0.0,
    presence_penalty: 0.0
  },
  is_global: true,
  is_enabled: true
};

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Setup: Get or create a provider and model for testing
const setupDependencies = async () => {
  console.log('Setting up dependencies for configuration tests');
  
  // Try to get existing providers
  const providers = await makeRequest('GET', '/api/ai-configuration/providers');
  
  if (providers.length > 0) {
    // Use the first provider
    providerId = providers[0].id;
    console.log(`Using existing provider with ID: ${providerId}`);
  } else {
    // Create a new provider
    const provider = await makeRequest('POST', '/api/ai-configuration/providers', {
      name: 'Test Provider for Configurations',
      description: 'A provider for testing configuration operations',
      api_base_url: 'https://api.testprovider.com',
      auth_type: 'api_key'
    });
    
    providerId = provider.id;
    console.log(`Created new provider with ID: ${providerId}`);
  }
  
  // Try to get existing models for this provider
  const models = await makeRequest('GET', `/api/ai-configuration/providers/${providerId}/models`);
  
  if (models.length > 0) {
    // Use the first model
    modelId = models[0].id;
    console.log(`Using existing model with ID: ${modelId}`);
  } else {
    // Create a new model
    const model = await makeRequest('POST', '/api/ai-configuration/models', {
      provider_id: providerId,
      name: 'Test Model for Configurations',
      model_identifier: 'test-model-config-v1',
      description: 'A model for testing configuration operations',
      capabilities: ['text-generation'],
      max_tokens: 4096,
      is_enabled: true
    });
    
    modelId = model.id;
    console.log(`Created new model with ID: ${modelId}`);
  }
  
  // Update test configuration with provider ID and model ID
  testConfiguration.provider_id = providerId;
  testConfiguration.default_model_id = modelId;
};

// Test functions
const testGetConfigurations = async () => {
  console.log('Testing GET /api/ai-configuration/configurations');
  const configurations = await makeRequest('GET', '/api/ai-configuration/configurations');
  assert(Array.isArray(configurations), 'Response should be an array');
  console.log(`Found ${configurations.length} configurations`);
  return configurations;
};

const testCreateConfiguration = async () => {
  console.log('Testing POST /api/ai-configuration/configurations');
  const configuration = await makeRequest('POST', '/api/ai-configuration/configurations', testConfiguration);
  assert(configuration.id, 'Created configuration should have an ID');
  assert(configuration.name === testConfiguration.name, 'Created configuration should have the correct name');
  assert(configuration.provider_id === providerId, 'Created configuration should have the correct provider ID');
  console.log(`Created configuration with ID: ${configuration.id}`);
  createdConfigurationId = configuration.id;
  return configuration;
};

const testGetConfigurationById = async () => {
  console.log(`Testing GET /api/ai-configuration/configurations/${createdConfigurationId}`);
  const configuration = await makeRequest('GET', `/api/ai-configuration/configurations/${createdConfigurationId}`);
  assert(configuration.id === createdConfigurationId, 'Configuration ID should match');
  assert(configuration.name === testConfiguration.name, 'Configuration name should match');
  console.log(`Retrieved configuration: ${configuration.name}`);
  return configuration;
};

const testUpdateConfiguration = async () => {
  console.log(`Testing PUT /api/ai-configuration/configurations/${createdConfigurationId}`);
  const updatedData = {
    ...testConfiguration,
    name: 'Updated Test Configuration',
    description: 'An updated configuration for testing purposes',
    additional_settings: {
      ...testConfiguration.additional_settings,
      temperature: 0.5
    }
  };
  const configuration = await makeRequest('PUT', `/api/ai-configuration/configurations/${createdConfigurationId}`, updatedData);
  assert(configuration.id === createdConfigurationId, 'Configuration ID should match');
  assert(configuration.name === updatedData.name, 'Configuration name should be updated');
  assert(configuration.description === updatedData.description, 'Configuration description should be updated');
  console.log(`Updated configuration: ${configuration.name}`);
  return configuration;
};

const testDeleteConfiguration = async () => {
  console.log(`Testing DELETE /api/ai-configuration/configurations/${createdConfigurationId}`);
  const result = await makeRequest('DELETE', `/api/ai-configuration/configurations/${createdConfigurationId}`);
  assert(result.message, 'Response should have a message');
  console.log(`Deleted configuration: ${result.message}`);
  
  // Verify the configuration is deleted
  try {
    await makeRequest('GET', `/api/ai-configuration/configurations/${createdConfigurationId}`);
    assert(false, 'Configuration should not exist after deletion');
  } catch (error) {
    assert(error.response.status === 404, 'Should get a 404 error');
    console.log('Verified configuration is deleted');
  }
  
  return result;
};

// Cleanup: Delete the test model and provider if we created them
const cleanupDependencies = async () => {
  try {
    // Only delete the model if we created it specifically for this test
    if (modelId) {
      const model = await makeRequest('GET', `/api/ai-configuration/models/${modelId}`);
      if (model.name === 'Test Model for Configurations') {
        console.log(`Cleaning up: Deleting test model with ID: ${modelId}`);
        await makeRequest('DELETE', `/api/ai-configuration/models/${modelId}`);
        console.log('Test model deleted');
      }
    }
    
    // Only delete the provider if we created it specifically for this test
    if (providerId) {
      const provider = await makeRequest('GET', `/api/ai-configuration/providers/${providerId}`);
      if (provider.name === 'Test Provider for Configurations') {
        console.log(`Cleaning up: Deleting test provider with ID: ${providerId}`);
        await makeRequest('DELETE', `/api/ai-configuration/providers/${providerId}`);
        console.log('Test provider deleted');
      }
    }
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Configuration CRUD tests...');
    
    // Setup: Get or create dependencies
    await setupDependencies();
    
    // Get initial configurations
    await testGetConfigurations();
    
    // Create a new configuration
    await testCreateConfiguration();
    
    // Get the configuration by ID
    await testGetConfigurationById();
    
    // Update the configuration
    await testUpdateConfiguration();
    
    // Delete the configuration
    await testDeleteConfiguration();
    
    // Verify configurations list again
    await testGetConfigurations();
    
    // Cleanup
    await cleanupDependencies();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    
    // Try to clean up even if tests fail
    try {
      if (createdConfigurationId) {
        await makeRequest('DELETE', `/api/ai-configuration/configurations/${createdConfigurationId}`);
      }
      await cleanupDependencies();
    } catch (cleanupError) {
      console.error('Error during cleanup:', cleanupError);
    }
    
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testGetConfigurations,
  testCreateConfiguration,
  testGetConfigurationById,
  testUpdateConfiguration,
  testDeleteConfiguration,
  setupDependencies,
  cleanupDependencies
};