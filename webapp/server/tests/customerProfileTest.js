import dotenv from 'dotenv';

dotenv.config();

// Mock data for testing
const mockFarms = {
  farm1: { id: 'farm-123', name: 'Test Farm 1' },
  farm2: { id: 'farm-456', name: 'Test Farm 2' }
};

const mockUsers = {
  user1: { id: 'user-123', email: '<EMAIL>', password: 'hashedPassword1', is_global_admin: false },
  user2: { id: 'user-456', email: '<EMAIL>', password: 'hashedPassword2', is_global_admin: false }
};

const mockCustomers = {
  customer1: {
    id: 'customer-123',
    user_id: mockUsers.user1.id,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    origin: 'marketplace',
    global_customer_id: 'global-customer-123'
  },
  customer2: {
    id: 'customer-456',
    user_id: mockUsers.user2.id,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    origin: 'farm',
    global_customer_id: 'global-customer-456'
  }
};

const mockAddresses = {
  address1: {
    id: 'address-123',
    customer_id: mockCustomers.customer1.id,
    farm_id: mockFarms.farm1.id,
    farm_alias: 'My Local Farm',
    address: '123 Main St',
    city: 'Farmville',
    state: 'CA',
    zip_code: '12345',
    country: 'USA',
    delivery_instructions: 'Leave at the front door',
    is_default: true
  },
  address2: {
    id: 'address-456',
    customer_id: mockCustomers.customer1.id,
    farm_id: mockFarms.farm2.id,
    farm_alias: 'Organic Farm',
    address: '456 Oak Ave',
    city: 'Farmville',
    state: 'CA',
    zip_code: '12345',
    country: 'USA',
    delivery_instructions: 'Call upon arrival',
    is_default: false
  },
  address3: {
    id: 'address-789',
    customer_id: mockCustomers.customer2.id,
    farm_id: mockFarms.farm1.id,
    farm_alias: 'Local Produce',
    address: '789 Pine St',
    city: 'Farmville',
    state: 'CA',
    zip_code: '12345',
    country: 'USA',
    delivery_instructions: null,
    is_default: true
  }
};

const mockOrders = {
  order1: {
    id: 'order-123',
    customer_id: mockCustomers.customer1.id,
    farm_id: mockFarms.farm1.id,
    status: 'delivered',
    created_at: '2023-01-01T12:00:00Z',
    items: [
      { product_id: 'product-123', quantity: 2, price: 10.99 }
    ]
  },
  order2: {
    id: 'order-456',
    customer_id: mockCustomers.customer1.id,
    farm_id: mockFarms.farm2.id,
    status: 'pending',
    created_at: '2023-02-01T12:00:00Z',
    items: [
      { product_id: 'product-456', quantity: 1, price: 5.99 }
    ]
  },
  order3: {
    id: 'order-789',
    customer_id: mockCustomers.customer2.id,
    farm_id: mockFarms.farm1.id,
    status: 'processing',
    created_at: '2023-03-01T12:00:00Z',
    items: [
      { product_id: 'product-789', quantity: 3, price: 7.99 }
    ]
  }
};

// Mock request objects
const createMockRequest = (user = null, body = {}, params = {}) => ({
  user,
  body,
  params
});

// Mock response object
const createMockResponse = () => {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    statusCode: 200,
    jsonData: null,
    cookieData: null
  };
  
  res.status.mockImplementation((code) => {
    res.statusCode = code;
    return res;
  });
  
  res.json.mockImplementation((data) => {
    res.jsonData = data;
    return res;
  });
  
  res.cookie.mockImplementation((name, value, options) => {
    res.cookieData = { name, value, options };
    return res;
  });
  
  return res;
};

// Test customer registration functionality
const testRegisterCustomer = () => {
  console.log('Testing customer registration functionality...\n');
  
  const testCases = [
    {
      name: 'Register new customer with valid data',
      body: {
        name: 'New Customer',
        email: '<EMAIL>',
        password: 'password123',
        phone: '************'
      },
      expectedStatus: 201,
      expectedResult: 'success'
    },
    {
      name: 'Registration fails with existing email',
      body: {
        name: 'Duplicate Email',
        email: mockCustomers.customer1.email,
        password: 'password123',
        phone: '************'
      },
      expectedStatus: 409,
      expectedResult: 'error'
    },
    {
      name: 'Registration fails with missing required fields',
      body: {
        name: 'Missing Fields',
        email: '<EMAIL>'
        // Missing password
      },
      expectedStatus: 400,
      expectedResult: 'error'
    },
    {
      name: 'Registration fails with invalid email format',
      body: {
        name: 'Invalid Email',
        email: 'not-an-email',
        password: 'password123',
        phone: '************'
      },
      expectedStatus: 400,
      expectedResult: 'error'
    },
    {
      name: 'Registration fails with password too short',
      body: {
        name: 'Short Password',
        email: '<EMAIL>',
        password: '123',
        phone: '************'
      },
      expectedStatus: 400,
      expectedResult: 'error'
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { body, expectedStatus, expectedResult } = testCase;
    
    // Simulate customer registration logic
    let actualStatus = 201;
    let actualResult = 'success';
    
    // Check if email already exists
    if (Object.values(mockCustomers).some(c => c.email === body.email)) {
      actualStatus = 409;
      actualResult = 'error';
    }
    
    // Check required fields
    else if (!body.name || !body.email || !body.password) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Validate email format
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.email)) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Validate password length
    else if (body.password.length < 6) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    const testPassed = actualStatus === expectedStatus && actualResult === expectedResult;
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Customer registration test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test customer login functionality
const testLoginCustomer = () => {
  console.log('\nTesting customer login functionality...\n');
  
  const testCases = [
    {
      name: 'Login with valid credentials',
      body: {
        email: mockCustomers.customer1.email,
        password: 'correctPassword'
      },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedToken: true
    },
    {
      name: 'Login fails with incorrect password',
      body: {
        email: mockCustomers.customer1.email,
        password: 'wrongPassword'
      },
      expectedStatus: 401,
      expectedResult: 'error',
      expectedToken: false
    },
    {
      name: 'Login fails with non-existent email',
      body: {
        email: '<EMAIL>',
        password: 'anyPassword'
      },
      expectedStatus: 401,
      expectedResult: 'error',
      expectedToken: false
    },
    {
      name: 'Login fails with missing email',
      body: {
        password: 'anyPassword'
      },
      expectedStatus: 400,
      expectedResult: 'error',
      expectedToken: false
    },
    {
      name: 'Login fails with missing password',
      body: {
        email: mockCustomers.customer1.email
      },
      expectedStatus: 400,
      expectedResult: 'error',
      expectedToken: false
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { body, expectedStatus, expectedResult, expectedToken } = testCase;
    
    // Simulate customer login logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualToken = false;
    
    // Check required fields
    if (!body.email || !body.password) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Find customer by email
    else {
      const customer = Object.values(mockCustomers).find(c => c.email === body.email);
      
      if (!customer) {
        actualStatus = 401;
        actualResult = 'error';
      }
      
      // Check password (simulated)
      else if (body.password !== 'correctPassword') {
        actualStatus = 401;
        actualResult = 'error';
      }
      
      else {
        actualToken = true;
      }
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       actualToken === expectedToken;
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, token ${expectedToken}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, token ${actualToken} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Customer login test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test get customer profile functionality
const testGetCustomerProfile = () => {
  console.log('\nTesting get customer profile functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user gets their profile',
      user: { id: mockUsers.user1.id },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedCustomerId: mockCustomers.customer1.id
    },
    {
      name: 'Unauthenticated request fails',
      user: null,
      expectedStatus: 401,
      expectedResult: 'error',
      expectedCustomerId: null
    },
    {
      name: 'User without customer profile gets 404',
      user: { id: 'user-without-profile' },
      expectedStatus: 404,
      expectedResult: 'error',
      expectedCustomerId: null
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, expectedStatus, expectedResult, expectedCustomerId } = testCase;
    
    // Simulate get customer profile logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualCustomerId = null;
    
    // Check authentication
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find customer by user ID
      const customer = Object.values(mockCustomers).find(c => c.user_id === user.id);
      
      if (!customer) {
        actualStatus = 404;
        actualResult = 'error';
      }
      
      else {
        actualCustomerId = customer.id;
      }
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedCustomerId === null || actualCustomerId === expectedCustomerId);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, customer ID ${expectedCustomerId || 'null'}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, customer ID ${actualCustomerId || 'null'} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Get customer profile test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test update customer profile functionality
const testUpdateCustomerProfile = () => {
  console.log('\nTesting update customer profile functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user updates their profile',
      user: { id: mockUsers.user1.id },
      body: {
        name: 'Updated Name',
        phone: '************'
      },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedName: 'Updated Name'
    },
    {
      name: 'Update fails with unauthenticated request',
      user: null,
      body: {
        name: 'Hacker'
      },
      expectedStatus: 401,
      expectedResult: 'error',
      expectedName: null
    },
    {
      name: 'Update fails for user without customer profile',
      user: { id: 'user-without-profile' },
      body: {
        name: 'New Profile'
      },
      expectedStatus: 404,
      expectedResult: 'error',
      expectedName: null
    },
    {
      name: 'Update with invalid email format fails',
      user: { id: mockUsers.user1.id },
      body: {
        email: 'not-an-email'
      },
      expectedStatus: 400,
      expectedResult: 'error',
      expectedName: null
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, body, expectedStatus, expectedResult, expectedName } = testCase;
    
    // Simulate update customer profile logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualName = null;
    
    // Check authentication
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find customer by user ID
      const customer = Object.values(mockCustomers).find(c => c.user_id === user.id);
      
      if (!customer) {
        actualStatus = 404;
        actualResult = 'error';
      }
      
      // Validate email format if provided
      else if (body.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.email)) {
        actualStatus = 400;
        actualResult = 'error';
      }
      
      else {
        // Update customer profile
        if (body.name) {
          customer.name = body.name;
        }
        if (body.email) {
          customer.email = body.email;
        }
        if (body.phone) {
          customer.phone = body.phone;
        }
        
        actualName = customer.name;
      }
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedName === null || actualName === expectedName);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, name ${expectedName || 'null'}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, name ${actualName || 'null'} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Update customer profile test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test add customer address functionality
const testAddCustomerAddress = () => {
  console.log('\nTesting add customer address functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user adds a new address',
      user: { id: mockUsers.user1.id },
      body: {
        farm_id: mockFarms.farm1.id,
        farm_alias: 'My New Farm',
        address: '101 New St',
        city: 'Farmville',
        state: 'CA',
        zip_code: '12345',
        country: 'USA',
        delivery_instructions: 'Ring doorbell',
        is_default: false
      },
      expectedStatus: 201,
      expectedResult: 'success'
    },
    {
      name: 'Add address fails with unauthenticated request',
      user: null,
      body: {
        farm_id: mockFarms.farm1.id,
        address: '101 Hack St',
        city: 'Hackville',
        state: 'CA',
        zip_code: '12345'
      },
      expectedStatus: 401,
      expectedResult: 'error'
    },
    {
      name: 'Add address fails for user without customer profile',
      user: { id: 'user-without-profile' },
      body: {
        farm_id: mockFarms.farm1.id,
        address: '101 Missing St',
        city: 'Missingville',
        state: 'CA',
        zip_code: '12345'
      },
      expectedStatus: 404,
      expectedResult: 'error'
    },
    {
      name: 'Add address fails with missing required fields',
      user: { id: mockUsers.user1.id },
      body: {
        farm_id: mockFarms.farm1.id,
        // Missing address
        city: 'Incomplete',
        state: 'CA',
        zip_code: '12345'
      },
      expectedStatus: 400,
      expectedResult: 'error'
    },
    {
      name: 'Add address fails with invalid farm ID',
      user: { id: mockUsers.user1.id },
      body: {
        farm_id: 'invalid-farm-id',
        address: '101 Invalid St',
        city: 'Invalidville',
        state: 'CA',
        zip_code: '12345'
      },
      expectedStatus: 404,
      expectedResult: 'error'
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, body, expectedStatus, expectedResult } = testCase;
    
    // Simulate add customer address logic
    let actualStatus = 201;
    let actualResult = 'success';
    
    // Check authentication
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find customer by user ID
      const customer = Object.values(mockCustomers).find(c => c.user_id === user.id);
      
      if (!customer) {
        actualStatus = 404;
        actualResult = 'error';
      }
      
      // Check required fields
      else if (!body.farm_id || !body.address || !body.city || !body.state || !body.zip_code) {
        actualStatus = 400;
        actualResult = 'error';
      }
      
      // Validate farm ID
      else if (!Object.values(mockFarms).some(f => f.id === body.farm_id)) {
        actualStatus = 404;
        actualResult = 'error';
      }
    }
    
    const testPassed = actualStatus === expectedStatus && actualResult === expectedResult;
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Add customer address test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test update customer address functionality
const testUpdateCustomerAddress = () => {
  console.log('\nTesting update customer address functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user updates their address',
      user: { id: mockUsers.user1.id },
      params: { addressId: mockAddresses.address1.id },
      body: {
        farm_alias: 'Updated Farm Name',
        delivery_instructions: 'Updated instructions'
      },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedFarmAlias: 'Updated Farm Name'
    },
    {
      name: 'Update address fails with unauthenticated request',
      user: null,
      params: { addressId: mockAddresses.address1.id },
      body: {
        farm_alias: 'Hacked Farm'
      },
      expectedStatus: 401,
      expectedResult: 'error',
      expectedFarmAlias: null
    },
    {
      name: 'Update address fails for non-existent address',
      user: { id: mockUsers.user1.id },
      params: { addressId: 'non-existent-address' },
      body: {
        farm_alias: 'Missing Farm'
      },
      expectedStatus: 404,
      expectedResult: 'error',
      expectedFarmAlias: null
    },
    {
      name: 'User cannot update another user\'s address',
      user: { id: mockUsers.user2.id },
      params: { addressId: mockAddresses.address1.id },
      body: {
        farm_alias: 'Stolen Farm'
      },
      expectedStatus: 403,
      expectedResult: 'error',
      expectedFarmAlias: null
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, params, body, expectedStatus, expectedResult, expectedFarmAlias } = testCase;
    
    // Simulate update customer address logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualFarmAlias = null;
    
    // Check authentication
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find address
      const address = Object.values(mockAddresses).find(a => a.id === params.addressId);
      
      if (!address) {
        actualStatus = 404;
        actualResult = 'error';
      }
      
      else {
        // Find customer by user ID
        const customer = Object.values(mockCustomers).find(c => c.user_id === user.id);
        
        // Check if address belongs to the customer
        if (!customer || address.customer_id !== customer.id) {
          actualStatus = 403;
          actualResult = 'error';
        }
        
        else {
          // Update address
          if (body.farm_alias) {
            address.farm_alias = body.farm_alias;
          }
          if (body.address) {
            address.address = body.address;
          }
          if (body.city) {
            address.city = body.city;
          }
          if (body.state) {
            address.state = body.state;
          }
          if (body.zip_code) {
            address.zip_code = body.zip_code;
          }
          if (body.country) {
            address.country = body.country;
          }
          if (body.delivery_instructions !== undefined) {
            address.delivery_instructions = body.delivery_instructions;
          }
          if (body.is_default !== undefined) {
            address.is_default = body.is_default;
          }
          
          actualFarmAlias = address.farm_alias;
        }
      }
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedFarmAlias === null || actualFarmAlias === expectedFarmAlias);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, farm alias ${expectedFarmAlias || 'null'}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, farm alias ${actualFarmAlias || 'null'} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Update customer address test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test delete customer address functionality
const testDeleteCustomerAddress = () => {
  console.log('\nTesting delete customer address functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user deletes their address',
      user: { id: mockUsers.user1.id },
      params: { addressId: mockAddresses.address1.id },
      expectedStatus: 200,
      expectedResult: 'success'
    },
    {
      name: 'Delete address fails with unauthenticated request',
      user: null,
      params: { addressId: mockAddresses.address1.id },
      expectedStatus: 401,
      expectedResult: 'error'
    },
    {
      name: 'Delete address fails for non-existent address',
      user: { id: mockUsers.user1.id },
      params: { addressId: 'non-existent-address' },
      expectedStatus: 404,
      expectedResult: 'error'
    },
    {
      name: 'User cannot delete another user\'s address',
      user: { id: mockUsers.user2.id },
      params: { addressId: mockAddresses.address1.id },
      expectedStatus: 403,
      expectedResult: 'error'
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, params, expectedStatus, expectedResult } = testCase;
    
    // Simulate delete customer address logic
    let actualStatus = 200;
    let actualResult = 'success';
    
    // Check authentication
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find address
      const address = Object.values(mockAddresses).find(a => a.id === params.addressId);
      
      if (!address) {
        actualStatus = 404;
        actualResult = 'error';
      }
      
      else {
        // Find customer by user ID
        const customer = Object.values(mockCustomers).find(c => c.user_id === user.id);
        
        // Check if address belongs to the customer
        if (!customer || address.customer_id !== customer.id) {
          actualStatus = 403;
          actualResult = 'error';
        }
      }
    }
    
    const testPassed = actualStatus === expectedStatus && actualResult === expectedResult;
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Delete customer address test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test get customer orders functionality
const testGetCustomerOrders = () => {
  console.log('\nTesting get customer orders functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user gets their orders',
      user: { id: mockUsers.user1.id },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedOrderCount: 2
    },
    {
      name: 'Get orders fails with unauthenticated request',
      user: null,
      expectedStatus: 401,
      expectedResult: 'error',
      expectedOrderCount: 0
    },
    {
      name: 'User without orders gets empty array',
      user: { id: 'user-without-orders' },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedOrderCount: 0
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, expectedStatus, expectedResult, expectedOrderCount } = testCase;
    
    // Simulate get customer orders logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualOrderCount = 0;
    
    // Check authentication
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find customer by user ID
      const customer = Object.values(mockCustomers).find(c => c.user_id === user.id);
      
      if (customer) {
        // Get orders for this customer
        const orders = Object.values(mockOrders).filter(o => o.customer_id === customer.id);
        actualOrderCount = orders.length;
      }
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       actualOrderCount === expectedOrderCount;
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, order count ${expectedOrderCount}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, order count ${actualOrderCount} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Get customer orders test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Run all tests
const runCustomerProfileTests = () => {
  console.log('Starting customer profile tests...\n');
  
  try {
    const registerPassed = testRegisterCustomer();
    const loginPassed = testLoginCustomer();
    const getProfilePassed = testGetCustomerProfile();
    const updateProfilePassed = testUpdateCustomerProfile();
    const addAddressPassed = testAddCustomerAddress();
    const updateAddressPassed = testUpdateCustomerAddress();
    const deleteAddressPassed = testDeleteCustomerAddress();
    const getOrdersPassed = testGetCustomerOrders();
    
    const allTestsPassed = registerPassed && 
                          loginPassed && 
                          getProfilePassed && 
                          updateProfilePassed && 
                          addAddressPassed && 
                          updateAddressPassed && 
                          deleteAddressPassed && 
                          getOrdersPassed;
    
    if (allTestsPassed) {
      console.log('\n🎉 All customer profile tests passed!');
      console.log('✅ Customer registration functionality works correctly');
      console.log('✅ Customer login functionality works correctly');
      console.log('✅ Get customer profile functionality works correctly');
      console.log('✅ Update customer profile functionality works correctly');
      console.log('✅ Add customer address functionality works correctly');
      console.log('✅ Update customer address functionality works correctly');
      console.log('✅ Delete customer address functionality works correctly');
      console.log('✅ Get customer orders functionality works correctly');
    } else {
      console.log('\n❌ Some customer profile tests failed. Please check the implementation.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Test execution failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (process.argv[1].endsWith('customerProfileTest.js')) {
  runCustomerProfileTests();
}

export { 
  testRegisterCustomer, 
  testLoginCustomer, 
  testGetCustomerProfile, 
  testUpdateCustomerProfile, 
  testAddCustomerAddress, 
  testUpdateCustomerAddress, 
  testDeleteCustomerAddress, 
  testGetCustomerOrders, 
  runCustomerProfileTests 
};