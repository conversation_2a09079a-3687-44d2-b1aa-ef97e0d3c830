import UserFarm from '../models/UserFarm.js';
import RolePermission from '../models/RolePermission.js';
import Invoice from '../models/Invoice.js';
import InvoiceAuditService from './invoiceAuditService.js';

/**
 * Service for managing invoice-specific permissions
 */
class InvoicePermissionService {
  /**
   * Check if user has permission to perform an action on an invoice
   * @param {Object} params - Permission check parameters
   * @param {string} params.userId - User ID
   * @param {string} params.farmId - User's current farm ID
   * @param {string} params.invoiceId - Invoice ID
   * @param {string} params.action - Action to check (view, edit, delete, etc.)
   * @param {Object} params.req - Express request object (for audit logging)
   * @param {boolean} params.isGlobalAdmin - Whether user is global admin
   * @returns {Promise<Object>} Permission result
   */
  static async checkInvoicePermission({
    userId,
    farmId,
    invoiceId,
    action,
    req = null,
    isGlobalAdmin = false
  }) {
    try {
      // Global admins have all permissions
      if (isGlobalAdmin) {
        await InvoiceAuditService.logPermissionCheck(
          invoiceId,
          userId,
          farmId,
          req,
          { action, result: 'allowed', reason: 'global_admin' }
        );
        return {
          allowed: true,
          reason: 'global_admin',
          metadata: { canEdit: true, isReceivedInvoice: false }
        };
      }

      // Get the invoice
      const invoice = await Invoice.findByPk(invoiceId);
      if (!invoice) {
        return {
          allowed: false,
          reason: 'invoice_not_found',
          error: 'Invoice not found'
        };
      }

      // Check basic farm relationship
      const isOwnInvoice = invoice.farm_id === farmId;
      const isReceivedInvoice = invoice.recipient_farm_id === farmId;
      const hasRelationship = isOwnInvoice || isReceivedInvoice;

      if (!hasRelationship) {
        await InvoiceAuditService.logAccessDenied(
          invoiceId,
          userId,
          farmId,
          req,
          { action, reason: 'no_relationship' }
        );
        return {
          allowed: false,
          reason: 'no_relationship',
          error: 'You can only access invoices that are sent by or received by your farm.'
        };
      }

      // Get user's role and permissions
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: userId,
          farm_id: farmId
        }
      });

      if (!userFarm) {
        await InvoiceAuditService.logAccessDenied(
          invoiceId,
          userId,
          farmId,
          req,
          { action, reason: 'no_farm_access' }
        );
        return {
          allowed: false,
          reason: 'no_farm_access',
          error: 'You do not have access to this farm.'
        };
      }

      // Check if user is approved
      if (userFarm.role === 'farm_employee' && !userFarm.is_approved) {
        await InvoiceAuditService.logAccessDenied(
          invoiceId,
          userId,
          farmId,
          req,
          { action, reason: 'pending_approval' }
        );
        return {
          allowed: false,
          reason: 'pending_approval',
          error: 'Your account is pending approval by the farm owner or admin.'
        };
      }

      // Check action-specific permissions
      const permissionResult = await this.checkActionPermission({
        action,
        userFarm,
        invoice,
        isOwnInvoice,
        isReceivedInvoice,
        farmId
      });

      // Log the permission check
      await InvoiceAuditService.logPermissionCheck(
        invoiceId,
        userId,
        farmId,
        req,
        {
          action,
          result: permissionResult.allowed ? 'allowed' : 'denied',
          reason: permissionResult.reason,
          role: userFarm.role,
          is_own_invoice: isOwnInvoice,
          is_received_invoice: isReceivedInvoice
        }
      );

      return {
        ...permissionResult,
        metadata: {
          canEdit: isOwnInvoice && permissionResult.allowed,
          isReceivedInvoice,
          isOwnInvoice,
          userRole: userFarm.role
        }
      };

    } catch (error) {
      console.error('Error checking invoice permission:', error);
      return {
        allowed: false,
        reason: 'system_error',
        error: 'Permission check failed'
      };
    }
  }

  /**
   * Check permission for specific action
   * @param {Object} params - Action permission parameters
   * @returns {Promise<Object>} Permission result
   */
  static async checkActionPermission({
    action,
    userFarm,
    invoice,
    isOwnInvoice,
    isReceivedInvoice,
    farmId
  }) {
    // Define action categories
    const readOnlyActions = ['view', 'download', 'export'];
    const editActions = ['edit', 'update', 'delete', 'cancel', 'send', 'upload_document', 'delete_document'];
    const paymentActions = ['pay', 'create_payment'];
    const disputeActions = ['dispute', 'resolve_dispute'];

    // Check if action is allowed based on invoice relationship
    if (readOnlyActions.includes(action)) {
      // For draft and cancelled invoices, only the creating farm can view them
      if ((invoice.status.toLowerCase() === 'draft' || invoice.status.toLowerCase() === 'cancelled') && !isOwnInvoice) {
        return {
          allowed: false,
          reason: 'status_visibility_restriction',
          error: `You cannot view invoices in ${invoice.status} status until they are sent to you.`
        };
      }

      // Both own and received invoices can be viewed (if not draft or cancelled for received invoices)
      return { allowed: true, reason: 'view_permission' };
    }

    if (editActions.includes(action)) {
      // Only own invoices can be edited
      if (!isOwnInvoice) {
        return {
          allowed: false,
          reason: 'received_invoice_readonly',
          error: `You cannot ${action} invoices received from other farms. Only the sending farm can ${action} this invoice.`
        };
      }

      // Check role-based permissions for editing
      return await this.checkRolePermission(userFarm, 'invoices', 'edit', farmId);
    }

    if (paymentActions.includes(action)) {
      // Only received invoices can be paid
      if (!isReceivedInvoice) {
        return {
          allowed: false,
          reason: 'cannot_pay_own_invoice',
          error: 'You cannot pay invoices sent by your own farm.'
        };
      }

      // Check role-based permissions for payment
      return await this.checkRolePermission(userFarm, 'invoices', 'pay', farmId);
    }

    if (disputeActions.includes(action)) {
      // Both parties can dispute, but with different permissions
      if (action === 'dispute' && isReceivedInvoice) {
        return await this.checkRolePermission(userFarm, 'invoices', 'dispute', farmId);
      }
      if (action === 'resolve_dispute' && isOwnInvoice) {
        return await this.checkRolePermission(userFarm, 'invoices', 'resolve_dispute', farmId);
      }

      return {
        allowed: false,
        reason: 'invalid_dispute_action',
        error: 'Invalid dispute action for this invoice relationship.'
      };
    }

    // Check invoice status restrictions
    return this.checkStatusRestrictions(action, invoice);
  }

  /**
   * Check role-based permissions
   * @param {Object} userFarm - UserFarm object
   * @param {string} feature - Feature name
   * @param {string} permission - Permission type
   * @param {string} farmId - Farm ID
   * @returns {Promise<Object>} Permission result
   */
  static async checkRolePermission(userFarm, feature, permission, farmId) {
    try {
      // Check custom permissions first
      if (userFarm.permissions && 
          userFarm.permissions[feature] && 
          userFarm.permissions[feature][permission]) {
        return { allowed: true, reason: 'custom_permission' };
      }

      // Check role-based permissions
      const rolePermission = await RolePermission.findOne({
        where: {
          farm_id: farmId,
          role_id: userFarm.role_id,
          feature: feature
        }
      });

      if (rolePermission && rolePermission[`can_${permission}`]) {
        return { allowed: true, reason: 'role_permission' };
      }

      // Default permissions based on role
      const defaultPermissions = this.getDefaultRolePermissions(userFarm.role, feature, permission);
      if (defaultPermissions.allowed) {
        return defaultPermissions;
      }

      return {
        allowed: false,
        reason: 'insufficient_permissions',
        error: `You do not have ${permission} permission for ${feature}.`
      };

    } catch (error) {
      console.error('Error checking role permission:', error);
      return {
        allowed: false,
        reason: 'permission_check_error',
        error: 'Permission check failed.'
      };
    }
  }

  /**
   * Get default role permissions
   * @param {string} role - User role
   * @param {string} feature - Feature name
   * @param {string} permission - Permission type
   * @returns {Object} Permission result
   */
  static getDefaultRolePermissions(role, feature, permission) {
    const defaultPermissions = {
      farm_owner: {
        invoices: ['view', 'edit', 'delete', 'pay', 'dispute', 'resolve_dispute']
      },
      farm_admin: {
        invoices: ['view', 'edit', 'delete', 'pay', 'dispute', 'resolve_dispute']
      },
      farm_manager: {
        invoices: ['view', 'edit', 'pay', 'dispute']
      },
      accountant: {
        invoices: ['view', 'edit', 'pay']
      },
      farm_employee: {
        invoices: ['view']
      }
    };

    const rolePerms = defaultPermissions[role];
    if (rolePerms && rolePerms[feature] && rolePerms[feature].includes(permission)) {
      return { allowed: true, reason: 'default_role_permission' };
    }

    return {
      allowed: false,
      reason: 'default_permission_denied',
      error: `Role ${role} does not have ${permission} permission for ${feature}.`
    };
  }

  /**
   * Check status-based restrictions
   * @param {string} action - Action to check
   * @param {Object} invoice - Invoice object
   * @returns {Object} Permission result
   */
  static checkStatusRestrictions(action, invoice) {
    const status = invoice.status;

    // Define status restrictions
    const restrictions = {
      paid: {
        disallowed: ['edit', 'delete', 'cancel', 'pay'],
        message: 'This action is not allowed on paid invoices.'
      },
      cancelled: {
        disallowed: ['edit', 'delete', 'cancel', 'pay', 'send'],
        message: 'This action is not allowed on cancelled invoices.'
      },
      draft: {
        disallowed: ['pay'],
        message: 'Draft invoices cannot be paid until they are sent.'
      }
    };

    const restriction = restrictions[status];
    if (restriction && restriction.disallowed.includes(action)) {
      return {
        allowed: false,
        reason: 'status_restriction',
        error: restriction.message
      };
    }

    return { allowed: true, reason: 'status_check_passed' };
  }

  /**
   * Middleware function for checking invoice permissions
   * @param {string} action - Required action
   * @returns {Function} Express middleware function
   */
  static requireInvoicePermission(action) {
    return async (req, res, next) => {
      try {
        const userId = req.user?.id;
        const farmId = req.farmId || req.user?.activeFarmId;
        const invoiceId = req.params.invoiceId;
        const isGlobalAdmin = req.user?.is_global_admin || false;

        if (!userId || !farmId || !invoiceId) {
          return res.status(400).json({
            error: 'Missing required parameters for permission check'
          });
        }

        const permissionResult = await this.checkInvoicePermission({
          userId,
          farmId,
          invoiceId,
          action,
          req,
          isGlobalAdmin
        });

        if (!permissionResult.allowed) {
          await InvoiceAuditService.logAccessDenied(
            invoiceId,
            userId,
            farmId,
            req,
            { action, reason: permissionResult.reason }
          );

          return res.status(403).json({
            error: permissionResult.error || 'Permission denied',
            reason: permissionResult.reason
          });
        }

        // Add permission metadata to request
        req.invoicePermission = permissionResult;
        next();

      } catch (error) {
        console.error('Invoice permission middleware error:', error);
        return res.status(500).json({
          error: 'Permission check failed'
        });
      }
    };
  }
}

export default InvoicePermissionService;
