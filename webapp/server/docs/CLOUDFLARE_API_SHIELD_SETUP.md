# Cloudflare API Shield Setup Guide

This document provides instructions on how to upload the NxtAcre API schema to Cloudflare for API protection using Cloudflare API Shield.

## What is Cloudflare API Shield?

Cloudflare API Shield is a security product that helps protect your API endpoints from attacks, abuse, and misuse. It provides:

- Schema validation
- Rate limiting
- Bot protection
- DDoS protection
- Positive security model enforcement

## Prerequisites

1. A Cloudflare account with access to the domain's configuration
2. Enterprise plan or API Shield add-on
3. The OpenAPI schema file (`cloudflare-api-schema.yaml`)

## Accessing the API Schema

The API schema is available through the following methods:

1. **Direct API Endpoint**: The schema is accessible at `https://api.nxtacre.com/api/schema` or `https://app.nxtacre.com/api/schema`
2. **File Location**: The schema file is located at `webapp/server/docs/cloudflare-api-schema.yaml` in the project repository

## Setup Instructions

### 1. Log in to Cloudflare Dashboard

Go to the [Cloudflare Dashboard](https://dash.cloudflare.com/) and log in with your credentials.

### 2. Select Your Domain

From the list of domains, select the domain that hosts your NxtAcre API (e.g., `nxtacre.com`).

### 3. Navigate to API Shield

1. In the left sidebar, click on "Security"
2. Select "API Shield"

### 4. Upload the Schema

1. Click on "Create API Shield"
2. Select "Upload Schema"
3. You have two options for uploading the schema:
   - **Option 1**: Choose the OpenAPI schema file (`cloudflare-api-schema.yaml`) from your local machine
   - **Option 2**: Provide the URL to the schema endpoint: `https://api.nxtacre.com/api/schema` or `https://app.nxtacre.com/api/schema`
4. Click "Upload"

### 5. Configure API Shield Settings

After uploading the schema, you'll be prompted to configure various settings:

1. **Endpoint Enforcement**: Choose which endpoints to protect
   - Recommended: Start with monitoring mode before enforcing
   - Enable enforcement for all endpoints or select specific ones

2. **Schema Validation**: Enable schema validation to ensure requests match the defined schema
   - Set validation action (Log, Block, etc.)

3. **Rate Limiting**: Configure rate limits for your API endpoints
   - Set limits based on client IP, tokens, or other identifiers
   - Define the rate limit threshold and time window

4. **Bot Protection**: Enable bot protection for your API
   - Configure challenge settings
   - Set up bot management rules

### 6. Deploy Configuration

After configuring the settings, click "Deploy" to activate API Shield protection.

### 7. Monitor and Adjust

1. Monitor the API Shield logs to ensure legitimate traffic is not being blocked
2. Adjust settings as needed based on observed traffic patterns
3. Gradually increase enforcement levels as confidence in the configuration grows

## Updating the Schema

When the API changes, follow these steps to update the schema:

1. Update the `cloudflare-api-schema.yaml` file with the new endpoints or changes
2. Log in to the Cloudflare Dashboard
3. Navigate to API Shield
4. Select "Update Schema"
5. You have two options for uploading the updated schema:
   - **Option 1**: Upload the updated schema file from your local machine
   - **Option 2**: Provide the URL to the schema endpoint (`https://api.nxtacre.com/api/schema`) to fetch the latest version
6. Review and deploy the changes

Note: The schema endpoint always serves the latest version of the schema file, so using Option 2 ensures you're always using the most up-to-date schema.

## Best Practices

1. **Start in Monitoring Mode**: Begin with logging-only mode to understand traffic patterns before enforcing blocks
2. **Incremental Deployment**: Roll out protection to less critical endpoints first
3. **Regular Updates**: Keep the schema up-to-date as your API evolves
4. **Test Before Deployment**: Validate schema changes before uploading to Cloudflare
5. **Monitor Logs**: Regularly review API Shield logs to identify potential issues

## Troubleshooting

If legitimate requests are being blocked:

1. Check the schema definition for accuracy
2. Review the API Shield logs to understand why requests are being blocked
3. Adjust validation rules or temporarily disable enforcement for specific endpoints
4. Contact Cloudflare support if issues persist

## Additional Resources

- [Cloudflare API Shield Documentation](https://developers.cloudflare.com/api-shield/)
- [OpenAPI Specification](https://swagger.io/specification/)
- [Cloudflare Support](https://support.cloudflare.com/)
