<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Delivery Completed</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4CAF50;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .order-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }
    .order-item:last-child {
      border-bottom: none;
    }
    .total {
      margin-top: 15px;
      font-weight: bold;
      text-align: right;
    }
    .success-icon {
      text-align: center;
      margin: 20px 0;
      font-size: 48px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Delivery Completed</h1>
    </div>
    <div class="content">
      <p>Hello {{customerName}},</p>
      
      <p>Great news! Your order from <strong>{{farmName}}</strong> has been successfully delivered.</p>
      
      <div class="success-icon">
        ✅
      </div>
      
      <p><strong>Order Details:</strong></p>
      <div class="order-details">
        <p><strong>Order ID:</strong> {{orderRequestId}}</p>
        <p><strong>Delivery Date:</strong> {{deliveryDate}}</p>
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        
        <p><strong>Items:</strong></p>
        {{#each items}}
        <div class="order-item">
          <p>{{quantity}} x {{productName}}</p>
        </div>
        {{/each}}
      </div>
      
      <p>We hope you're satisfied with your purchase. If you have any issues with your order, please contact {{farmName}} directly.</p>
      
      <p>You can view your order details by clicking the button below:</p>
      
      <a href="{{orderUrl}}" class="button">View Order Details</a>
      
      <p>Thank you for supporting local farms!</p>
      
      <p>If you have a moment, we'd love to hear your feedback about your shopping experience:</p>
      
      <a href="{{feedbackUrl}}" class="button" style="background-color: #3498db;">Share Your Feedback</a>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>