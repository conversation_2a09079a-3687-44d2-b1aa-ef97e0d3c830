<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Order Request Confirmation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4CAF50;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .order-item {
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }
    .order-item:last-child {
      border-bottom: none;
    }
    .total {
      margin-top: 15px;
      font-weight: bold;
      text-align: right;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Order Request Confirmation</h1>
    </div>
    <div class="content">
      <p>Hello {{customerName}},</p>
      
      <p>Thank you for your order request from <strong>{{farmName}}</strong>. We've received your request and it's being processed.</p>
      
      <p><strong>Order Request Details:</strong></p>
      <div class="order-details">
        <p><strong>Order Request ID:</strong> {{orderRequestId}}</p>
        <p><strong>Date Submitted:</strong> {{orderDate}}</p>
        <p><strong>Fulfillment Method:</strong> {{fulfillmentMethod}}</p>
        
        {{#if isDelivery}}
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        {{/if}}
        
        {{#if isPickup}}
        <p><strong>Pickup Information:</strong><br>
          Location: {{pickupLocation}}<br>
          Date: {{pickupDate}}
        </p>
        {{/if}}
        
        <p><strong>Items:</strong></p>
        {{#each items}}
        <div class="order-item">
          <p>{{quantity}} x {{productName}} - ${{price}} each</p>
        </div>
        {{/each}}
        
        <div class="total">
          <p>Subtotal: ${{subtotal}}</p>
          {{#if deliveryFee}}
          <p>Delivery Fee: ${{deliveryFee}}</p>
          {{/if}}
          <p>Total: ${{total}}</p>
        </div>
      </div>
      
      <p>The farm will review your order request and confirm availability. You'll receive another email when your order is approved or if there are any issues.</p>
      
      <p>You can track the status of your order request by clicking the button below:</p>
      
      <a href="{{orderUrl}}" class="button">View Order Request</a>
      
      <p>If you have any questions about your order, please contact {{farmName}} directly.</p>
      
      <p>Thank you for supporting local farms!</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>