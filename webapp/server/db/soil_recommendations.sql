-- Add soil_recommendations table to allow users to specify specific soil needs

-- Set the search path to the site schema
SET search_path TO site;

-- Enable the uuid-ossp extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE TABLE soil_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    soil_sample_id UUID REFERENCES soil_samples(id) ON DELETE CASCADE,
    field_id UUID,  -- Will reference fields table once created
    nutrient_type VARCHAR(50) NOT NULL, -- nitrogen, phosphorus, potassium, etc.
    recommended_amount DECIMAL(10, 2) NOT NULL,
    unit VARCHAR(50) NOT NULL, -- lbs/acre, tons/acre, kg/hectare, etc.
    application_method VARCHAR(100),
    application_timing VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add index for faster lookups
CREATE INDEX idx_soil_recommendations_sample_id ON soil_recommendations(soil_sample_id);
CREATE INDEX idx_soil_recommendations_field_id ON soil_recommendations(field_id);
