# Trial Subscription Columns

## Overview
This document describes the schema changes made to support trial subscription plans in the NxtAcre Farm Management Platform.

## Changes
Two columns were added to the `subscription_plans` table:

1. `is_trial` (BOOLEAN, DEFAULT false)
   - Indicates whether this plan is a trial subscription plan
   - Used to identify plans that are offered as trial subscriptions

2. `is_default` (BOOLEAN, DEFA<PERSON>LT false)
   - Indicates whether this plan is the default trial subscription plan
   - Used to determine which trial plan should be assigned to new users by default

## Migration Files
- `webapp/server/db/migrations/add_trial_columns_to_subscription_plans.sql` - Adds the trial columns to the subscription_plans table

## Model Updates
- `webapp/server/db/model_updates/add_missing_columns_to_subscription_plans.sql` - Contains the same changes for direct application

## Purpose
These schema changes enable the platform to:
1. Offer trial subscription plans to new users
2. Designate a default trial plan for automatic assignment
3. Distinguish between trial plans and regular subscription plans

## Usage
When creating or updating subscription plans, you can now set:
- `is_trial = true` to mark a plan as a trial subscription
- `is_default = true` to mark a plan as the default trial plan (only one plan should have this set to true)

The application will use these flags to determine which plans to offer during the trial period and which plan to assign by default.