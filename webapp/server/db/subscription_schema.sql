-- Subscription and Multitenancy Schema Extensions for Farm Books

-- Set the search path to the site schema
SET search_path TO site;

-- Enable the uuid-ossp extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Subscription Plans Table
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10, 2) NOT NULL,
    price_yearly DECIMAL(10, 2) NOT NULL,
    features JSONB NOT NULL, -- Store features as JSON
    max_farms INTEGER NOT NULL, -- Maximum number of farms allowed
    max_users INTEGER NOT NULL, -- Maximum number of users allowed
    storage_quota_gb INTEGER NOT NULL DEFAULT 5, -- Storage quota in GB
    max_file_size_mb INTEGER NOT NULL DEFAULT 25, -- Maximum file size in MB
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_subscription_plans_timestamp BEFORE UPDATE ON subscription_plans
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Tenants/Organizations Table
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    subscription_status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, past_due, canceled, etc.
    subscription_start_date DATE,
    subscription_end_date DATE,
    billing_email VARCHAR(255),
    billing_address VARCHAR(255),
    billing_city VARCHAR(100),
    billing_state VARCHAR(50),
    billing_zip_code VARCHAR(20),
    billing_country VARCHAR(100) DEFAULT 'USA',
    payment_method_id VARCHAR(255), -- For storing payment method reference
    stripe_customer_id VARCHAR(255), -- For Stripe integration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_tenants_timestamp BEFORE UPDATE ON tenants
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Modify Users Table to include tenant_id
ALTER TABLE users ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);

-- Modify Farms Table to include tenant_id
ALTER TABLE farms ADD COLUMN tenant_id UUID REFERENCES tenants(id);
CREATE INDEX idx_farms_tenant_id ON farms(tenant_id);

-- Tenant Admins Table (users who can manage tenant settings)
CREATE TABLE tenant_admins (
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'admin', -- admin, billing_admin, etc.
    PRIMARY KEY (tenant_id, user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_tenant_admins_timestamp BEFORE UPDATE ON tenant_admins
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Subscription Transactions Table (for billing history)
CREATE TABLE subscription_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) NOT NULL, -- succeeded, failed, pending
    payment_method VARCHAR(50), -- credit_card, bank_transfer, etc.
    payment_reference VARCHAR(255), -- Reference ID from payment processor
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    billing_period_start DATE,
    billing_period_end DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_subscription_transactions_timestamp BEFORE UPDATE ON subscription_transactions
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Feature Usage Tracking Table
CREATE TABLE feature_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    feature_name VARCHAR(100) NOT NULL,
    usage_count INTEGER DEFAULT 0,
    usage_limit INTEGER,
    reset_date DATE, -- For features with usage limits that reset periodically
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at timestamp
CREATE TRIGGER update_feature_usage_timestamp BEFORE UPDATE ON feature_usage
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price_monthly, price_yearly, features, max_farms, max_users, storage_quota_gb, max_file_size_mb)
VALUES 
('Basic', 'Basic farm management features for small farms', 9.99, 99.99, 
 '{"financial_management": true, "inventory_management": true, "field_management": true, "equipment_tracking": true, "advanced_reporting": false, "api_access": false}',
 1, 2, 5, 25),

('Standard', 'Standard features for medium-sized farms', 19.99, 199.99, 
 '{"financial_management": true, "inventory_management": true, "field_management": true, "equipment_tracking": true, "advanced_reporting": true, "api_access": false}',
 3, 5, 20, 50),

('Premium', 'Premium features for large farms with advanced needs', 39.99, 399.99, 
 '{"financial_management": true, "inventory_management": true, "field_management": true, "equipment_tracking": true, "advanced_reporting": true, "api_access": true}',
 10, 15, 100, 100),

('Enterprise', 'Enterprise-level features for large agricultural operations', 99.99, 999.99, 
 '{"financial_management": true, "inventory_management": true, "field_management": true, "equipment_tracking": true, "advanced_reporting": true, "api_access": true, "priority_support": true, "custom_integrations": true}',
 999999, 999999, 1000, 500);
