-- Set the search path to the appropriate schema
SET search_path TO site;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add role_id column to user_farms table
ALTER TABLE user_farms ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES roles(id);

-- Update existing user_farms records to set role_id based on role
-- First, update farm_owner roles
UPDATE user_farms
SET role_id = (SELECT id FROM roles WHERE name = 'farm_owner' AND farm_id IS NULL)
WHERE role = 'farm_owner' AND role_id IS NULL;

-- Update farm_admin roles
UPDATE user_farms
SET role_id = (SELECT id FROM roles WHERE name = 'farm_admin' AND farm_id IS NULL)
WHERE role = 'farm_admin' AND role_id IS NULL;

-- Update farm_manager roles
UPDATE user_farms
SET role_id = (SELECT id FROM roles WHERE name = 'farm_manager' AND farm_id IS NULL)
WHERE role = 'farm_manager' AND role_id IS NULL;

-- Update farm_employee roles
UPDATE user_farms
SET role_id = (SELECT id FROM roles WHERE name = 'farm_employee' AND farm_id IS NULL)
WHERE role = 'farm_employee' AND role_id IS NULL;

-- Update accountant roles
UPDATE user_farms
SET role_id = (SELECT id FROM roles WHERE name = 'accountant' AND farm_id IS NULL)
WHERE role = 'accountant' AND role_id IS NULL;

-- Set default for any remaining NULL role_id values to farm_employee
UPDATE user_farms
SET role_id = (SELECT id FROM roles WHERE name = 'farm_employee' AND farm_id IS NULL)
WHERE role_id IS NULL;

-- Now make role_id NOT NULL after all existing records have been updated
ALTER TABLE user_farms ALTER COLUMN role_id SET NOT NULL;
