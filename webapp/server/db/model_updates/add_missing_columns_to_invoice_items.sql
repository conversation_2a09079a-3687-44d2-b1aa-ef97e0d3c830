-- Add missing columns to invoice_items table
-- Generated on 2025-04-30T19:58:20.406Z
-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the current schema or use 'site' as default
    BEGIN
        -- Check if DB_SCHEMA is set as a PostgreSQL variable with proper namespace
        SELECT current_setting('app.db_schema') INTO schema_name;
        EXCEPTION WHEN OTHERS THEN
            -- If not set, use current schema or 'site' as default
            SELECT current_schema() INTO schema_name;
    END;

    -- Add product_id column if it doesn't exist
    EXECUTE format('
        ALTER TABLE %I.invoice_items 
        ADD COLUMN IF NOT EXISTS product_id UUID;
    ', schema_name);

    RAISE NOTICE 'Successfully added missing columns to invoice_items table in schema %', schema_name;
END $$;
