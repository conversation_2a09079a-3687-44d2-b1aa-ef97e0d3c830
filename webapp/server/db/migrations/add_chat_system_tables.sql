-- Migration: Add chat system tables
-- Depends on:

SET search_path TO site;

-- Chat Permissions Table (for enabling/disabling chat by role or farm)
CREATE TABLE chat_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL, -- owner, manager, accountant, etc. (matches user_farms.role)
    can_use_chat BOOLEAN DEFAULT TRUE,
    can_create_group_chats BOOLEAN DEFAULT TRUE,
    can_share_files BOOLEAN DEFAULT TRUE,
    can_create_tasks_from_chat BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(farm_id, role)
);

-- User Connections Table (for farmer-to-farmer connections)
CREATE TABLE user_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    connected_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, accepted, rejected, blocked
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, connected_user_id),
    CHECK (user_id != connected_user_id)
);

-- Chat Conversations Table (for one-to-one and group chats)
CREATE TABLE chat_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255), -- NULL for direct messages, name for group chats
    type VARCHAR(50) NOT NULL DEFAULT 'direct', -- direct, group, channel
    farm_id UUID REFERENCES farms(id) ON DELETE CASCADE, -- NULL for farmer-to-farmer chats outside farm context
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    is_pinned BOOLEAN DEFAULT FALSE, -- for pinning conversations to the chat bar
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat Conversation Participants Table
CREATE TABLE chat_conversation_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES chat_conversations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_admin BOOLEAN DEFAULT FALSE, -- for group chat admins
    is_muted BOOLEAN DEFAULT FALSE, -- for muting notifications
    last_read_at TIMESTAMP, -- for tracking read/unread status
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(conversation_id, user_id)
);

-- Chat Messages Table
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES chat_conversations(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE SET NULL,
    message_type VARCHAR(50) NOT NULL DEFAULT 'text', -- text, image, file, system, task
    content TEXT NOT NULL,
    is_edited BOOLEAN DEFAULT FALSE,
    parent_message_id UUID REFERENCES chat_messages(id) ON DELETE SET NULL, -- for threaded replies
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat Message Attachments Table
CREATE TABLE chat_message_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL,
    file_url VARCHAR(1024) NOT NULL,
    thumbnail_url VARCHAR(1024),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Chat Message Reactions Table
CREATE TABLE chat_message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reaction VARCHAR(50) NOT NULL, -- emoji code
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id, reaction)
);

-- Chat Message Read Status Table
CREATE TABLE chat_message_read_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id)
);

-- Chat Tasks Table (for tasks created from chat messages)
CREATE TABLE chat_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES chat_messages(id) ON DELETE SET NULL,
    task_id UUID, -- References tasks table (assuming it exists)
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Online Status Table
CREATE TABLE user_online_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_online BOOLEAN DEFAULT FALSE,
    last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Create indexes for performance
CREATE INDEX idx_chat_permissions_farm_id ON chat_permissions(farm_id);
CREATE INDEX idx_user_connections_user_id ON user_connections(user_id);
CREATE INDEX idx_user_connections_connected_user_id ON user_connections(connected_user_id);
CREATE INDEX idx_chat_conversations_farm_id ON chat_conversations(farm_id);
CREATE INDEX idx_chat_conversations_created_by ON chat_conversations(created_by);
CREATE INDEX idx_chat_conversation_participants_conversation_id ON chat_conversation_participants(conversation_id);
CREATE INDEX idx_chat_conversation_participants_user_id ON chat_conversation_participants(user_id);
CREATE INDEX idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_parent_message_id ON chat_messages(parent_message_id);
CREATE INDEX idx_chat_message_attachments_message_id ON chat_message_attachments(message_id);
CREATE INDEX idx_chat_message_reactions_message_id ON chat_message_reactions(message_id);
CREATE INDEX idx_chat_message_reactions_user_id ON chat_message_reactions(user_id);
CREATE INDEX idx_chat_message_read_status_message_id ON chat_message_read_status(message_id);
CREATE INDEX idx_chat_message_read_status_user_id ON chat_message_read_status(user_id);
CREATE INDEX idx_chat_tasks_message_id ON chat_tasks(message_id);
CREATE INDEX idx_chat_tasks_task_id ON chat_tasks(task_id);
CREATE INDEX idx_user_online_status_user_id ON user_online_status(user_id);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_chat_permissions_timestamp BEFORE UPDATE ON chat_permissions
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_user_connections_timestamp BEFORE UPDATE ON user_connections
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_conversations_timestamp BEFORE UPDATE ON chat_conversations
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_conversation_participants_timestamp BEFORE UPDATE ON chat_conversation_participants
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_messages_timestamp BEFORE UPDATE ON chat_messages
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_message_attachments_timestamp BEFORE UPDATE ON chat_message_attachments
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_message_reactions_timestamp BEFORE UPDATE ON chat_message_reactions
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_message_read_status_timestamp BEFORE UPDATE ON chat_message_read_status
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_chat_tasks_timestamp BEFORE UPDATE ON chat_tasks
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_user_online_status_timestamp BEFORE UPDATE ON user_online_status
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Add comments to tables and columns
COMMENT ON TABLE chat_permissions IS 'Stores chat feature permissions by farm and role';
COMMENT ON TABLE user_connections IS 'Stores connections between farmers for direct messaging';
COMMENT ON TABLE chat_conversations IS 'Stores chat conversations (direct messages, group chats, channels)';
COMMENT ON TABLE chat_conversation_participants IS 'Stores participants in chat conversations';
COMMENT ON TABLE chat_messages IS 'Stores chat messages';
COMMENT ON TABLE chat_message_attachments IS 'Stores files attached to chat messages';
COMMENT ON TABLE chat_message_reactions IS 'Stores emoji reactions to chat messages';
COMMENT ON TABLE chat_message_read_status IS 'Stores read status of messages by users';
COMMENT ON TABLE chat_tasks IS 'Stores tasks created from chat messages';
COMMENT ON TABLE user_online_status IS 'Stores online status of users';