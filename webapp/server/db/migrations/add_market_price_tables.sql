-- Add market price tables to the database
-- This migration adds tables for storing market prices, future prices, and historical prices

-- Set the search path to the site schema
SET search_path TO site;

-- Create ENUM type for price trend if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'price_trend_enum') THEN
    CREATE TYPE price_trend_enum AS ENUM ('up', 'down', 'stable');
  END IF;
END
$$;

-- Create market_prices table
CREATE TABLE IF NOT EXISTS market_prices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
  commodity VARCHAR(50) NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  date DATE NOT NULL,
  location VARCHAR(100) NOT NULL,
  trend price_trend_enum NOT NULL,
  percent_change DECIMAL(5, 2) NOT NULL,
  source VARCHAR(100),
  api_source VARCHAR(50),
  is_cached BOOLEAN NOT NULL DEFAULT TRUE,
  cache_expiry TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for market_prices
CREATE INDEX IF NOT EXISTS market_prices_commodity_date_idx ON market_prices(commodity, date);
CREATE INDEX IF NOT EXISTS market_prices_farm_id_idx ON market_prices(farm_id);

-- Create future_prices table
CREATE TABLE IF NOT EXISTS future_prices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
  commodity VARCHAR(50) NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  month VARCHAR(3) NOT NULL,
  year VARCHAR(4) NOT NULL,
  exchange VARCHAR(20) NOT NULL,
  contract_date DATE,
  source VARCHAR(100),
  api_source VARCHAR(50),
  is_cached BOOLEAN NOT NULL DEFAULT TRUE,
  cache_expiry TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for future_prices
CREATE INDEX IF NOT EXISTS future_prices_commodity_month_year_idx ON future_prices(commodity, month, year);
CREATE INDEX IF NOT EXISTS future_prices_farm_id_idx ON future_prices(farm_id);

-- Create historical_prices table
CREATE TABLE IF NOT EXISTS historical_prices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
  commodity VARCHAR(50) NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  date DATE NOT NULL,
  location VARCHAR(100) NOT NULL,
  trend price_trend_enum NOT NULL,
  percent_change DECIMAL(5, 2) NOT NULL,
  source VARCHAR(100),
  api_source VARCHAR(50),
  is_cached BOOLEAN NOT NULL DEFAULT TRUE,
  cache_expiry TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for historical_prices
CREATE INDEX IF NOT EXISTS historical_prices_commodity_date_idx ON historical_prices(commodity, date);
CREATE INDEX IF NOT EXISTS historical_prices_farm_id_idx ON historical_prices(farm_id);
CREATE INDEX IF NOT EXISTS historical_prices_date_idx ON historical_prices(date);

-- Add comments to tables
COMMENT ON TABLE market_prices IS 'Stores current market prices for agricultural commodities';
COMMENT ON TABLE future_prices IS 'Stores future price predictions for agricultural commodities';
COMMENT ON TABLE historical_prices IS 'Stores historical price data for agricultural commodities';

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status)
    VALUES (
      gen_random_uuid(),
      'Add Market Price Tables',
      'migrations/add_market_price_tables.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      NOW(),
      'completed'
    );
  END IF;
END
$$;
