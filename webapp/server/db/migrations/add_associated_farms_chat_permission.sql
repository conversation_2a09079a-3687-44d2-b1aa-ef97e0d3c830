-- Migration: Add permission for chatting with associated farms
-- Depends on:

SET search_path TO site;

-- Add can_chat_with_associated_farms column to chat_permissions table
ALTER TABLE chat_permissions
ADD COLUMN can_chat_with_associated_farms BOOLEAN DEFAULT TRUE;

-- Add comment to explain the purpose of the column
COMMENT ON COLUMN chat_permissions.can_chat_with_associated_farms IS 'Whether the role can chat with users from associated farms';

-- Update existing permissions to set the new column value
-- Owners and managers can chat with associated farms by default
UPDATE chat_permissions
SET can_chat_with_associated_farms = TRUE
WHERE role IN ('owner', 'manager');

-- Other roles cannot chat with associated farms by default
UPDATE chat_permissions
SET can_chat_with_associated_farms = FALSE
WHERE role NOT IN ('owner', 'manager');