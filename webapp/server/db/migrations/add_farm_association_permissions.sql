-- Migration: Add permissions for farm associations
-- Depends on:

SET search_path TO site;

-- Create farm_association_permissions table
CREATE TABLE farm_association_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_association_id UUID NOT NULL REFERENCES farm_associations(id) ON DELETE CASCADE,
  permission_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  initiator_farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT unique_farm_association_permission UNIQUE (farm_association_id, permission_type),
  CONSTRAINT valid_status CHECK (status IN ('pending', 'active', 'rejected'))
);

-- Add comment to explain the purpose of the table
COMMENT ON TABLE farm_association_permissions IS 'Stores permissions for shared features between associated farms';
COMMENT ON COLUMN farm_association_permissions.permission_type IS 'Type of permission (invoices, chat, customers, products, equipment, tasks, etc.)';
COMMENT ON COLUMN farm_association_permissions.status IS 'Status of the permission (pending, active, rejected)';
COMMENT ON COLUMN farm_association_permissions.initiator_farm_id IS 'The farm that initiated the permission change';
COMMENT ON COLUMN farm_association_permissions.last_updated_at IS 'When the permission was last updated';

-- Create index for faster lookups
CREATE INDEX idx_farm_association_permissions_farm_association_id ON farm_association_permissions(farm_association_id);
CREATE INDEX idx_farm_association_permissions_initiator_farm_id ON farm_association_permissions(initiator_farm_id);

-- Create a function to automatically update last_updated_at
CREATE OR REPLACE FUNCTION update_farm_association_permission_last_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to call the function
CREATE TRIGGER update_farm_association_permission_last_updated_at
BEFORE UPDATE ON farm_association_permissions
FOR EACH ROW
EXECUTE FUNCTION update_farm_association_permission_last_updated_at();

-- Insert default permission types for existing farm associations
INSERT INTO farm_association_permissions (farm_association_id, permission_type, status, initiator_farm_id)
SELECT 
  fa.id, 
  'invoices', 
  CASE WHEN fa.status = 'active' THEN 'pending' ELSE fa.status END,
  fa.initiator_farm_id
FROM farm_associations fa;

INSERT INTO farm_association_permissions (farm_association_id, permission_type, status, initiator_farm_id)
SELECT 
  fa.id, 
  'chat', 
  CASE WHEN fa.status = 'active' THEN 'pending' ELSE fa.status END,
  fa.initiator_farm_id
FROM farm_associations fa;

INSERT INTO farm_association_permissions (farm_association_id, permission_type, status, initiator_farm_id)
SELECT 
  fa.id, 
  'customers', 
  CASE WHEN fa.status = 'active' THEN 'pending' ELSE fa.status END,
  fa.initiator_farm_id
FROM farm_associations fa;

INSERT INTO farm_association_permissions (farm_association_id, permission_type, status, initiator_farm_id)
SELECT 
  fa.id, 
  'products', 
  CASE WHEN fa.status = 'active' THEN 'pending' ELSE fa.status END,
  fa.initiator_farm_id
FROM farm_associations fa;

INSERT INTO farm_association_permissions (farm_association_id, permission_type, status, initiator_farm_id)
SELECT 
  fa.id, 
  'equipment', 
  CASE WHEN fa.status = 'active' THEN 'pending' ELSE fa.status END,
  fa.initiator_farm_id
FROM farm_associations fa;

INSERT INTO farm_association_permissions (farm_association_id, permission_type, status, initiator_farm_id)
SELECT 
  fa.id, 
  'tasks', 
  CASE WHEN fa.status = 'active' THEN 'pending' ELSE fa.status END,
  fa.initiator_farm_id
FROM farm_associations fa;