# Fix Invoice Items Foreign Key Constraint

## Issue Description

The following error was reported:

```
https://api.nxtacre.com/invoices/b5dc3e30-328c-4526-a8af-2afac04aa34d: InvoiceItem is not associated to Invoice!
```

This error indicates that there is an issue with the foreign key constraint between the `invoice_items` and `invoices` tables. The application expects a proper association between these tables, but the database constraint might be missing or incorrectly configured.

## Solution

The migration file `20240801_fix_invoice_items_fk_constraint.sql` addresses this issue by:

1. Dropping the existing foreign key constraint if it exists
2. Recreating the constraint with the `ON DELETE CASCADE` option

This ensures that:
- The relationship between `invoice_items` and `invoices` is properly enforced
- When an invoice is deleted, all associated invoice items are also deleted (cascade behavior)
- The database schema matches the relationship defined in the application models

## Implementation Details

The migration performs the following SQL operations:

```sql
-- Drop the existing constraint
ALTER TABLE invoice_items DROP CONSTRAINT IF EXISTS invoice_items_invoice_id_fkey;

-- Recreate the constraint with ON DELETE CASCADE
ALTER TABLE invoice_items 
ADD CONSTRAINT invoice_items_invoice_id_fkey 
FOREIGN KEY (invoice_id) 
REFERENCES invoices(id) 
ON DELETE CASCADE;
```

This change aligns the database schema with the Sequelize model associations defined in the application code:

```javascript
// From associations.js
InvoiceItem.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
Invoice.hasMany(InvoiceItem, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
```

## Related Files

- `webapp/server/models/Invoice.js`
- `webapp/server/models/InvoiceItem.js`
- `webapp/server/models/associations.js`