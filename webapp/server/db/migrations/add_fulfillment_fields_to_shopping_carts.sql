-- Migration: Add fulfillment fields to shopping carts
-- Depends on: add_shopping_cart_tables.sql, add_farm_fulfillment_options_table.sql

SET search_path TO site;

-- Add fulfillment fields to shopping_carts table
ALTER TABLE shopping_carts
ADD COLUMN fulfillment_method VARCHAR(50),
ADD COLUMN pickup_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN delivery_address_id UUID REFERENCES customer_addresses(id);

COMMENT ON COLUMN shopping_carts.fulfillment_method IS 'Method of fulfillment: delivery, pickup';
COMMENT ON COLUMN shopping_carts.pickup_date IS 'Scheduled date for pickup (if applicable)';
COMMENT ON COLUMN shopping_carts.delivery_address_id IS 'Reference to the delivery address (if applicable)';