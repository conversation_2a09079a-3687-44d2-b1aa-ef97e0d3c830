-- Migration to add harvest_direction_maps table
-- This table stores harvest direction maps for fields with ordering, descriptions, and drawing elements

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Step 1: Create harvest_direction_maps table

CREATE TABLE IF NOT EXISTS site.harvest_direction_maps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    field_id UUID NOT NULL REFERENCES site.fields(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL DEFAULT 1,
    elements JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE site.harvest_direction_maps IS 'Stores harvest direction maps for fields with ordering, descriptions, and drawing elements';
COMMENT ON COLUMN site.harvest_direction_maps.id IS 'Unique identifier for the harvest direction map';
COMMENT ON COLUMN site.harvest_direction_maps.field_id IS 'ID of the field this harvest direction map is for';
COMMENT ON COLUMN site.harvest_direction_maps.name IS 'Name of the harvest direction map';
COMMENT ON COLUMN site.harvest_direction_maps.description IS 'Description of the harvest direction map';
COMMENT ON COLUMN site.harvest_direction_maps.order_index IS 'Order index for sorting multiple maps';
COMMENT ON COLUMN site.harvest_direction_maps.elements IS 'JSONB array of drawing elements (polygons, polylines, markers) with properties like color, label, etc.';

-- Step 2: Create indexes for better performance

CREATE INDEX IF NOT EXISTS idx_harvest_direction_maps_field_id ON site.harvest_direction_maps(field_id);
CREATE INDEX IF NOT EXISTS idx_harvest_direction_maps_order_index ON site.harvest_direction_maps(order_index);

-- Step 3: Create trigger for updated_at timestamp

CREATE TRIGGER update_harvest_direction_maps_timestamp
BEFORE UPDATE ON site.harvest_direction_maps
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();