-- Migration to add custom_login_text and custom_login_logo columns to the farms table

-- Set the search path to the appropriate schema
SET search_path TO site;

-- Get the schema name from environment variable or use 'site' as default
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add custom_login_text column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'farms'
        AND column_name = 'custom_login_text'
    ) THEN
        EXECUTE format('ALTER TABLE %I.farms ADD COLUMN custom_login_text TEXT', schema_name);
    END IF;

    -- Add custom_login_logo column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'farms'
        AND column_name = 'custom_login_logo'
    ) THEN
        EXECUTE format('ALTER TABLE %I.farms ADD COLUMN custom_login_logo VARCHAR(255)', schema_name);
    END IF;
END $$;
