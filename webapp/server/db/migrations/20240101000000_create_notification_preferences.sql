-- Migration: Create notification preferences table
-- Depends on: 20230101000000_create_users.sql

SET search_path TO site;

CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  enable_in_app BOOLEAN NOT NULL DEFAULT TRUE,
  enable_email BOOLEAN NOT NULL DEFAULT TRUE,
  chat_message_notifications BOOLEAN NOT NULL DEFAULT TRUE,
  task_notifications BOOLEAN NOT NULL DEFAULT TRUE,
  document_notifications BOOLEAN NOT NULL DEFAULT TRUE,
  system_notifications B<PERSON><PERSON>EAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Add index for faster lookups by user_id
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);

-- Add comment to the table
COMMENT ON TABLE notification_preferences IS 'Stores user notification preferences for different types of notifications';