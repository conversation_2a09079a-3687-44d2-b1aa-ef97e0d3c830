-- Create weather table

-- Set the search path to the site schema
SET search_path TO site;

-- Enable the uuid-ossp extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE TABLE IF NOT EXISTS weather (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
  field_id UUID REFERENCES fields(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 6) NOT NULL,
  longitude DECIMAL(10, 6) NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  temperature DECIMAL(5, 2),
  feels_like DECIMAL(5, 2),
  humidity INTEGER,
  wind_speed DECIMAL(5, 2),
  wind_direction VARCHAR(10),
  precipitation DECIMAL(5, 2),
  precipitation_chance INTEGER,
  condition VARCHAR(50),
  icon VARCHAR(20),
  forecast_type VARCHAR(10) NOT NULL,
  forecast_hour INTEGER,
  forecast_day INTEGER,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create index on farm_id
CREATE INDEX IF NOT EXISTS idx_weather_farm_id ON weather(farm_id);

-- Create index on field_id
CREATE INDEX IF NOT EXISTS idx_weather_field_id ON weather(field_id);

-- Create index on timestamp
CREATE INDEX IF NOT EXISTS idx_weather_timestamp ON weather(timestamp);

-- Create index on forecast_type
CREATE INDEX IF NOT EXISTS idx_weather_forecast_type ON weather(forecast_type);

-- Add comment to table
COMMENT ON TABLE weather IS 'Stores weather data for farms and fields';
