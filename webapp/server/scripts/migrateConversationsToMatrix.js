import ChatConversation from '../models/ChatConversation.js';
import ChatMessage from '../models/ChatMessage.js';
import MatrixRoomMapping from '../models/MatrixRoomMapping.js';
import matrixClientService from '../services/matrixClientService.js';
import { getNxtAcreUserIdToMatrixUserId } from '../utils/matrixUtils.js';
import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Migrate conversations from NxtAcre to Matrix Synapse
 * This script creates Matrix rooms for all NxtAcre conversations and migrates messages
 */
async function migrateConversationsToMatrix() {
  console.log('Starting conversation migration to Matrix Synapse...');

  try {
    // Get all conversations from NxtAcre
    const conversations = await ChatConversation.findAll({
      include: [
        {
          model: sequelize.models.ChatParticipant,
          as: 'participants',
          include: [
            {
              model: sequelize.models.User,
              as: 'user',
              attributes: ['id', 'email', 'first_name', 'last_name']
            }
          ]
        }
      ]
    });

    console.log(`Found ${conversations.length} conversations to migrate`);

    // Track statistics
    let created = 0;
    let skipped = 0;
    let errors = 0;
    let messagesMigrated = 0;

    // Create a mapping of NxtAcre conversation IDs to Matrix room IDs
    const conversationToRoomMap = new Map();

    // Process each conversation
    for (const conversation of conversations) {
      try {
        console.log(`Processing conversation: ${conversation.id} (${conversation.name || 'Unnamed'})`);

        // Check if this conversation has already been migrated
        // We'll store this in a separate table later, but for now we'll check if a room with this name exists
        const existingRooms = await matrixClientService.getRooms();
        const existingRoom = existingRooms.find(room => 
          room.name === conversation.name || 
          room.roomId.includes(conversation.id)
        );

        if (existingRoom) {
          console.log(`Conversation ${conversation.id} already migrated to room ${existingRoom.roomId}, skipping`);
          conversationToRoomMap.set(conversation.id, existingRoom.roomId);
          skipped++;
          continue;
        }

        // Get participants
        const participants = conversation.participants || [];
        console.log(`Conversation has ${participants.length} participants`);

        // Create a room name
        let roomName = conversation.name;
        if (!roomName && conversation.type === 'direct' && participants.length === 2) {
          // For direct messages, use the other participant's name
          const otherParticipant = participants.find(p => p.user_id !== conversation.created_by);
          if (otherParticipant && otherParticipant.user) {
            roomName = `${otherParticipant.user.first_name} ${otherParticipant.user.last_name}`;
          } else {
            roomName = `Direct Message (${conversation.id})`;
          }
        } else if (!roomName) {
          roomName = `Group Chat (${conversation.id})`;
        }

        // Create a room in Matrix
        const isPublic = conversation.type === 'channel';
        const response = await matrixClientService.createRoom(
          roomName,
          '', // No topic for now
          isPublic,
          conversation.created_by,
          conversation.farm_id
        );

        const roomId = response.room_id;
        console.log(`Created Matrix room ${roomId} for conversation ${conversation.id}`);

        // Store the mapping
        conversationToRoomMap.set(conversation.id, roomId);

        // Invite all participants to the room
        for (const participant of participants) {
          if (participant.user_id !== conversation.created_by) {
            try {
              await matrixClientService.inviteToRoom(roomId, participant.user_id);
              console.log(`Invited user ${participant.user_id} to room ${roomId}`);
            } catch (error) {
              console.error(`Error inviting user ${participant.user_id} to room ${roomId}:`, error.message);
            }
          }
        }

        // Migrate messages
        const messages = await ChatMessage.findAll({
          where: { conversation_id: conversation.id },
          order: [['created_at', 'ASC']]
        });

        console.log(`Migrating ${messages.length} messages for conversation ${conversation.id}`);

        for (const message of messages) {
          try {
            // Send the message to the room
            await matrixClientService.sendMessage(
              roomId,
              message.content,
              'm.room.message',
              message.sender_id
            );

            messagesMigrated++;
          } catch (error) {
            console.error(`Error migrating message ${message.id}:`, error.message);
          }
        }

        created++;
      } catch (error) {
        console.error(`Error migrating conversation ${conversation.id}:`, error.message);
        errors++;
      }
    }

    console.log('Conversation migration completed:');
    console.log(`- Created: ${created}`);
    console.log(`- Skipped (already exist): ${skipped}`);
    console.log(`- Errors: ${errors}`);
    console.log(`- Messages migrated: ${messagesMigrated}`);

    // Store the conversation to room mapping in the database
    console.log('Storing conversation to room mappings in the database...');
    let mappingsStored = 0;
    let mappingErrors = 0;

    for (const [conversationId, roomId] of conversationToRoomMap.entries()) {
      try {
        // Check if mapping already exists
        const existingMapping = await MatrixRoomMapping.getByConversationId(conversationId);

        if (existingMapping) {
          console.log(`Mapping for conversation ${conversationId} already exists, skipping`);
          continue;
        }

        // Create the mapping
        await MatrixRoomMapping.createMapping({
          conversation_id: conversationId,
          matrix_room_id: roomId
        });

        console.log(`Stored mapping for conversation ${conversationId} to room ${roomId}`);
        mappingsStored++;
      } catch (error) {
        console.error(`Error storing mapping for conversation ${conversationId}:`, error.message);
        mappingErrors++;
      }
    }

    console.log(`Stored ${mappingsStored} mappings with ${mappingErrors} errors`)

  } catch (error) {
    console.error('Error migrating conversations to Matrix:', error);
  }
}

// Run the script if called directly
if (process.argv[1].endsWith('migrateConversationsToMatrix.js')) {
  migrateConversationsToMatrix()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error running migration script:', error);
      process.exit(1);
    });
}

export default migrateConversationsToMatrix;
