import { sequelize } from '../config/database.js';
import Role from '../models/Role.js';
import RolePermission from '../models/RolePermission.js';
import dotenv from 'dotenv';

dotenv.config();

// Get the schema from environment variables
const schema = process.env.DB_SCHEMA || 'site';

// List of all features in the application
const features = [
  // User and authentication features
  'auth',
  'users',
  'roles',
  'permissions',
  'user_farms',
  
  // Farm management features
  'farms',
  'fields',
  'field_health',
  'crops',
  'crop_types',
  'harvests',
  'harvest_schedules',
  
  // Financial features
  'invoices',
  'expenses',
  'subscriptions',
  'quickbooks',
  
  // Inventory and product features
  'inventory',
  'products',
  'product_inventory',
  'seeds',
  'chemicals',
  
  // Equipment features
  'equipment',
  'maintenance',
  'telematics',
  'isobus',
  'equipment_sharing',
  
  // Livestock features
  'livestock',
  'vets',
  
  // Weather and environmental features
  'weather',
  'weather_alerts',
  'historical_weather',
  'soil',
  'environment',
  'sustainability',
  
  // Market features
  'market_prices',
  'market',
  
  // Customer and supplier features
  'customers',
  'suppliers',
  'orders',
  'service_providers',
  'service_requests',
  
  // Employee features
  'employees',
  'time_entries',
  'time_off_requests',
  'pay_stubs',
  
  // Document features
  'documents',
  'external_storage_auth',
  
  // Integration features
  'integrations',
  'ambrook',
  
  // Dashboard and reporting features
  'dashboard',
  
  // Task and workflow features
  'tasks',
  'workflows',
  'alerts',
  
  // Support and help features
  'support',
  'help',
  'faqs',
  
  // Grant features
  'grants',
  
  // Menu preferences
  'menu',
  
  // Business account features
  'business_account',
  
  // Admin features
  'admin',
  'cron',
  'migration',
  'db_migrations',
  'sentry',
  
  // Transport features
  'drivers',
  'deliveries',
  'pickups',
  'driver_schedules',
  'driver_locations',
  
  // Receipt features
  'receipts',
  
  // Webhook features
  'webhooks',
  
  // AI assistant features
  'ai_assistant',
  
  // Session features
  'sessions',
  
  // IoT features
  'iot'
];

// Initialize permissions for all features
const initializeFeaturePermissions = async () => {
  try {
    console.log('Initializing feature permissions...');
    
    // Set search_path to use the specified schema
    await sequelize.query(`SET search_path TO ${schema};`);
    
    // Get all roles
    const roles = await Role.findAll();
    console.log(`Found ${roles.length} roles`);
    
    // For each role and feature, ensure permissions exist
    for (const role of roles) {
      console.log(`Processing role: ${role.name} (${role.id})`);
      
      for (const feature of features) {
        // Check if permission already exists
        const existingPermission = await RolePermission.findOne({
          where: {
            role_id: role.id,
            farm_id: role.farm_id,
            feature: feature
          }
        });
        
        if (!existingPermission) {
          console.log(`Creating permission for role ${role.name} and feature ${feature}`);
          
          // Default permissions based on role
          let permissions = {
            can_view: false,
            can_create: false,
            can_edit: false,
            can_delete: false,
            can_approve: false,
            can_reject: false,
            can_assign: false,
            can_export: false,
            can_import: false,
            can_manage_settings: false,
            can_generate_reports: false,
            can_view_sensitive: false
          };
          
          // Set default permissions based on role name
          if (role.name === 'farm_owner') {
            // Farm owners have all permissions
            permissions = {
              can_view: true,
              can_create: true,
              can_edit: true,
              can_delete: true,
              can_approve: true,
              can_reject: true,
              can_assign: true,
              can_export: true,
              can_import: true,
              can_manage_settings: true,
              can_generate_reports: true,
              can_view_sensitive: true
            };
          } else if (role.name === 'farm_admin') {
            // Farm admins have most permissions except delete
            permissions = {
              can_view: true,
              can_create: true,
              can_edit: true,
              can_delete: false,
              can_approve: true,
              can_reject: true,
              can_assign: true,
              can_export: true,
              can_import: true,
              can_manage_settings: true,
              can_generate_reports: true,
              can_view_sensitive: true
            };
          } else if (role.name === 'farm_manager') {
            // Farm managers have operational permissions
            permissions = {
              can_view: true,
              can_create: true,
              can_edit: true,
              can_delete: false,
              can_approve: true,
              can_reject: true,
              can_assign: true,
              can_export: true,
              can_import: false,
              can_manage_settings: false,
              can_generate_reports: true,
              can_view_sensitive: false
            };
          } else if (role.name === 'farm_employee') {
            // Farm employees have basic permissions
            permissions = {
              can_view: true,
              can_create: true,
              can_edit: false,
              can_delete: false,
              can_approve: false,
              can_reject: false,
              can_assign: false,
              can_export: false,
              can_import: false,
              can_manage_settings: false,
              can_generate_reports: false,
              can_view_sensitive: false
            };
          } else if (role.name === 'accountant') {
            // Accountants have financial permissions
            permissions = {
              can_view: true,
              can_create: false,
              can_edit: false,
              can_delete: false,
              can_approve: false,
              can_reject: false,
              can_assign: false,
              can_export: true,
              can_import: false,
              can_manage_settings: false,
              can_generate_reports: true,
              can_view_sensitive: true
            };
          }
          
          // Create the permission
          await RolePermission.create({
            role_id: role.id,
            farm_id: role.farm_id,
            feature: feature,
            ...permissions
          });
        }
      }
    }
    
    console.log('Feature permissions initialized successfully');
  } catch (error) {
    console.error('Error initializing feature permissions:', error);
  }
};

// Run the initialization if this script is executed directly
if (process.argv[1].endsWith('initializeFeaturePermissions.js')) {
  initializeFeaturePermissions()
    .then(() => {
      console.log('Initialization completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error during initialization:', error);
      process.exit(1);
    });
}

export default initializeFeaturePermissions;