import { sequelize } from '../config/database.js';

async function verifyUserTypeColumn() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    const schema = process.env.DB_SCHEMA || 'site';
    console.log(`Using schema: ${schema}`);
    
    // Set search_path to use the specified schema
    await sequelize.query(`SET search_path TO ${schema};`);
    
    // Check if the user_type column exists
    console.log('Checking if user_type column exists in users table...');
    const columnResult = await sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_schema = '${schema}' 
      AND table_name = 'users' 
      AND column_name = 'user_type'
    `, { type: sequelize.QueryTypes.SELECT });
    
    if (columnResult.length === 0) {
      console.error('ERROR: user_type column does not exist in users table!');
    } else {
      console.log('SUCCESS: user_type column exists with the following properties:');
      console.log(JSON.stringify(columnResult[0], null, 2));
      
      // Check if it's an enum type
      if (columnResult[0].data_type === 'USER-DEFINED' && columnResult[0].udt_name === 'user_type_enum') {
        console.log('SUCCESS: user_type column is properly defined as an ENUM type.');
      } else {
        console.warn(`WARNING: user_type column exists but has type ${columnResult[0].data_type} (${columnResult[0].udt_name}) instead of ENUM.`);
      }
      
      // Check if the enum type exists
      const enumResult = await sequelize.query(`
        SELECT typname, typtype
        FROM pg_type
        WHERE typname = 'user_type_enum'
      `, { type: sequelize.QueryTypes.SELECT });
      
      if (enumResult.length === 0) {
        console.error('ERROR: user_type_enum type does not exist!');
      } else {
        console.log('SUCCESS: user_type_enum type exists with the following properties:');
        console.log(JSON.stringify(enumResult[0], null, 2));
      }
      
      // Check the values in the enum type
      const enumValuesResult = await sequelize.query(`
        SELECT enumlabel
        FROM pg_enum
        WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_type_enum')
        ORDER BY enumsortorder
      `, { type: sequelize.QueryTypes.SELECT });
      
      if (enumValuesResult.length === 0) {
        console.error('ERROR: user_type_enum type has no values!');
      } else {
        console.log('SUCCESS: user_type_enum type has the following values:');
        console.log(enumValuesResult.map(row => row.enumlabel).join(', '));
      }
    }
  } catch (error) {
    console.error('Error verifying user_type column:', error);
  } finally {
    // Close the connection
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

// Run the function
verifyUserTypeColumn()
  .then(() => {
    console.log('Verification completed.');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });