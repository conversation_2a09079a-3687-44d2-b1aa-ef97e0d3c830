import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runRoleMigration() {
  try {
    console.log('Running migration to add roles table and role_id column to user_farms...');
    
    // Define the migration files to run in order
    const migrationFiles = [
      'add_roles_table.sql',
      'add_role_id_to_user_farms.sql'
    ];
    
    // Run each migration file
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      // Read the SQL file
      const sqlPath = path.join(__dirname, '../db', file);
      const sql = fs.readFileSync(sqlPath, 'utf8');
      
      // Execute the SQL
      await sequelize.query(sql);
      
      console.log(`Migration ${file} completed successfully`);
    }
    
    console.log('All migrations completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

runRoleMigration();