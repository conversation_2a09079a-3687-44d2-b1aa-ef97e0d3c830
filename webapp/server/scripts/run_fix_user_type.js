import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runFixUserTypeColumn() {
  try {
    console.log('Starting user_type column fix...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db/fix_user_type_column.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    console.log('Executing SQL...');
    await sequelize.query(sql);
    
    // Verify the column exists and has the correct type
    console.log('Verifying user_type column...');
    const schema = process.env.DB_SCHEMA || 'site';
    const verifyResult = await sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_schema = '${schema}' 
      AND table_name = 'users' 
      AND column_name = 'user_type'
    `, { type: sequelize.QueryTypes.SELECT });
    
    if (verifyResult.length === 0) {
      console.error('Error: user_type column still does not exist!');
      process.exit(1);
    } else {
      console.log('user_type column exists with the following properties:');
      console.log(JSON.stringify(verifyResult[0], null, 2));
      
      // Check if it's an enum type
      if (verifyResult[0].data_type === 'USER-DEFINED' && verifyResult[0].udt_name === 'user_type_enum') {
        console.log('Success: user_type column is properly defined as an ENUM type.');
      } else {
        console.warn(`Warning: user_type column exists but has type ${verifyResult[0].data_type} (${verifyResult[0].udt_name}) instead of ENUM.`);
      }
    }
    
    console.log('User type column fix completed successfully');
  } catch (error) {
    console.error('Error fixing user_type column:', error);
    process.exit(1);
  }
}

// Run the function
runFixUserTypeColumn()
  .then(() => {
    console.log('Fix completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });