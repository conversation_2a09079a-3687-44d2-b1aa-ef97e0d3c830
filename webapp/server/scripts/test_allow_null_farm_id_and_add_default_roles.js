import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to run a script and return a promise
function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    console.log(`Running script: ${scriptPath}`);
    
    const process = spawn('node', [scriptPath], { stdio: 'inherit' });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`Script ${scriptPath} completed successfully`);
        resolve();
      } else {
        console.error(`Script ${scriptPath} failed with code ${code}`);
        reject(new Error(`Script ${scriptPath} failed with code ${code}`));
      }
    });
    
    process.on('error', (err) => {
      console.error(`Error running script ${scriptPath}:`, err);
      reject(err);
    });
  });
}

async function testAllowNullFarmIdAndAddDefaultRoles() {
  try {
    console.log('Starting test: Allow NULL farm_id and add default roles');
    
    // Step 1: Run the allow_null_farm_id_in_role_permissions.js script
    const allowNullFarmIdScript = path.join(__dirname, 'allow_null_farm_id_in_role_permissions.js');
    await runScript(allowNullFarmIdScript);
    
    // Step 2: Run the add_default_roles.js script
    const addDefaultRolesScript = path.join(__dirname, 'add_default_roles.js');
    await runScript(addDefaultRolesScript);
    
    // Step 3: Run the test_default_roles.js script to verify
    const testDefaultRolesScript = path.join(__dirname, 'test_default_roles.js');
    await runScript(testDefaultRolesScript);
    
    console.log('\nAll tests completed successfully!');
    console.log('The solution has been verified to work correctly.');
    
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the function
testAllowNullFarmIdAndAddDefaultRoles()
  .then(() => {
    console.log('\nTest completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });