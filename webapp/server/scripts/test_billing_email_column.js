import { sequelize } from '../config/database.js';

async function testBillingEmailColumn() {
  try {
    console.log('Testing if billing_email column exists in farms table...');

    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);

    // Check if the billing_email column exists in the farms table
    const columnCheck = await sequelize.query(
      `SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = '${currentSchema}' 
        AND table_name = 'farms' 
        AND column_name = 'billing_email'
      );`,
      { type: sequelize.QueryTypes.SELECT }
    );

    const columnExists = columnCheck[0].exists;
    console.log(`billing_email column exists in farms table: ${columnExists}`);

    if (columnExists) {
      console.log('Migration was successful!');
    } else {
      console.error('billing_email column does not exist. Migration may not have been run.');
    }

  } catch (error) {
    console.error('Error testing billing_email column:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the test
testBillingEmailColumn()
  .then(() => {
    console.log('Test completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
