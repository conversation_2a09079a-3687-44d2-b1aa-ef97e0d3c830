/**
 * Utility functions for handling HTTP requests
 */

/**
 * Get the client's IP address from the request
 * @param {Object} req - Express request object
 * @returns {string} The client's IP address
 */
export const getClientIp = (req) => {
  const forwardedFor = req.headers['x-forwarded-for'];
  if (forwardedFor) {
    // Get the first IP if there are multiple in the header
    return forwardedFor.split(',')[0].trim();
  }
  return req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket?.remoteAddress || 
         '0.0.0.0';
};