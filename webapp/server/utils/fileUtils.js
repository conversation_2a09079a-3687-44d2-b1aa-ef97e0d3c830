import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { promisify } from 'util';
import { fileTypeFromBuffer } from 'file-type';
import pdfParse from 'pdf-parse';
import Document from '../models/Document.js';
import FarmStorageUsage from '../models/FarmStorageUsage.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import Farm from '../models/Farm.js';
import { uploadToSpaces, downloadFromSpaces, deleteFromSpaces, fileExistsInSpaces } from './spacesUtils.js';
import { encryptFile, decryptFile } from './encryptionUtils.js';
import { uuid_nil } from './uuidUtils.js';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create test data directory if it doesn't exist
const ensureTestDataDirectory = () => {
  const testDataDir = path.join(__dirname, '..', '..', 'test', 'data');
  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
  }
  return testDataDir;
};

// Convert fs functions to promise-based
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const unlink = promisify(fs.unlink);

// No longer using local uploads directory
// All files are stored in Digital Ocean Spaces

// Allowed file types and their MIME types
const ALLOWED_FILE_TYPES = {
  // Documents
  'pdf': 'application/pdf',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'txt': 'text/plain',
  'rtf': 'application/rtf',
  'odt': 'application/vnd.oasis.opendocument.text',

  // Spreadsheets
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'csv': 'text/csv',
  'ods': 'application/vnd.oasis.opendocument.spreadsheet',

  // Presentations
  'ppt': 'application/vnd.ms-powerpoint',
  'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'odp': 'application/vnd.oasis.opendocument.presentation',

  // Images
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'gif': 'image/gif',
  'bmp': 'image/bmp',
  'tiff': 'image/tiff',
  'tif': 'image/tiff',
  'svg': 'image/svg+xml',

  // Audio
  'mp3': 'audio/mpeg',
  'wav': 'audio/wav',
  'ogg': 'audio/ogg',

  // Video
  'mp4': 'video/mp4',
  'avi': 'video/x-msvideo',
  'mov': 'video/quicktime',
  'wmv': 'video/x-ms-wmv',

  // Archives
  'zip': 'application/zip',
  'rar': 'application/x-rar-compressed',
  'tar': 'application/x-tar',
  'gz': 'application/gzip',

  // Web
  'html': 'text/html',
  'htm': 'text/html',
  'css': 'text/css',
  'js': 'text/javascript',

  // Other
  'json': 'application/json',
  'xml': 'application/xml',
  'md': 'text/markdown'
};

// Dangerous file extensions that should be blocked
const DANGEROUS_EXTENSIONS = [
  'exe', 'dll', 'bat', 'cmd', 'sh', 'js', 'vbs', 'ps1', 'msi', 'com', 'jar', 'jnlp',
  'app', 'dmg', 'pkg', 'deb', 'rpm', 'apk', 'scr', 'sys', 'php', 'asp', 'aspx', 'jsp',
  'cgi', 'pl', 'py', 'rb'
];

/**
 * Validates if a file is allowed based on its extension and MIME type
 * @param {string} filename - The name of the file
 * @param {Buffer} fileBuffer - The file buffer for MIME type detection
 * @returns {Promise<{valid: boolean, reason: string|null, detectedType: string|null}>}
 */
export const validateFileType = async (filename, fileBuffer) => {
  try {
    // Get file extension
    const ext = path.extname(filename).toLowerCase().substring(1);

    // Check if extension is in dangerous list
    if (DANGEROUS_EXTENSIONS.includes(ext)) {
      return {
        valid: false,
        reason: 'File type is not allowed for security reasons',
        detectedType: null
      };
    }

    // Check if extension is in allowed list
    if (!Object.keys(ALLOWED_FILE_TYPES).includes(ext)) {
      return {
        valid: false,
        reason: 'File type is not supported',
        detectedType: null
      };
    }

    // Detect actual file type from buffer
    let fileType;
    try {
      fileType = await fileTypeFromBuffer(fileBuffer);
    } catch (error) {
      console.warn(`Error detecting file type: ${error.message}`);
      // If there's an error detecting the file type, we'll proceed with null fileType
      // and rely on extension-based validation
      fileType = null;
    }

    // List of file types that might not be detected by fileTypeFromBuffer
    const commonUndetectableTypes = [
      // Text and document formats
      'txt', 'csv', 'json', 'xml', 'rtf', 'md', 'html', 'htm', 'css', 'js', 
      'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp',
      // Image formats
      'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif', 'svg'
    ];

    // If file type couldn't be detected, check if it's a common type that might not be detected
    if (!fileType && commonUndetectableTypes.includes(ext)) {
      // For text-based files, do a simple check
      if (['txt', 'csv', 'json', 'xml', 'md', 'html', 'css', 'js'].includes(ext)) {
        const isText = isTextFile(fileBuffer);
        if (isText) {
          return {
            valid: true,
            reason: null,
            detectedType: ALLOWED_FILE_TYPES[ext]
          };
        }
      } else {
        // For other common file types that might not be detected, trust the extension
        return {
          valid: true,
          reason: null,
          detectedType: ALLOWED_FILE_TYPES[ext]
        };
      }
    }

    // If file type couldn't be detected and it's not in our common undetectable types list, reject
    if (!fileType && !commonUndetectableTypes.includes(ext)) {
      return {
        valid: false,
        reason: 'Could not determine file type: ' + ext,
        detectedType: null
      };
    }

    // Special handling for Office documents that are detected as zip files
    // Office documents (docx, xlsx, pptx) are actually zip files containing XML
    const officeExtensions = ['docx', 'xlsx', 'pptx', 'odt', 'ods', 'odp'];
    if (fileType && fileType.mime === 'application/zip' && officeExtensions.includes(ext)) {
      return {
        valid: true,
        reason: null,
        detectedType: ALLOWED_FILE_TYPES[ext]
      };
    }

    // If detected MIME type doesn't match expected MIME type for the extension
    if (fileType && fileType.mime !== ALLOWED_FILE_TYPES[ext]) {
      return {
        valid: false,
        reason: 'File extension does not match actual file type',
        detectedType: fileType.mime
      };
    }

    return {
      valid: true,
      reason: null,
      detectedType: fileType ? fileType.mime : ALLOWED_FILE_TYPES[ext]
    };
  } catch (error) {
    console.error('Error validating file type:', error);
    return {
      valid: false,
      reason: 'Error validating file type',
      detectedType: null
    };
  }
};

/**
 * Simple check if a buffer contains text
 * @param {Buffer} buffer - The file buffer
 * @returns {boolean}
 */
const isTextFile = (buffer) => {
  // Check a sample of the file for non-text characters
  const sample = buffer.slice(0, Math.min(buffer.length, 1000));
  for (let i = 0; i < sample.length; i++) {
    const byte = sample[i];
    // Control characters that aren't whitespace
    if (byte < 32 && ![9, 10, 13].includes(byte)) {
      return false;
    }
  }
  return true;
};

/**
 * Gets the full path of a folder in the folder hierarchy
 * @param {string} farmId - The farm ID
 * @param {string} folderId - The folder ID
 * @returns {Promise<string>} The full path of the folder
 */
export const getFolderPath = async (farmId, folderId) => {
  // Helper function to join paths with forward slashes
  const joinPaths = (...parts) => {
    return parts.join('/').replace(/\/+/g, '/');
  };

  // If no folderId is provided, return the root path
  if (!folderId || folderId === 'null' || folderId === uuid_nil()) {
    return joinPaths(farmId, 'documents');
  }

  try {
    // Import DocumentFolder model
    const DocumentFolder = (await import('../models/DocumentFolder.js')).default;

    // Get the folder
    const folder = await DocumentFolder.findByPk(folderId);
    if (!folder) {
      console.warn(`Folder with ID ${folderId} not found, using root path`);
      return joinPaths(farmId, 'documents');
    }

    // If this is a root folder, return the root path
    if (!folder.parent_folder_id || folder.parent_folder_id === uuid_nil()) {
      return joinPaths(farmId, 'documents', folder.name);
    }

    // Recursively get the parent folder path
    const parentPath = await getFolderPath(farmId, folder.parent_folder_id);

    // Log the paths for debugging
    console.log(`Building path for folder ${folder.name} (ID: ${folderId})`);
    console.log(`Parent folder ID: ${folder.parent_folder_id}`);
    console.log(`Parent path: ${parentPath}`);

    // Join paths with forward slashes
    const fullPath = joinPaths(parentPath, folder.name);
    console.log(`Full path: ${fullPath}`);

    return fullPath;
  } catch (error) {
    console.error('Error getting folder path:', error);
    return joinPaths(farmId, 'documents');
  }
};

/**
 * Generates a storage path for a file
 * @param {string} farmId - The farm ID
 * @param {string} userId - The user ID who uploaded the file
 * @param {string} filename - The original filename
 * @param {string} folderId - The folder ID where the file is being uploaded
 * @returns {Promise<string>} The storage path relative to the uploads directory
 */
export const generateStoragePath = async (farmId, userId, filename, folderId) => {
  // Helper function to join paths with forward slashes
  const joinPaths = (...parts) => {
    return parts.join('/').replace(/\/+/g, '/');
  };

  // Ensure farmId is valid, use a default if not
  const safeFarmId = farmId || 'default';
  const sanitizedFilename = path.basename(filename).replace(/[^a-zA-Z0-9_.-]/g, '_');
  const timestamp = Date.now();
  const userIdPrefix = userId ? userId.substring(0, 8) : 'unknown';

  try {
    // Get the folder path
    const folderPath = await getFolderPath(safeFarmId, folderId);

    // Create the storage path with forward slashes
    const storagePath = joinPaths(
      folderPath,
      `${timestamp}_${userIdPrefix}_${sanitizedFilename}`
    );

    console.log(`Generated storage path: ${storagePath}`);
    return storagePath;
  } catch (error) {
    console.error('Error generating storage path:', error);

    // Fallback to the old path structure if there's an error
    return joinPaths(
      safeFarmId,
      'documents',
      new Date().getFullYear().toString(),
      (new Date().getMonth() + 1).toString().padStart(2, '0'),
      `${timestamp}_${userIdPrefix}_${sanitizedFilename}`
    );
  }
};

/**
 * Saves a file to Digital Ocean Spaces
 * @param {Buffer|string} fileData - The file data or path to temp file
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<string>} The storage path where the file was saved
 */
export const saveFile = async (fileData, storagePath) => {
  try {
    // Use Digital Ocean Spaces for storage
    await uploadToSpaces(fileData, storagePath);
    return storagePath;
  } catch (error) {
    console.error('Error saving to Spaces:', error);
    throw new Error(`Failed to upload file to Digital Ocean Spaces: ${error.message}`);
  }
};

/**
 * Deletes a file from Digital Ocean Spaces
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<boolean>} Whether the file was deleted successfully
 */
export const deleteFile = async (storagePath) => {
  try {
    // Delete from Spaces
    const spacesResult = await deleteFromSpaces(storagePath);

    // Return the result
    return spacesResult;
  } catch (error) {
    console.error('Error deleting file from Spaces:', error);
    return false;
  }
};

/**
 * Extracts text content from a file for search indexing
 * @param {string} filePath - The storage path in Spaces
 * @param {string} mimeType - The MIME type of the file
 * @returns {Promise<string|null>} The extracted text or null if extraction failed
 */
export const extractTextContent = async (filePath, mimeType) => {
  try {
    let dataBuffer;

    // Get the file from Spaces
    try {
      const fileExists = await fileExistsInSpaces(filePath);
      if (fileExists) {
        dataBuffer = await downloadFromSpaces(filePath);
      } else {
        console.error(`File not found in Spaces: ${filePath}`);
        return null;
      }
    } catch (error) {
      console.error('Error downloading file from Spaces:', error);
      return null;
    }

    if (mimeType === 'application/pdf') {
      const pdfData = await pdfParse(dataBuffer);
      return pdfData.text;
    } else if (mimeType === 'text/plain') {
      return dataBuffer.toString('utf8');
    } else if (mimeType.startsWith('image/')) {
      // For images, we could use OCR in the future
      return null;
    } else {
      // For other file types, we don't extract text yet
      return null;
    }
  } catch (error) {
    console.error('Error extracting text content:', error);
    return null;
  }
};

/**
 * Reads a file's content as text
 * @param {string} filePath - The full path to the file
 * @returns {Promise<string>} The file content as text
 */
export const readFileContent = async (filePath) => {
  try {
    const data = await readFile(filePath);
    return data.toString('utf8');
  } catch (error) {
    console.error('Error reading file content:', error);
    throw error;
  }
};

/**
 * Checks if a farm has enough storage quota for a new file
 * @param {string} farmId - The farm ID
 * @param {number} fileSize - The size of the file in bytes
 * @param {boolean} isGlobalAdmin - Whether the user is a global admin
 * @returns {Promise<{allowed: boolean, reason: string|null, currentUsage: number, quota: number}>}
 */
export const checkStorageQuota = async (farmId, fileSize, isGlobalAdmin = false) => {
  try {
    if (!farmId) {
      return {
        allowed: false,
        reason: 'Invalid farm ID',
        currentUsage: 0,
        quota: 0
      };
    }

    // Bypass quota check for global admins
    if (isGlobalAdmin) {
      return {
        allowed: true,
        reason: null,
        currentUsage: 0,
        quota: 0,
        bypassedAsAdmin: true
      };
    }

    // Get farm's subscription plan directly
    const farm = await Farm.findByPk(farmId, {
      include: [{ model: SubscriptionPlan }]
    });

    if (!farm) {
      return {
        allowed: false,
        reason: 'Farm not found',
        currentUsage: 0,
        quota: 0
      };
    }

    if (!farm.SubscriptionPlan) {
      return {
        allowed: false,
        reason: 'Farm has no active subscription plan',
        currentUsage: 0,
        quota: 0
      };
    }

    // Get storage quota from subscription plan (convert GB to bytes)
    const storageQuotaBytes = farm.SubscriptionPlan.storage_quota_gb * 1024 * 1024 * 1024;

    // Get current storage usage
    let storageUsage;
    try {
      storageUsage = await FarmStorageUsage.findOne({
        where: { farm_id: farmId }
      });
    } catch (usageError) {
      console.error('Error retrieving storage usage:', usageError);
      return {
        allowed: false,
        reason: 'Error retrieving storage usage information',
        currentUsage: 0,
        quota: storageQuotaBytes
      };
    }

    if (!storageUsage) {
      // Create storage usage record if it doesn't exist
      try {
        storageUsage = await FarmStorageUsage.create({
          farm_id: farmId,
          total_bytes_used: 0,
          document_count: 0,
          external_document_count: 0,
          last_calculated_at: new Date()
        });
      } catch (createError) {
        console.error('Error creating storage usage record:', createError);
        return {
          allowed: false,
          reason: 'Error initializing storage usage tracking',
          currentUsage: 0,
          quota: storageQuotaBytes
        };
      }
    }

    // Check if adding the new file would exceed the quota
    const newTotalUsage = storageUsage.total_bytes_used + fileSize;

    if (newTotalUsage > storageQuotaBytes) {
      return {
        allowed: false,
        reason: 'Storage quota exceeded',
        currentUsage: storageUsage.total_bytes_used,
        quota: storageQuotaBytes
      };
    }

    // Check if file size exceeds max file size
    const maxFileSizeBytes = farm.SubscriptionPlan.max_file_size_mb * 1024 * 1024;

    if (fileSize > maxFileSizeBytes) {
      return {
        allowed: false,
        reason: 'File size exceeds maximum allowed size',
        currentUsage: storageUsage.total_bytes_used,
        quota: storageQuotaBytes
      };
    }

    return {
      allowed: true,
      reason: null,
      currentUsage: storageUsage.total_bytes_used,
      quota: storageQuotaBytes
    };
  } catch (error) {
    console.error('Error checking storage quota:', error);
    // Provide more specific error message based on the error type
    let errorReason = 'Error checking storage quota';

    if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      errorReason = 'Database connection error while checking storage quota';
    } else if (error.name === 'SequelizeForeignKeyConstraintError') {
      errorReason = 'Data integrity error while checking storage quota';
    } else if (error.name === 'SequelizeValidationError') {
      errorReason = 'Data validation error while checking storage quota';
    }

    return {
      allowed: false,
      reason: errorReason,
      currentUsage: 0,
      quota: 0,
      error: error.message // Include the actual error message for debugging
    };
  }
};

/**
 * Updates the storage usage for a farm
 * @param {string} farmId - The farm ID
 * @param {number} bytesAdded - The number of bytes added (positive) or removed (negative)
 * @param {boolean} isExternal - Whether the document is external
 * @param {number} countChange - The change in document count (1 for add, -1 for remove)
 * @returns {Promise<boolean>} Whether the update was successful
 */
export const updateStorageUsage = async (farmId, bytesAdded, isExternal, countChange) => {
  try {
    let storageUsage = await FarmStorageUsage.findOne({
      where: { farm_id: farmId }
    });

    if (!storageUsage) {
      // Create storage usage record if it doesn't exist
      storageUsage = await FarmStorageUsage.create({
        farm_id: farmId,
        total_bytes_used: Math.max(0, bytesAdded), // Ensure non-negative
        document_count: Math.max(0, countChange), // Ensure non-negative
        external_document_count: isExternal ? Math.max(0, countChange) : 0, // Ensure non-negative
        last_calculated_at: new Date()
      });
    } else {
      // Update existing record
      const newBytesUsed = Math.max(0, storageUsage.total_bytes_used + bytesAdded);
      const newDocCount = Math.max(0, storageUsage.document_count + countChange);
      const newExternalDocCount = Math.max(
        0, 
        storageUsage.external_document_count + (isExternal ? countChange : 0)
      );

      await storageUsage.update({
        total_bytes_used: newBytesUsed,
        document_count: newDocCount,
        external_document_count: newExternalDocCount,
        last_calculated_at: new Date()
      });
    }

    return true;
  } catch (error) {
    console.error('Error updating storage usage:', error);
    return false;
  }
};

/**
 * Recalculates storage usage for a farm from scratch
 * @param {string} farmId - The farm ID
 * @returns {Promise<boolean>} Whether the recalculation was successful
 */
export const recalculateStorageUsage = async (farmId) => {
  try {
    // Get all documents for the farm
    const documents = await Document.findAll({
      where: { farm_id: farmId }
    });

    // Calculate total bytes used and document counts
    let totalBytesUsed = 0;
    let documentCount = documents.length;
    let externalDocumentCount = 0;

    for (const doc of documents) {
      if (doc.is_external) {
        externalDocumentCount++;
      } else {
        totalBytesUsed += doc.file_size;
      }
    }

    // Update or create storage usage record
    let storageUsage = await FarmStorageUsage.findOne({
      where: { farm_id: farmId }
    });

    if (!storageUsage) {
      storageUsage = await FarmStorageUsage.create({
        farm_id: farmId,
        total_bytes_used: totalBytesUsed,
        document_count: documentCount,
        external_document_count: externalDocumentCount,
        last_calculated_at: new Date()
      });
    } else {
      await storageUsage.update({
        total_bytes_used: totalBytesUsed,
        document_count: documentCount,
        external_document_count: externalDocumentCount,
        last_calculated_at: new Date()
      });
    }

    return true;
  } catch (error) {
    console.error('Error recalculating storage usage:', error);
    return false;
  }
};

/**
 * Saves an encrypted file to Digital Ocean Spaces
 * @param {Buffer|string} fileData - The file data or path to temp file
 * @param {string} storagePath - The storage path in Spaces
 * @returns {Promise<{storagePath: string, encryptionDetails: {key: string, iv: string, method: string}}>}
 */
export const saveEncryptedFile = async (fileData, storagePath) => {
  try {
    // Encrypt the file
    const { encryptedData, key, iv, method } = await encryptFile(fileData);

    // Save the encrypted data to Spaces
    await uploadToSpaces(encryptedData, storagePath);

    return {
      storagePath,
      encryptionDetails: {
        key,
        iv,
        method
      }
    };
  } catch (error) {
    console.error('Error saving encrypted file to Spaces:', error);
    throw new Error(`Failed to upload encrypted file to Digital Ocean Spaces: ${error.message}`);
  }
};

/**
 * Retrieves and decrypts a file from Digital Ocean Spaces
 * @param {string} storagePath - The storage path in Spaces
 * @param {string} encryptedKey - The encrypted key
 * @param {string} iv - The initialization vector
 * @param {string} method - The encryption method used
 * @returns {Promise<Buffer>} The decrypted file data
 */
export const retrieveAndDecryptFile = async (storagePath, encryptedKey, iv, method) => {
  try {
    let encryptedData;

    // Get the file from Spaces
    try {
      const fileExists = await fileExistsInSpaces(storagePath);
      if (fileExists) {
        encryptedData = await downloadFromSpaces(storagePath);
      } else {
        throw new Error(`File not found in Spaces: ${storagePath}`);
      }
    } catch (error) {
      console.error('Error downloading file from Spaces:', error);
      throw error;
    }

    // Decrypt the file
    const decryptedData = await decryptFile(encryptedData, encryptedKey, iv, method);

    return decryptedData;
  } catch (error) {
    console.error('Error retrieving and decrypting file:', error);
    throw error;
  }
};

export default {
  validateFileType,
  generateStoragePath,
  saveFile,
  saveEncryptedFile,
  deleteFile,
  extractTextContent,
  readFileContent,
  checkStorageQuota,
  updateStorageUsage,
  recalculateStorageUsage,
  retrieveAndDecryptFile,
  ALLOWED_FILE_TYPES,
  DANGEROUS_EXTENSIONS
};
