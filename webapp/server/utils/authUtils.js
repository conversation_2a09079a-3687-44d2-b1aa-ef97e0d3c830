import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

/**
 * Generate an authentication code for invoice access
 * 
 * @param {string} invoiceId - The invoice ID
 * @param {string} customerId - The customer ID
 * @param {string} farmId - The farm ID
 * @returns {string} - The generated auth code
 */
export const generateInvoiceAuthCode = (invoiceId, customerId, farmId) => {
  const payload = {
    invoiceId,
    customerId,
    farmId,
    type: 'invoice_access',
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 30) // 30 days expiration
  };

  return jwt.sign(payload, JWT_SECRET);
};

/**
 * Verify an invoice authentication code
 * 
 * @param {string} authCode - The auth code to verify
 * @returns {Object|null} - The decoded payload if valid, null otherwise
 */
export const verifyInvoiceAuthCode = (authCode) => {
  try {
    const decoded = jwt.verify(authCode, JWT_SECRET);
    
    // Check if the token is for invoice access
    if (decoded.type !== 'invoice_access') {
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('Error verifying invoice auth code:', error);
    return null;
  }
};