import dotenv from 'dotenv';

dotenv.config();

// Default schema from environment variables
const DEFAULT_SCHEMA = process.env.DB_SCHEMA || 'site';

// Store the current schema callback
let currentSchemaCallback = () => DEFAULT_SCHEMA;

/**
 * Set the callback function that will be used to determine the schema for models
 * @param {Function} callback - A function that returns the schema name to use
 */
export const setSchemaCallback = (callback) => {
  if (typeof callback !== 'function') {
    throw new Error('Schema callback must be a function');
  }
  currentSchemaCallback = callback;
};

/**
 * Get the current schema based on the callback
 * @param {Object} options - Optional parameters that might influence schema selection
 * @param {string} options.farmId - The farm ID if available
 * @returns {string} The schema name to use
 */
export const getSchema = (options = {}) => {
  return currentSchemaCallback(options);
};

/**
 * Reset the schema callback to use the default schema
 */
export const resetSchemaCallback = () => {
  currentSchemaCallback = () => DEFAULT_SCHEMA;
};

/**
 * Create a farm-specific schema callback
 * @param {Function} farmSchemaResolver - A function that resolves a farm ID to a schema name
 * @returns {Function} A callback function that can be used with setSchemaCallback
 */
export const createFarmSchemaCallback = (farmSchemaResolver) => {
  return (options = {}) => {
    if (options.farmId) {
      const farmSchema = farmSchemaResolver(options.farmId);
      if (farmSchema) {
        return farmSchema;
      }
    }
    return DEFAULT_SCHEMA;
  };
};

// Initialize with default schema callback
resetSchemaCallback();