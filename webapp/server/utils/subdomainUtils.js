import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import profanity from 'leo-profanity';

// Initialize profanity filter
profanity.loadDictionary();

// Add additional words to the filter
profanity.add([
  'inappropriate', 'offensive', 'explicit', 
  'obscene', 'vulgar', 'profane', 'lewd', 'crude',
  'indecent', 'rude', 'nasty', 'vile', 'filthy',
  'dirty', 'sleazy', 'trashy', 'smutty', 'naughty',
  'risque', 'racy', 'bawdy', 'coarse', 'crass',
  'improper', 'unsuitable', 'objectionable', 'unacceptable',
  'distasteful', 'unseemly', 'unbecoming', 'shameful',
  'scandalous', 'shocking', 'outrageous', 'provocative',
  'suggestive', 'adult', 'mature', 'nsfw', 'xxx',
  'porn', 'porno', 'pornographic', 'erotic', 'sexual',
  'sensual', 'seductive', 'lascivious', 'licentious',
  'lustful', 'lecherous', 'salacious', 'prurient',
  'depraved', 'perverted', 'degenerate', 'immoral',
  'sinful', 'wicked', 'evil', 'corrupt', 'debauched',
  'debauchery', 'vice', 'vicious', 'malicious', 'harmful',
  'hurtful', 'hateful', 'spiteful', 'vengeful', 'violent',
  'abusive', 'aggressive', 'hostile', 'antagonistic',
  'belligerent', 'combative', 'confrontational', 'threatening',
  'menacing', 'intimidating', 'bullying', 'harassing',
  'discriminatory', 'prejudiced', 'biased', 'racist',
  'sexist', 'homophobic', 'transphobic', 'xenophobic',
  'bigoted', 'intolerant', 'supremacist'
]);

/**
 * Generate a default subdomain from a farm name
 * @param {string} farmName - The name of the farm
 * @returns {string} - A subdomain-compatible string
 */
export const generateSubdomainFromName = (farmName) => {
  if (!farmName) return '';

  // Convert to lowercase, replace spaces with hyphens, and remove special characters
  return farmName
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '')
    .replace(/-+/g, '-') // Replace multiple hyphens with a single one
    .replace(/^-|-$/g, ''); // Remove leading and trailing hyphens
};

/**
 * Check if a subdomain contains profanity or inappropriate words
 * @param {string} subdomain - The subdomain to check
 * @returns {boolean} - True if the subdomain is clean, false if it contains profanity
 */
export const isSubdomainClean = (subdomain) => {
  if (!subdomain) return false;

  // Convert to lowercase for case-insensitive checking
  const lowerSubdomain = subdomain.toLowerCase();

  // Also create a version with hyphens removed to catch split profanities
  const noHyphensSubdomain = lowerSubdomain.replace(/-/g, '');

  // Check if the subdomain contains any profanity as a whole
  if (profanity.check(lowerSubdomain) || profanity.check(noHyphensSubdomain)) {
    return false;
  }

  // Get the list of bad words to check for partial matches
  const badWords = profanity.list();
  const customWords = [
    'inappropriate', 'offensive', 'explicit', 
    'obscene', 'vulgar', 'profane', 'lewd', 'crude',
    'indecent', 'rude', 'nasty', 'vile', 'filthy',
    'dirty', 'sleazy', 'trashy', 'smutty', 'naughty',
    'risque', 'racy', 'bawdy', 'coarse', 'crass',
    'improper', 'unsuitable', 'objectionable', 'unacceptable',
    'distasteful', 'unseemly', 'unbecoming', 'shameful',
    'scandalous', 'shocking', 'outrageous', 'provocative',
    'suggestive', 'adult', 'mature', 'nsfw', 'xxx',
    'porn', 'porno', 'pornographic', 'erotic', 'sexual',
    'sensual', 'seductive', 'lascivious', 'licentious',
    'lustful', 'lecherous', 'salacious', 'prurient',
    'depraved', 'perverted', 'degenerate', 'immoral',
    'sinful', 'wicked', 'evil', 'corrupt', 'debauched',
    'debauchery', 'vice', 'vicious', 'malicious', 'harmful',
    'hurtful', 'hateful', 'spiteful', 'vengeful', 'violent',
    'abusive', 'aggressive', 'hostile', 'antagonistic',
    'belligerent', 'combative', 'confrontational', 'threatening',
    'menacing', 'intimidating', 'bullying', 'harassing',
    'discriminatory', 'prejudiced', 'biased', 'racist',
    'sexist', 'homophobic', 'transphobic', 'xenophobic',
    'bigoted', 'intolerant', 'supremacist'
  ];

  // Combine the default list with our custom list
  const allBadWords = [...badWords, ...customWords];

  // Check for partial matches (words embedded within the subdomain)
  for (const word of allBadWords) {
    if (word.length >= 3) {
      // Check both the original subdomain and the version with hyphens removed
      if (lowerSubdomain.includes(word.toLowerCase()) || 
          noHyphensSubdomain.includes(word.toLowerCase())) {
        return false;
      }
    }
  }

  return true;
};

/**
 * Check if a subdomain is available (not already in use)
 * @param {string} subdomain - The subdomain to check
 * @param {string} [excludeFarmId] - Optional farm ID to exclude from the check (for updates)
 * @returns {Promise<boolean>} - True if the subdomain is available, false if it's already in use
 */
export const isSubdomainAvailable = async (subdomain, excludeFarmId) => {
  if (!subdomain) return false;

  // Build the query conditions
  const whereCondition = {
    subdomain: {
      [Op.iLike]: subdomain // Case-insensitive comparison
    }
  };

  // If excludeFarmId is provided, exclude that farm from the check
  if (excludeFarmId) {
    whereCondition.id = {
      [Op.ne]: excludeFarmId // Not equal to excludeFarmId
    };
  }

  // Check if the subdomain is already in use by another farm
  const existingFarm = await Farm.findOne({
    where: whereCondition
  });

  return !existingFarm;
};

/**
 * Generate a unique subdomain based on the farm name
 * If the subdomain is already taken, append a number to make it unique
 * @param {string} farmName - The name of the farm
 * @returns {Promise<string>} - A unique subdomain
 */
export const generateUniqueSubdomain = async (farmName) => {
  let subdomain = generateSubdomainFromName(farmName);

  // Check if the subdomain is clean
  if (!isSubdomainClean(subdomain)) {
    // If not clean, use a generic subdomain
    subdomain = 'farm';
  }

  // Check if the subdomain is available
  let isAvailable = await isSubdomainAvailable(subdomain);
  let counter = 1;

  // If the subdomain is not available, append a number to make it unique
  while (!isAvailable) {
    const newSubdomain = `${subdomain}-${counter}`;
    isAvailable = await isSubdomainAvailable(newSubdomain);

    if (isAvailable) {
      subdomain = newSubdomain;
    } else {
      counter++;
    }
  }

  return subdomain;
};

/**
 * Validate a subdomain
 * @param {string} subdomain - The subdomain to validate
 * @returns {Object} - An object with isValid and message properties
 */
export const validateSubdomain = (subdomain) => {
  if (!subdomain) {
    return { isValid: false, message: 'Subdomain is required' };
  }

  // Check if the subdomain is in the correct format
  if (!/^[a-z0-9-]+$/i.test(subdomain)) {
    return { isValid: false, message: 'Subdomain can only contain letters, numbers, and hyphens' };
  }

  // Check if the subdomain is too short or too long
  if (subdomain.length < 3) {
    return { isValid: false, message: 'Subdomain must be at least 3 characters long' };
  }

  if (subdomain.length > 50) {
    return { isValid: false, message: 'Subdomain must be at most 50 characters long' };
  }

  // Check if the subdomain is a reserved word or sensitive route
  const reservedWords = [
    // System and administrative routes
    'admin', 'www', 'api', 'mail', 'smtp', 'pop', 'ftp', 'webmail', 'support', 'help',
    // Authentication and security related routes
    'login', 'auth', 'pass', 'password', 'sso', 'security', 'secure', 'account', 'accounts',
    // Common service names that could be misleading
    'billing', 'payment', 'paypal', 'stripe', 'checkout', 'pay',
    // Email related
    'email', 'webmail', 'outlook', 'gmail',
    // Other potentially sensitive or misleading routes
    'dashboard', 'portal', 'system', 'internal', 'private', 'public', 'static',
    'reset', 'recover', 'verification', 'verify', 'confirm', 'signup', 'register'
  ];
  if (reservedWords.includes(subdomain.toLowerCase())) {
    return { isValid: false, message: 'This subdomain is reserved and cannot be used' };
  }

  // Check if the subdomain contains profanity
  if (!isSubdomainClean(subdomain)) {
    return { isValid: false, message: 'Subdomain contains inappropriate language' };
  }

  return { isValid: true, message: 'Subdomain is valid' };
};
