/**
 * Utility functions for subscription validation and status checking
 */
import Farm from '../models/Farm.js';
import SubscriptionPlan from '../models/SubscriptionPlan.js';
import UserFarm from '../models/UserFarm.js';

/**
 * Check if a farm's subscription is valid
 * @param {string} farmId - The farm ID to check
 * @returns {Promise<Object>} - Object containing subscription status information
 */
export const checkSubscriptionStatus = async (farmId) => {
  try {
    // Get farm with subscription plan
    const farm = await Farm.findByPk(farmId, {
      include: [{ model: SubscriptionPlan }]
    });

    if (!farm) {
      return {
        isValid: false,
        error: 'Farm not found'
      };
    }

    const now = new Date();
    const subscriptionEndDate = farm.subscription_end_date ? new Date(farm.subscription_end_date) : null;
    
    // Check if subscription is expired
    const isExpired = subscriptionEndDate && subscriptionEndDate < now;
    
    // Check if subscription is expiring soon (within 7 days)
    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
    const isExpiringSoon = subscriptionEndDate && subscriptionEndDate > now && subscriptionEndDate < sevenDaysFromNow;
    
    // Check if this is a trial subscription
    const isTrial = farm.SubscriptionPlan?.is_trial || false;
    
    // Check if trial is ending soon (within 3 days)
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    const isTrialEndingSoon = isTrial && subscriptionEndDate && subscriptionEndDate > now && subscriptionEndDate < threeDaysFromNow;

    // Check if auto-pay is enabled (assuming payment_method_id indicates auto-pay)
    const hasAutoPayEnabled = !!farm.payment_method_id;

    return {
      isValid: !isExpired,
      isExpired,
      isExpiringSoon,
      isTrial,
      isTrialEndingSoon,
      hasAutoPayEnabled,
      subscriptionEndDate,
      subscriptionStatus: farm.subscription_status
    };
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return {
      isValid: false,
      error: error.message
    };
  }
};

/**
 * Check if a user has billing permissions for a farm
 * @param {string} userId - The user ID to check
 * @param {string} farmId - The farm ID to check
 * @returns {Promise<boolean>} - Whether the user has billing permissions
 */
export const hasBillingPermissions = async (userId, farmId) => {
  try {
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: userId,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      return false;
    }

    // Farm owners, farm admins, and users with is_billing_contact=true have billing permissions
    return userFarm.role === 'farm_owner' || 
           userFarm.role === 'farm_admin' || 
           userFarm.is_billing_contact === true;
  } catch (error) {
    console.error('Error checking billing permissions:', error);
    return false;
  }
};

/**
 * Check if a user is an employee or accountant for a farm
 * @param {string} userId - The user ID to check
 * @param {string} farmId - The farm ID to check
 * @returns {Promise<boolean>} - Whether the user is an employee or accountant
 */
export const isEmployeeOrAccountant = async (userId, farmId) => {
  try {
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: userId,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      return false;
    }

    return userFarm.role === 'farm_employee' || userFarm.role === 'accountant';
  } catch (error) {
    console.error('Error checking if user is employee or accountant:', error);
    return false;
  }
};