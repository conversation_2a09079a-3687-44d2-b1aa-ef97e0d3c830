import Field from '../models/Field.js';
import Farm from '../models/Farm.js';
import FieldSoilData from '../models/FieldSoilData.js';
import ConservationPractice from '../models/ConservationPractice.js';
import CropRotation from '../models/CropRotation.js';
import Harvest from '../models/Harvest.js';
import Crop from '../models/Crop.js';
import YieldPrediction from '../models/YieldPrediction.js';
import HarvestDirectionMap from '../models/HarvestDirectionMap.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import axios from 'axios';
import dotenv from 'dotenv';
import NodeCache from 'node-cache';

dotenv.config();

// Initialize cache with standard TTL of 10 minutes and check period of 60 seconds
const cache = new NodeCache({ stdTTL: 600, checkperiod: 60 });

// Get the data.gov API URLs and keys from environment variables
const NRCS_API_URL = process.env.NRCS_API_URL || 'https://api.nrcs.usda.gov/v1';
const NRCS_API_KEY = process.env.NRCS_API_KEY;

// Get all fields for a farm
export const getFarmFields = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all fields for the farm
    const fields = await Field.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    return res.status(200).json(fields);
  } catch (error) {
    console.error('Error getting farm fields:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single field by ID
export const getFieldById = async (req, res) => {
  try {
    const { fieldId } = req.params;

    const field = await Field.findByPk(fieldId);

    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    return res.status(200).json(field);
  } catch (error) {
    console.error('Error getting field:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new field
export const createField = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farm_id, 
      name, 
      size, 
      size_unit, 
      field_type, 
      crop_type, 
      status, 
      location_data, 
      notes 
    } = req.body;

    // Validate required fields
    if (!farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Field name is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farm_id);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create field
    const field = await Field.create({
      farm_id,
      name,
      size,
      size_unit: size_unit || 'acres',
      field_type,
      crop_type,
      status: status || 'active',
      location_data,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Field created successfully',
      field 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating field:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a field
export const updateField = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { 
      name, 
      size, 
      size_unit, 
      field_type, 
      crop_type, 
      status, 
      location_data, 
      notes 
    } = req.body;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Update field
    await field.update({
      name: name || field.name,
      size: size !== undefined ? size : field.size,
      size_unit: size_unit || field.size_unit,
      field_type: field_type !== undefined ? field_type : field.field_type,
      crop_type: crop_type !== undefined ? crop_type : field.crop_type,
      status: status || field.status,
      location_data: location_data || field.location_data,
      notes: notes !== undefined ? notes : field.notes
    });

    return res.status(200).json({ 
      message: 'Field updated successfully',
      field 
    });
  } catch (error) {
    console.error('Error updating field:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a field
export const deleteField = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Field not found' });
    }

    // Delete field
    await field.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Field deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting field:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update crop type for a field
export const updateFieldCropType = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { crop_type } = req.body;

    // Validate required fields
    if (!crop_type) {
      return res.status(400).json({ error: 'Crop type is required' });
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Update field crop type
    await field.update({
      crop_type
    });

    return res.status(200).json({ 
      message: 'Field crop type updated successfully',
      field 
    });
  } catch (error) {
    console.error('Error updating field crop type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all fields with a specific crop type
export const getFieldsByCropType = async (req, res) => {
  try {
    const { farmId, cropType } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all fields for the farm with the specified crop type
    const fields = await Field.findAll({
      where: { 
        farm_id: farmId,
        crop_type: cropType
      },
      order: [['name', 'ASC']]
    });

    return res.status(200).json(fields);
  } catch (error) {
    console.error('Error getting fields by crop type:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get field data from data.gov for a specific field
export const getFieldDataGov = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Create a cache key based on the field ID
    const cacheKey = `field_data_${fieldId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached field data for field ${fieldId}`);
      return res.status(200).json(cachedData);
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Try to get field soil data from the database
    const fieldSoilData = await FieldSoilData.findOne({
      where: { field_id: fieldId }
    });

    // Get conservation practices from the database
    const conservationPractices = await ConservationPractice.findAll({
      where: { field_id: fieldId },
      attributes: ['practice_name']
    });

    // Get recommended crops from crop rotations
    const cropRotation = await CropRotation.findOne({
      where: { field_id: fieldId },
      order: [['created_at', 'DESC']]
    });

    // Get historical yield data from harvests
    const harvests = await Harvest.findAll({
      where: { 
        field_id: fieldId,
        status: 'completed',
        yield_amount: { [Op.not]: null }
      },
      include: [{
        model: Crop,
        as: 'harvestCrop',
        attributes: ['name']
      }],
      order: [['actual_date', 'DESC']],
      limit: 5
    });

    // If we have data in the database, use it
    if (fieldSoilData) {
      // Transform the data to match our FieldDataGov interface
      const fieldData = {
        field_id: fieldId,
        soil_type: fieldSoilData.soil_type,
        soil_health_index: fieldSoilData.soil_health_index || 0,
        conservation_practices: conservationPractices.map(cp => cp.practice_name),
        land_capability_class: fieldSoilData.land_capability_class || 'Unknown',
        erosion_risk: fieldSoilData.erosion_risk || 'Unknown',
        water_availability: fieldSoilData.water_availability || 'Unknown',
        recommended_crops: cropRotation ? cropRotation.recommended_sequence : [],
        historical_yield_data: harvests.map(harvest => ({
          year: new Date(harvest.actual_date).getFullYear(),
          crop: harvest.harvestCrop ? harvest.harvestCrop.name : 'Unknown',
          yield: parseFloat(harvest.yield_amount),
          unit: harvest.yield_unit
        })),
        last_updated: fieldSoilData.last_updated || new Date().toISOString()
      };

      // Store in cache
      cache.set(cacheKey, fieldData);
      console.log(`Cached field data for field ${fieldId}`);

      return res.status(200).json(fieldData);
    }

    // If no data in database, try to get it from NRCS API
    try {
      // Create NRCS API client
      const nrcsClient = axios.create({
        baseURL: NRCS_API_URL,
        headers: {
          'Authorization': `Bearer ${NRCS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Get field data from NRCS API using SOAP requests
      // First, check if we have location data for the field
      if (!field.location_data?.center?.latitude || !field.location_data?.center?.longitude) {
        throw new Error('Field location data is missing or invalid');
      }

      const latitude = field.location_data.center.latitude;
      const longitude = field.location_data.center.longitude;

      // First, get the soil survey area (SSA) for the given coordinates
      const ssaXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetSoilSurveyAreaByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetSoilSurveyAreaByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const ssaResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', ssaXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil survey area ID
      const ssaXmlResponse = ssaResponse.data;
      // Extract areasymbol from XML response (simplified parsing for example)
      const areaSymbolMatch = ssaXmlResponse.match(/<areasymbol>(.*?)<\/areasymbol>/);
      const areaSymbol = areaSymbolMatch ? areaSymbolMatch[1] : null;

      if (!areaSymbol) {
        throw new Error('No soil survey area found for the given coordinates.');
      }

      // Now get the soil map unit for the given coordinates
      const muXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetMapUnitByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetMapUnitByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const muResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', muXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the map unit key
      const muXmlResponse = muResponse.data;
      // Extract mukey from XML response (simplified parsing for example)
      const mukeyMatch = muXmlResponse.match(/<mukey>(.*?)<\/mukey>/);
      const mukey = mukeyMatch ? mukeyMatch[1] : null;

      if (!mukey) {
        throw new Error('No map unit found for the given coordinates.');
      }

      // Now get the soil properties for the map unit
      const propertiesXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <RunQuery xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <Query>
                SELECT 
                  component.mukey, 
                  component.cokey, 
                  component.compname, 
                  component.comppct_r, 
                  component.majcompflag, 
                  component.taxclname, 
                  component.taxorder, 
                  component.taxsuborder, 
                  component.taxgrtgroup, 
                  component.drainagecl, 
                  chorizon.hzname, 
                  chorizon.hzdept_r, 
                  chorizon.hzdepb_r, 
                  chorizon.ph1to1h2o_r, 
                  chorizon.om_r, 
                  chorizon.cec7_r, 
                  chorizon.sandtotal_r, 
                  chorizon.silttotal_r, 
                  chorizon.claytotal_r, 
                  chorizon.awc_r, 
                  copmgrp.flodfreqcl, 
                  copmgrp.floddurcl, 
                  copmgrp.pondfreqcl, 
                  copmgrp.ponddurcl, 
                  copmgrp.droughty, 
                  copmgrp.hydgrp
                FROM component 
                LEFT JOIN chorizon ON component.cokey = chorizon.cokey 
                LEFT JOIN copmgrp ON component.cokey = copmgrp.cokey 
                WHERE component.mukey = '${mukey}'
                ORDER BY component.comppct_r DESC, chorizon.hzdept_r ASC
              </Query>
            </RunQuery>
          </soap12:Body>
        </soap12:Envelope>`;

      const propertiesResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', propertiesXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil properties
      const propertiesXmlResponse = propertiesResponse.data;

      // Extract soil properties from XML response (simplified parsing for example)
      const soilTypeMatch = propertiesXmlResponse.match(/<compname>(.*?)<\/compname>/);
      const soilType = soilTypeMatch ? soilTypeMatch[1] : 'Unknown';

      const drainageClassMatch = propertiesXmlResponse.match(/<drainagecl>(.*?)<\/drainagecl>/);
      const drainageClass = drainageClassMatch ? drainageClassMatch[1] : 'Unknown';

      const phMatch = propertiesXmlResponse.match(/<ph1to1h2o_r>(.*?)<\/ph1to1h2o_r>/);
      const ph = phMatch ? parseFloat(phMatch[1]) : null;

      const organicMatterMatch = propertiesXmlResponse.match(/<om_r>(.*?)<\/om_r>/);
      const organicMatter = organicMatterMatch ? parseFloat(organicMatterMatch[1]) : null;

      const sandContentMatch = propertiesXmlResponse.match(/<sandtotal_r>(.*?)<\/sandtotal_r>/);
      const sandContent = sandContentMatch ? parseFloat(sandContentMatch[1]) : null;

      const siltContentMatch = propertiesXmlResponse.match(/<silttotal_r>(.*?)<\/silttotal_r>/);
      const siltContent = siltContentMatch ? parseFloat(siltContentMatch[1]) : null;

      const clayContentMatch = propertiesXmlResponse.match(/<claytotal_r>(.*?)<\/claytotal_r>/);
      const clayContent = clayContentMatch ? parseFloat(clayContentMatch[1]) : null;

      const awcMatch = propertiesXmlResponse.match(/<awc_r>(.*?)<\/awc_r>/);
      const awc = awcMatch ? parseFloat(awcMatch[1]) : null;

      const floodFrequencyMatch = propertiesXmlResponse.match(/<flodfreqcl>(.*?)<\/flodfreqcl>/);
      const floodFrequency = floodFrequencyMatch ? floodFrequencyMatch[1] : 'Unknown';

      // Determine erosion risk based on soil properties
      let erosionRisk = 'Unknown';
      if (clayContent !== null && sandContent !== null) {
        if (clayContent > 35) {
          erosionRisk = 'Low'; // Clay soils are less prone to erosion
        } else if (sandContent > 70) {
          erosionRisk = 'High'; // Sandy soils are more prone to erosion
        } else {
          erosionRisk = 'Medium';
        }
      }

      // Determine water availability based on AWC and drainage class
      let waterAvailability = 'Unknown';
      if (awc !== null) {
        if (awc > 0.2) {
          waterAvailability = 'High';
        } else if (awc > 0.1) {
          waterAvailability = 'Medium';
        } else {
          waterAvailability = 'Low';
        }
      } else if (drainageClass) {
        if (drainageClass.includes('poor')) {
          waterAvailability = 'High';
        } else if (drainageClass.includes('well')) {
          waterAvailability = 'Medium';
        } else {
          waterAvailability = 'Low';
        }
      }

      // Calculate soil health index based on organic matter, pH, and other factors
      let soilHealthIndex = 0;
      if (organicMatter !== null) {
        // Organic matter contributes up to 40 points
        soilHealthIndex += Math.min(organicMatter * 10, 40);
      }

      if (ph !== null) {
        // pH contributes up to 30 points (optimal pH is around 6.5)
        const phScore = 30 - Math.abs(ph - 6.5) * 10;
        soilHealthIndex += Math.max(phScore, 0);
      }

      // Drainage contributes up to 30 points
      if (drainageClass) {
        if (drainageClass.includes('well')) {
          soilHealthIndex += 30;
        } else if (drainageClass.includes('moderate')) {
          soilHealthIndex += 20;
        } else if (drainageClass.includes('poor')) {
          soilHealthIndex += 10;
        }
      }

      // Determine land capability class based on soil properties
      let landCapabilityClass = 'Unknown';
      if (soilHealthIndex > 80) {
        landCapabilityClass = 'Class I';
      } else if (soilHealthIndex > 60) {
        landCapabilityClass = 'Class II';
      } else if (soilHealthIndex > 40) {
        landCapabilityClass = 'Class III';
      } else if (soilHealthIndex > 20) {
        landCapabilityClass = 'Class IV';
      } else {
        landCapabilityClass = 'Class V or higher';
      }

      // Now get recommended crops based on soil properties
      // This would typically come from a separate API call or database lookup
      // For now, we'll determine based on soil properties
      let recommendedCrops = [];
      if (soilHealthIndex > 60) {
        recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Alfalfa', 'Vegetables'];
      } else if (soilHealthIndex > 40) {
        recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Oats'];
      } else if (soilHealthIndex > 20) {
        recommendedCrops = ['Wheat', 'Oats', 'Barley', 'Rye'];
      } else {
        recommendedCrops = ['Pasture', 'Hay', 'Conservation Cover'];
      }

      // Now get conservation practices based on soil properties
      let conservationPractices = [];
      if (erosionRisk === 'High') {
        conservationPractices.push('Contour Farming', 'Cover Crops', 'Reduced Tillage');
      } else if (erosionRisk === 'Medium') {
        conservationPractices.push('Cover Crops', 'Crop Rotation');
      }

      if (waterAvailability === 'Low') {
        conservationPractices.push('Irrigation Management', 'Drought-Resistant Crops');
      } else if (waterAvailability === 'High') {
        conservationPractices.push('Drainage Management', 'Raised Beds');
      }

      // Remove duplicates from conservation practices
      conservationPractices = [...new Set(conservationPractices)];

      // Transform the data to match our FieldDataGov interface
      const fieldData = {
        field_id: fieldId,
        soil_type: soilType,
        soil_health_index: soilHealthIndex,
        conservation_practices: conservationPractices,
        land_capability_class: landCapabilityClass,
        erosion_risk: erosionRisk,
        water_availability: waterAvailability,
        recommended_crops: recommendedCrops,
        historical_yield_data: [], // This would come from a separate API call or database
        last_updated: new Date().toISOString()
      };

      // Store the data in the database for future use
      await FieldSoilData.create({
        farm_id: field.farm_id,
        field_id: fieldId,
        soil_type: fieldData.soil_type,
        soil_health_index: fieldData.soil_health_index,
        land_capability_class: fieldData.land_capability_class,
        erosion_risk: fieldData.erosion_risk,
        water_availability: fieldData.water_availability,
        last_updated: new Date()
      });

      // Store conservation practices
      if (fieldData.conservation_practices && fieldData.conservation_practices.length > 0) {
        for (const practice of fieldData.conservation_practices) {
          await ConservationPractice.create({
            farm_id: field.farm_id,
            field_id: fieldId,
            practice_name: practice,
            practice_type: 'soil',
            status: 'active'
          });
        }
      }

      // Store in cache
      cache.set(cacheKey, fieldData);
      console.log(`Cached field data for field ${fieldId}`);

      return res.status(200).json(fieldData);
    } catch (apiError) {
      console.warn('Failed to fetch from NRCS API:', apiError);

      // If the API call fails, return nothing to the user
      return res.status(404).json({ 
        error: 'No field data available', 
        message: 'Could not retrieve field data from the NRCS API'
      });
    }
  } catch (error) {
    console.error('Error getting field data from data.gov:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get field data from data.gov for all fields in a farm
export const getFarmFieldsDataGov = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Create a cache key based on the farm ID
    const cacheKey = `farm_fields_data_${farmId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached farm fields data for farm ${farmId}`);
      return res.status(200).json(cachedData);
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all fields for the farm
    const fields = await Field.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    // Get field soil data for all fields
    const fieldSoilData = await FieldSoilData.findAll({
      where: { farm_id: farmId }
    });

    // Get conservation practices for all fields
    const conservationPractices = await ConservationPractice.findAll({
      where: { farm_id: farmId },
      attributes: ['field_id', 'practice_name']
    });

    // Get crop rotations for all fields
    const cropRotations = await CropRotation.findAll({
      where: { farm_id: farmId },
      order: [['created_at', 'DESC']]
    });

    // Get harvests for all fields
    const harvests = await Harvest.findAll({
      where: { 
        farm_id: farmId,
        status: 'completed',
        yield_amount: { [Op.not]: null }
      },
      include: [{
        model: Crop,
        as: 'harvestCrop',
        attributes: ['name']
      }],
      order: [['actual_date', 'DESC']]
    });

    // Group harvests by field_id
    const harvestsByField = harvests.reduce((acc, harvest) => {
      if (!acc[harvest.field_id]) {
        acc[harvest.field_id] = [];
      }
      acc[harvest.field_id].push(harvest);
      return acc;
    }, {});

    // Group conservation practices by field_id
    const practicesByField = conservationPractices.reduce((acc, practice) => {
      if (!acc[practice.field_id]) {
        acc[practice.field_id] = [];
      }
      acc[practice.field_id].push(practice.practice_name);
      return acc;
    }, {});

    // Create a map of field_id to crop rotation
    const rotationsByField = cropRotations.reduce((acc, rotation) => {
      if (!acc[rotation.field_id] || new Date(rotation.created_at) > new Date(acc[rotation.field_id].created_at)) {
        acc[rotation.field_id] = rotation;
      }
      return acc;
    }, {});

    // Create a map of field_id to soil data
    const soilDataByField = fieldSoilData.reduce((acc, data) => {
      acc[data.field_id] = data;
      return acc;
    }, {});

    // If we have data for at least some fields, use it
    if (fieldSoilData.length > 0 || conservationPractices.length > 0 || cropRotations.length > 0 || harvests.length > 0) {
      // Transform the data to match our FieldDataGov interface
      const fieldsData = fields.map(field => {
        const soilData = soilDataByField[field.id];
        const practices = practicesByField[field.id] || [];
        const rotation = rotationsByField[field.id];
        const fieldHarvests = harvestsByField[field.id] || [];

        return {
          field_id: field.id,
          soil_type: soilData ? soilData.soil_type : 'Unknown',
          soil_health_index: soilData ? soilData.soil_health_index || 0 : 0,
          conservation_practices: practices,
          land_capability_class: soilData ? soilData.land_capability_class || 'Unknown' : 'Unknown',
          erosion_risk: soilData ? soilData.erosion_risk || 'Unknown' : 'Unknown',
          water_availability: soilData ? soilData.water_availability || 'Unknown' : 'Unknown',
          recommended_crops: rotation ? rotation.recommended_sequence : [],
          historical_yield_data: fieldHarvests.slice(0, 5).map(harvest => ({
            year: new Date(harvest.actual_date).getFullYear(),
            crop: harvest.harvestCrop ? harvest.harvestCrop.name : 'Unknown',
            yield: parseFloat(harvest.yield_amount),
            unit: harvest.yield_unit
          })),
          last_updated: soilData ? soilData.last_updated || new Date().toISOString() : new Date().toISOString()
        };
      });

      // Store in cache
      cache.set(cacheKey, fieldsData);
      console.log(`Cached farm fields data for farm ${farmId}`);

      return res.status(200).json(fieldsData);
    }

    // If no data in database, try to get it from NRCS API
    try {
      // Create NRCS API client
      const nrcsClient = axios.create({
        baseURL: NRCS_API_URL,
        headers: {
          'Authorization': `Bearer ${NRCS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Get field data from NRCS API for all fields
      // We need to make API calls for each field individually
      const fieldsDataPromises = fields.map(async (field) => {
        try {
          // Check if we have location data for the field
          if (!field.location_data?.center?.latitude || !field.location_data?.center?.longitude) {
            console.warn(`Field ${field.id} has missing or invalid location data`);
            return {
              field_id: field.id,
              soil_type: 'Unknown',
              soil_health_index: 0,
              conservation_practices: [],
              land_capability_class: 'Unknown',
              erosion_risk: 'Unknown',
              water_availability: 'Unknown',
              recommended_crops: [],
              historical_yield_data: [],
              last_updated: new Date().toISOString()
            };
          }

          const latitude = field.location_data.center.latitude;
          const longitude = field.location_data.center.longitude;

          // First, get the soil survey area (SSA) for the given coordinates
          const ssaXml = `<?xml version="1.0" encoding="utf-8"?>
            <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
              <soap12:Body>
                <GetSoilSurveyAreaByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
                  <latitude>${latitude}</latitude>
                  <longitude>${longitude}</longitude>
                </GetSoilSurveyAreaByLocation>
              </soap12:Body>
            </soap12:Envelope>`;

          const ssaResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', ssaXml, {
            headers: {
              'Content-Type': 'application/soap+xml',
              'Authorization': `Bearer ${NRCS_API_KEY}`
            }
          });

          // Parse the XML response to get the soil survey area ID
          const ssaXmlResponse = ssaResponse.data;
          // Extract areasymbol from XML response (simplified parsing for example)
          const areaSymbolMatch = ssaXmlResponse.match(/<areasymbol>(.*?)<\/areasymbol>/);
          const areaSymbol = areaSymbolMatch ? areaSymbolMatch[1] : null;

          if (!areaSymbol) {
            console.warn(`No soil survey area found for field ${field.id}`);
            return {
              field_id: field.id,
              soil_type: 'Unknown',
              soil_health_index: 0,
              conservation_practices: [],
              land_capability_class: 'Unknown',
              erosion_risk: 'Unknown',
              water_availability: 'Unknown',
              recommended_crops: [],
              historical_yield_data: [],
              last_updated: new Date().toISOString()
            };
          }

          // Now get the soil map unit for the given coordinates
          const muXml = `<?xml version="1.0" encoding="utf-8"?>
            <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
              <soap12:Body>
                <GetMapUnitByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
                  <latitude>${latitude}</latitude>
                  <longitude>${longitude}</longitude>
                </GetMapUnitByLocation>
              </soap12:Body>
            </soap12:Envelope>`;

          const muResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', muXml, {
            headers: {
              'Content-Type': 'application/soap+xml',
              'Authorization': `Bearer ${NRCS_API_KEY}`
            }
          });

          // Parse the XML response to get the map unit key
          const muXmlResponse = muResponse.data;
          // Extract mukey from XML response (simplified parsing for example)
          const mukeyMatch = muXmlResponse.match(/<mukey>(.*?)<\/mukey>/);
          const mukey = mukeyMatch ? mukeyMatch[1] : null;

          if (!mukey) {
            console.warn(`No map unit found for field ${field.id}`);
            return {
              field_id: field.id,
              soil_type: 'Unknown',
              soil_health_index: 0,
              conservation_practices: [],
              land_capability_class: 'Unknown',
              erosion_risk: 'Unknown',
              water_availability: 'Unknown',
              recommended_crops: [],
              historical_yield_data: [],
              last_updated: new Date().toISOString()
            };
          }

          // Now get the soil properties for the map unit
          const propertiesXml = `<?xml version="1.0" encoding="utf-8"?>
            <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
              <soap12:Body>
                <RunQuery xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
                  <Query>
                    SELECT 
                      component.mukey, 
                      component.cokey, 
                      component.compname, 
                      component.comppct_r, 
                      component.majcompflag, 
                      component.taxclname, 
                      component.taxorder, 
                      component.taxsuborder, 
                      component.taxgrtgroup, 
                      component.drainagecl, 
                      chorizon.hzname, 
                      chorizon.hzdept_r, 
                      chorizon.hzdepb_r, 
                      chorizon.ph1to1h2o_r, 
                      chorizon.om_r, 
                      chorizon.cec7_r, 
                      chorizon.sandtotal_r, 
                      chorizon.silttotal_r, 
                      chorizon.claytotal_r, 
                      chorizon.awc_r, 
                      copmgrp.flodfreqcl, 
                      copmgrp.floddurcl, 
                      copmgrp.pondfreqcl, 
                      copmgrp.ponddurcl, 
                      copmgrp.droughty, 
                      copmgrp.hydgrp
                    FROM component 
                    LEFT JOIN chorizon ON component.cokey = chorizon.cokey 
                    LEFT JOIN copmgrp ON component.cokey = copmgrp.cokey 
                    WHERE component.mukey = '${mukey}'
                    ORDER BY component.comppct_r DESC, chorizon.hzdept_r ASC
                  </Query>
                </RunQuery>
              </soap12:Body>
            </soap12:Envelope>`;

          const propertiesResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', propertiesXml, {
            headers: {
              'Content-Type': 'application/soap+xml',
              'Authorization': `Bearer ${NRCS_API_KEY}`
            }
          });

          // Parse the XML response to get the soil properties
          const propertiesXmlResponse = propertiesResponse.data;

          // Extract soil properties from XML response (simplified parsing for example)
          const soilTypeMatch = propertiesXmlResponse.match(/<compname>(.*?)<\/compname>/);
          const soilType = soilTypeMatch ? soilTypeMatch[1] : 'Unknown';

          const drainageClassMatch = propertiesXmlResponse.match(/<drainagecl>(.*?)<\/drainagecl>/);
          const drainageClass = drainageClassMatch ? drainageClassMatch[1] : 'Unknown';

          const phMatch = propertiesXmlResponse.match(/<ph1to1h2o_r>(.*?)<\/ph1to1h2o_r>/);
          const ph = phMatch ? parseFloat(phMatch[1]) : null;

          const organicMatterMatch = propertiesXmlResponse.match(/<om_r>(.*?)<\/om_r>/);
          const organicMatter = organicMatterMatch ? parseFloat(organicMatterMatch[1]) : null;

          const sandContentMatch = propertiesXmlResponse.match(/<sandtotal_r>(.*?)<\/sandtotal_r>/);
          const sandContent = sandContentMatch ? parseFloat(sandContentMatch[1]) : null;

          const siltContentMatch = propertiesXmlResponse.match(/<silttotal_r>(.*?)<\/silttotal_r>/);
          const siltContent = siltContentMatch ? parseFloat(siltContentMatch[1]) : null;

          const clayContentMatch = propertiesXmlResponse.match(/<claytotal_r>(.*?)<\/claytotal_r>/);
          const clayContent = clayContentMatch ? parseFloat(clayContentMatch[1]) : null;

          const awcMatch = propertiesXmlResponse.match(/<awc_r>(.*?)<\/awc_r>/);
          const awc = awcMatch ? parseFloat(awcMatch[1]) : null;

          const floodFrequencyMatch = propertiesXmlResponse.match(/<flodfreqcl>(.*?)<\/flodfreqcl>/);
          const floodFrequency = floodFrequencyMatch ? floodFrequencyMatch[1] : 'Unknown';

          // Determine erosion risk based on soil properties
          let erosionRisk = 'Unknown';
          if (clayContent !== null && sandContent !== null) {
            if (clayContent > 35) {
              erosionRisk = 'Low'; // Clay soils are less prone to erosion
            } else if (sandContent > 70) {
              erosionRisk = 'High'; // Sandy soils are more prone to erosion
            } else {
              erosionRisk = 'Medium';
            }
          }

          // Determine water availability based on AWC and drainage class
          let waterAvailability = 'Unknown';
          if (awc !== null) {
            if (awc > 0.2) {
              waterAvailability = 'High';
            } else if (awc > 0.1) {
              waterAvailability = 'Medium';
            } else {
              waterAvailability = 'Low';
            }
          } else if (drainageClass) {
            if (drainageClass.includes('poor')) {
              waterAvailability = 'High';
            } else if (drainageClass.includes('well')) {
              waterAvailability = 'Medium';
            } else {
              waterAvailability = 'Low';
            }
          }

          // Calculate soil health index based on organic matter, pH, and other factors
          let soilHealthIndex = 0;
          if (organicMatter !== null) {
            // Organic matter contributes up to 40 points
            soilHealthIndex += Math.min(organicMatter * 10, 40);
          }

          if (ph !== null) {
            // pH contributes up to 30 points (optimal pH is around 6.5)
            const phScore = 30 - Math.abs(ph - 6.5) * 10;
            soilHealthIndex += Math.max(phScore, 0);
          }

          // Drainage contributes up to 30 points
          if (drainageClass) {
            if (drainageClass.includes('well')) {
              soilHealthIndex += 30;
            } else if (drainageClass.includes('moderate')) {
              soilHealthIndex += 20;
            } else if (drainageClass.includes('poor')) {
              soilHealthIndex += 10;
            }
          }

          // Determine land capability class based on soil properties
          let landCapabilityClass = 'Unknown';
          if (soilHealthIndex > 80) {
            landCapabilityClass = 'Class I';
          } else if (soilHealthIndex > 60) {
            landCapabilityClass = 'Class II';
          } else if (soilHealthIndex > 40) {
            landCapabilityClass = 'Class III';
          } else if (soilHealthIndex > 20) {
            landCapabilityClass = 'Class IV';
          } else {
            landCapabilityClass = 'Class V or higher';
          }

          // Now get recommended crops based on soil properties
          // This would typically come from a separate API call or database lookup
          // For now, we'll determine based on soil properties
          let recommendedCrops = [];
          if (soilHealthIndex > 60) {
            recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Alfalfa', 'Vegetables'];
          } else if (soilHealthIndex > 40) {
            recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Oats'];
          } else if (soilHealthIndex > 20) {
            recommendedCrops = ['Wheat', 'Oats', 'Barley', 'Rye'];
          } else {
            recommendedCrops = ['Pasture', 'Hay', 'Conservation Cover'];
          }

          // Now get conservation practices based on soil properties
          let conservationPractices = [];
          if (erosionRisk === 'High') {
            conservationPractices.push('Contour Farming', 'Cover Crops', 'Reduced Tillage');
          } else if (erosionRisk === 'Medium') {
            conservationPractices.push('Cover Crops', 'Crop Rotation');
          }

          if (waterAvailability === 'Low') {
            conservationPractices.push('Irrigation Management', 'Drought-Resistant Crops');
          } else if (waterAvailability === 'High') {
            conservationPractices.push('Drainage Management', 'Raised Beds');
          }

          // Remove duplicates from conservation practices
          conservationPractices = [...new Set(conservationPractices)];

          // Store the data in the database for future use
          await FieldSoilData.create({
            farm_id: farmId,
            field_id: field.id,
            soil_type: soilType,
            soil_health_index: soilHealthIndex,
            land_capability_class: landCapabilityClass,
            erosion_risk: erosionRisk,
            water_availability: waterAvailability,
            last_updated: new Date()
          }).catch(err => console.error(`Error storing soil data for field ${field.id}:`, err));

          // Store conservation practices
          for (const practice of conservationPractices) {
            await ConservationPractice.create({
              farm_id: farmId,
              field_id: field.id,
              practice_name: practice,
              practice_type: 'soil',
              status: 'active'
            }).catch(err => console.error(`Error storing conservation practice for field ${field.id}:`, err));
          }

          // Return the field data
          return {
            field_id: field.id,
            soil_type: soilType,
            soil_health_index: soilHealthIndex,
            conservation_practices: conservationPractices,
            land_capability_class: landCapabilityClass,
            erosion_risk: erosionRisk,
            water_availability: waterAvailability,
            recommended_crops: recommendedCrops,
            historical_yield_data: [], // This would come from a separate API call or database
            last_updated: new Date().toISOString()
          };
        } catch (fieldError) {
          console.error(`Error getting data for field ${field.id}:`, fieldError);

          // Return basic data for this field
          return {
            field_id: field.id,
            soil_type: 'Unknown',
            soil_health_index: 0,
            conservation_practices: [],
            land_capability_class: 'Unknown',
            erosion_risk: 'Unknown',
            water_availability: 'Unknown',
            recommended_crops: [],
            historical_yield_data: [],
            last_updated: new Date().toISOString()
          };
        }
      });

      // Wait for all field data to be fetched
      const fieldsData = await Promise.all(fieldsDataPromises);

      // Store in cache
      cache.set(cacheKey, fieldsData);
      console.log(`Cached farm fields data for farm ${farmId}`);

      return res.status(200).json(fieldsData);
    } catch (apiError) {
      console.warn('Failed to fetch from NRCS API, using mock data instead:', apiError);

      // If the API call fails, return mock data
      const mockFieldsData = fields.map((field, index) => {
        const soilType = ['Loam', 'Clay Loam', 'Sandy Loam', 'Silt Loam'][index % 4];
        const soilHealthIndex = 65 + (index * 5) % 30;
        const landCapabilityClass = `Class ${(index % 4) + 1}`;
        const erosionRisk = ['Low', 'Medium', 'High'][index % 3];
        const waterAvailability = ['Low', 'Medium', 'High'][index % 3];
        const conservationPractices = ['Cover Cropping', 'Reduced Tillage', 'Crop Rotation'];

        // Store the mock data in the database for future use
        FieldSoilData.create({
          farm_id: farmId,
          field_id: field.id,
          soil_type: soilType,
          soil_health_index: soilHealthIndex,
          land_capability_class: landCapabilityClass,
          erosion_risk: erosionRisk,
          water_availability: waterAvailability,
          last_updated: new Date()
        }).catch(err => console.error(`Error storing mock soil data for field ${field.id}:`, err));

        // Store conservation practices
        for (const practice of conservationPractices) {
          ConservationPractice.create({
            farm_id: farmId,
            field_id: field.id,
            practice_name: practice,
            practice_type: 'soil',
            status: 'active'
          }).catch(err => console.error(`Error storing mock conservation practice for field ${field.id}:`, err));
        }

        return {
          field_id: field.id,
          soil_type: soilType,
          soil_health_index: soilHealthIndex,
          conservation_practices: conservationPractices,
          land_capability_class: landCapabilityClass,
          erosion_risk: erosionRisk,
          water_availability: waterAvailability,
          recommended_crops: ['Corn', 'Soybeans', 'Wheat', 'Alfalfa'],
          historical_yield_data: [
            { year: 2020, crop: 'Corn', yield: 160 + index * 10, unit: 'bushels/acre' },
            { year: 2019, crop: 'Soybeans', yield: 50 + index * 3, unit: 'bushels/acre' },
            { year: 2018, crop: 'Wheat', yield: 60 + index * 5, unit: 'bushels/acre' }
          ],
          last_updated: new Date().toISOString()
        };
      });

      // Store in cache
      cache.set(cacheKey, mockFieldsData);
      console.log(`Cached mock farm fields data for farm ${farmId}`);

      return res.status(200).json(mockFieldsData);
    }
  } catch (error) {
    console.error('Error getting farm fields data from data.gov:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get recommended crops for a field based on data.gov information
export const getFieldRecommendedCrops = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Create a cache key based on the field ID
    const cacheKey = `field_recommended_crops_${fieldId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached recommended crops for field ${fieldId}`);
      return res.status(200).json(cachedData);
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Try to get recommended crops from crop rotations
    const cropRotation = await CropRotation.findOne({
      where: { field_id: fieldId },
      order: [['created_at', 'DESC']]
    });

    if (cropRotation && cropRotation.recommended_sequence) {
      // Store in cache
      cache.set(cacheKey, cropRotation.recommended_sequence);
      console.log(`Cached recommended crops for field ${fieldId} from crop rotation`);
      return res.status(200).json(cropRotation.recommended_sequence);
    }

    // Try to get recommended crops from yield predictions
    const yieldPredictions = await YieldPrediction.findAll({
      where: { field_id: fieldId },
      include: [{
        model: Crop,
        attributes: ['name']
      }],
      order: [['confidence_level', 'DESC']],
      limit: 5
    });

    if (yieldPredictions.length > 0) {
      const recommendedCrops = yieldPredictions
        .filter(prediction => prediction.Crop)
        .map(prediction => prediction.Crop.name);

      if (recommendedCrops.length > 0) {
        // Store in cache
        cache.set(cacheKey, recommendedCrops);
        console.log(`Cached recommended crops for field ${fieldId} from yield predictions`);
        return res.status(200).json(recommendedCrops);
      }
    }

    // If no data in database, try to get it from NRCS API
    try {
      // Create NRCS API client
      const nrcsClient = axios.create({
        baseURL: NRCS_API_URL,
        headers: {
          'Authorization': `Bearer ${NRCS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Get recommended crops from NRCS API using soil data
      // First, check if we have location data for the field
      if (!field.location_data?.center?.latitude || !field.location_data?.center?.longitude) {
        throw new Error('Field location data is missing or invalid');
      }

      const latitude = field.location_data.center.latitude;
      const longitude = field.location_data.center.longitude;

      // First, get the soil survey area (SSA) for the given coordinates
      const ssaXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetSoilSurveyAreaByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetSoilSurveyAreaByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const ssaResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', ssaXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil survey area ID
      const ssaXmlResponse = ssaResponse.data;
      // Extract areasymbol from XML response (simplified parsing for example)
      const areaSymbolMatch = ssaXmlResponse.match(/<areasymbol>(.*?)<\/areasymbol>/);
      const areaSymbol = areaSymbolMatch ? areaSymbolMatch[1] : null;

      if (!areaSymbol) {
        throw new Error('No soil survey area found for the given coordinates.');
      }

      // Now get the soil map unit for the given coordinates
      const muXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetMapUnitByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetMapUnitByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const muResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', muXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the map unit key
      const muXmlResponse = muResponse.data;
      // Extract mukey from XML response (simplified parsing for example)
      const mukeyMatch = muXmlResponse.match(/<mukey>(.*?)<\/mukey>/);
      const mukey = mukeyMatch ? mukeyMatch[1] : null;

      if (!mukey) {
        throw new Error('No map unit found for the given coordinates.');
      }

      // Now get the soil properties for the map unit
      const propertiesXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <RunQuery xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <Query>
                SELECT 
                  component.mukey, 
                  component.cokey, 
                  component.compname, 
                  component.comppct_r, 
                  component.majcompflag, 
                  component.taxclname, 
                  component.taxorder, 
                  component.taxsuborder, 
                  component.taxgrtgroup, 
                  component.drainagecl, 
                  chorizon.hzname, 
                  chorizon.hzdept_r, 
                  chorizon.hzdepb_r, 
                  chorizon.ph1to1h2o_r, 
                  chorizon.om_r, 
                  chorizon.cec7_r, 
                  chorizon.sandtotal_r, 
                  chorizon.silttotal_r, 
                  chorizon.claytotal_r, 
                  chorizon.awc_r, 
                  copmgrp.flodfreqcl, 
                  copmgrp.floddurcl, 
                  copmgrp.pondfreqcl, 
                  copmgrp.ponddurcl, 
                  copmgrp.droughty, 
                  copmgrp.hydgrp
                FROM component 
                LEFT JOIN chorizon ON component.cokey = chorizon.cokey 
                LEFT JOIN copmgrp ON component.cokey = copmgrp.cokey 
                WHERE component.mukey = '${mukey}'
                ORDER BY component.comppct_r DESC, chorizon.hzdept_r ASC
              </Query>
            </RunQuery>
          </soap12:Body>
        </soap12:Envelope>`;

      const propertiesResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', propertiesXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil properties
      const propertiesXmlResponse = propertiesResponse.data;

      // Extract soil properties from XML response (simplified parsing for example)
      const soilTypeMatch = propertiesXmlResponse.match(/<compname>(.*?)<\/compname>/);
      const soilType = soilTypeMatch ? soilTypeMatch[1] : 'Unknown';

      const drainageClassMatch = propertiesXmlResponse.match(/<drainagecl>(.*?)<\/drainagecl>/);
      const drainageClass = drainageClassMatch ? drainageClassMatch[1] : 'Unknown';

      const phMatch = propertiesXmlResponse.match(/<ph1to1h2o_r>(.*?)<\/ph1to1h2o_r>/);
      const ph = phMatch ? parseFloat(phMatch[1]) : null;

      const organicMatterMatch = propertiesXmlResponse.match(/<om_r>(.*?)<\/om_r>/);
      const organicMatter = organicMatterMatch ? parseFloat(organicMatterMatch[1]) : null;

      const sandContentMatch = propertiesXmlResponse.match(/<sandtotal_r>(.*?)<\/sandtotal_r>/);
      const sandContent = sandContentMatch ? parseFloat(sandContentMatch[1]) : null;

      const siltContentMatch = propertiesXmlResponse.match(/<silttotal_r>(.*?)<\/silttotal_r>/);
      const siltContent = siltContentMatch ? parseFloat(siltContentMatch[1]) : null;

      const clayContentMatch = propertiesXmlResponse.match(/<claytotal_r>(.*?)<\/claytotal_r>/);
      const clayContent = clayContentMatch ? parseFloat(clayContentMatch[1]) : null;

      const awcMatch = propertiesXmlResponse.match(/<awc_r>(.*?)<\/awc_r>/);
      const awc = awcMatch ? parseFloat(awcMatch[1]) : null;

      // Calculate soil health index based on organic matter, pH, and other factors
      let soilHealthIndex = 0;
      if (organicMatter !== null) {
        // Organic matter contributes up to 40 points
        soilHealthIndex += Math.min(organicMatter * 10, 40);
      }

      if (ph !== null) {
        // pH contributes up to 30 points (optimal pH is around 6.5)
        const phScore = 30 - Math.abs(ph - 6.5) * 10;
        soilHealthIndex += Math.max(phScore, 0);
      }

      // Drainage contributes up to 30 points
      if (drainageClass) {
        if (drainageClass.includes('well')) {
          soilHealthIndex += 30;
        } else if (drainageClass.includes('moderate')) {
          soilHealthIndex += 20;
        } else if (drainageClass.includes('poor')) {
          soilHealthIndex += 10;
        }
      }

      // Now get recommended crops based on soil properties
      // This would typically come from a separate API call or database lookup
      // For now, we'll determine based on soil properties
      let recommendedCrops = [];
      if (soilHealthIndex > 60) {
        recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Alfalfa', 'Vegetables'];
      } else if (soilHealthIndex > 40) {
        recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Oats'];
      } else if (soilHealthIndex > 20) {
        recommendedCrops = ['Wheat', 'Oats', 'Barley', 'Rye'];
      } else {
        recommendedCrops = ['Pasture', 'Hay', 'Conservation Cover'];
      }

      // Adjust recommendations based on soil texture
      if (sandContent !== null && clayContent !== null && siltContent !== null) {
        if (sandContent > 70) {
          // Sandy soils are good for root crops
          recommendedCrops.push('Potatoes', 'Carrots', 'Peanuts');
        } else if (clayContent > 40) {
          // Clay soils are good for certain crops
          recommendedCrops.push('Rice', 'Sorghum');
        } else if (siltContent > 40) {
          // Silty soils are good for vegetables
          recommendedCrops.push('Lettuce', 'Spinach', 'Cabbage');
        }
      }

      // Remove duplicates
      recommendedCrops = [...new Set(recommendedCrops)];

      // Store the recommended crops in a crop rotation record
      if (recommendedCrops.length > 0) {
        try {
          await CropRotation.create({
            farm_id: field.farm_id,
            field_id: fieldId,
            current_crop: field.crop_type || 'Unknown',
            recommended_sequence: recommendedCrops,
            benefits: ['Improved soil health', 'Reduced pest pressure', 'Enhanced nutrient cycling'],
            rotation_years: recommendedCrops.length,
            soil_health_impact: 'positive'
          });
        } catch (err) {
          console.error(`Error storing recommended crops for field ${fieldId}:`, err);
        }
      }

      // Store in cache
      cache.set(cacheKey, recommendedCrops);
      console.log(`Cached recommended crops for field ${fieldId} from API`);

      return res.status(200).json(recommendedCrops);
    } catch (apiError) {
      console.warn('Failed to fetch from NRCS API, using mock data instead:', apiError);

      // If the API call fails, return mock data
      const mockRecommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Alfalfa', 'Oats', 'Barley'];

      // Store the mock recommended crops in a crop rotation record
      try {
        await CropRotation.create({
          farm_id: field.farm_id,
          field_id: fieldId,
          current_crop: field.crop_type || 'Unknown',
          recommended_sequence: mockRecommendedCrops,
          benefits: ['Improved soil health', 'Reduced pest pressure', 'Enhanced nutrient cycling'],
          rotation_years: mockRecommendedCrops.length,
          soil_health_impact: 'positive'
        });
      } catch (err) {
        console.error(`Error storing mock recommended crops for field ${fieldId}:`, err);
      }

      // Store in cache
      cache.set(cacheKey, mockRecommendedCrops);
      console.log(`Cached mock recommended crops for field ${fieldId}`);

      return res.status(200).json(mockRecommendedCrops);
    }
  } catch (error) {
    console.error('Error getting field recommended crops:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get conservation practices for a field based on data.gov information
export const getFieldConservationPractices = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Create a cache key based on the field ID
    const cacheKey = `field_conservation_practices_${fieldId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached conservation practices for field ${fieldId}`);
      return res.status(200).json(cachedData);
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Try to get conservation practices from the database
    const conservationPractices = await ConservationPractice.findAll({
      where: { field_id: fieldId },
      attributes: ['practice_name'],
      order: [['created_at', 'DESC']]
    });

    if (conservationPractices.length > 0) {
      const practiceNames = conservationPractices.map(practice => practice.practice_name);

      // Store in cache
      cache.set(cacheKey, practiceNames);
      console.log(`Cached conservation practices for field ${fieldId} from database`);

      return res.status(200).json(practiceNames);
    }

    // If no data in database, try to get it from NRCS API
    try {
      // Create NRCS API client
      const nrcsClient = axios.create({
        baseURL: NRCS_API_URL,
        headers: {
          'Authorization': `Bearer ${NRCS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Get conservation practices from NRCS API using soil data
      // First, check if we have location data for the field
      if (!field.location_data?.center?.latitude || !field.location_data?.center?.longitude) {
        throw new Error('Field location data is missing or invalid');
      }

      const latitude = field.location_data.center.latitude;
      const longitude = field.location_data.center.longitude;

      // First, get the soil survey area (SSA) for the given coordinates
      const ssaXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetSoilSurveyAreaByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetSoilSurveyAreaByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const ssaResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', ssaXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil survey area ID
      const ssaXmlResponse = ssaResponse.data;
      // Extract areasymbol from XML response (simplified parsing for example)
      const areaSymbolMatch = ssaXmlResponse.match(/<areasymbol>(.*?)<\/areasymbol>/);
      const areaSymbol = areaSymbolMatch ? areaSymbolMatch[1] : null;

      if (!areaSymbol) {
        throw new Error('No soil survey area found for the given coordinates.');
      }

      // Now get the soil map unit for the given coordinates
      const muXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetMapUnitByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetMapUnitByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const muResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', muXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the map unit key
      const muXmlResponse = muResponse.data;
      // Extract mukey from XML response (simplified parsing for example)
      const mukeyMatch = muXmlResponse.match(/<mukey>(.*?)<\/mukey>/);
      const mukey = mukeyMatch ? mukeyMatch[1] : null;

      if (!mukey) {
        throw new Error('No map unit found for the given coordinates.');
      }

      // Now get the soil properties for the map unit
      const propertiesXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <RunQuery xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <Query>
                SELECT 
                  component.mukey, 
                  component.cokey, 
                  component.compname, 
                  component.comppct_r, 
                  component.majcompflag, 
                  component.taxclname, 
                  component.taxorder, 
                  component.taxsuborder, 
                  component.taxgrtgroup, 
                  component.drainagecl, 
                  chorizon.hzname, 
                  chorizon.hzdept_r, 
                  chorizon.hzdepb_r, 
                  chorizon.ph1to1h2o_r, 
                  chorizon.om_r, 
                  chorizon.cec7_r, 
                  chorizon.sandtotal_r, 
                  chorizon.silttotal_r, 
                  chorizon.claytotal_r, 
                  chorizon.awc_r, 
                  copmgrp.flodfreqcl, 
                  copmgrp.floddurcl, 
                  copmgrp.pondfreqcl, 
                  copmgrp.ponddurcl, 
                  copmgrp.droughty, 
                  copmgrp.hydgrp
                FROM component 
                LEFT JOIN chorizon ON component.cokey = chorizon.cokey 
                LEFT JOIN copmgrp ON component.cokey = copmgrp.cokey 
                WHERE component.mukey = '${mukey}'
                ORDER BY component.comppct_r DESC, chorizon.hzdept_r ASC
              </Query>
            </RunQuery>
          </soap12:Body>
        </soap12:Envelope>`;

      const propertiesResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', propertiesXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil properties
      const propertiesXmlResponse = propertiesResponse.data;

      // Extract soil properties from XML response (simplified parsing for example)
      const soilTypeMatch = propertiesXmlResponse.match(/<compname>(.*?)<\/compname>/);
      const soilType = soilTypeMatch ? soilTypeMatch[1] : 'Unknown';

      const drainageClassMatch = propertiesXmlResponse.match(/<drainagecl>(.*?)<\/drainagecl>/);
      const drainageClass = drainageClassMatch ? drainageClassMatch[1] : 'Unknown';

      const sandContentMatch = propertiesXmlResponse.match(/<sandtotal_r>(.*?)<\/sandtotal_r>/);
      const sandContent = sandContentMatch ? parseFloat(sandContentMatch[1]) : null;

      const clayContentMatch = propertiesXmlResponse.match(/<claytotal_r>(.*?)<\/claytotal_r>/);
      const clayContent = clayContentMatch ? parseFloat(clayContentMatch[1]) : null;

      const awcMatch = propertiesXmlResponse.match(/<awc_r>(.*?)<\/awc_r>/);
      const awc = awcMatch ? parseFloat(awcMatch[1]) : null;

      const floodFrequencyMatch = propertiesXmlResponse.match(/<flodfreqcl>(.*?)<\/flodfreqcl>/);
      const floodFrequency = floodFrequencyMatch ? floodFrequencyMatch[1] : 'Unknown';

      const droughtyMatch = propertiesXmlResponse.match(/<droughty>(.*?)<\/droughty>/);
      const droughty = droughtyMatch ? droughtyMatch[1] === 'Yes' : false;

      // Determine erosion risk based on soil properties
      let erosionRisk = 'Unknown';
      if (clayContent !== null && sandContent !== null) {
        if (clayContent > 35) {
          erosionRisk = 'Low'; // Clay soils are less prone to erosion
        } else if (sandContent > 70) {
          erosionRisk = 'High'; // Sandy soils are more prone to erosion
        } else {
          erosionRisk = 'Medium';
        }
      }

      // Determine water availability based on AWC and drainage class
      let waterAvailability = 'Unknown';
      if (awc !== null) {
        if (awc > 0.2) {
          waterAvailability = 'High';
        } else if (awc > 0.1) {
          waterAvailability = 'Medium';
        } else {
          waterAvailability = 'Low';
        }
      } else if (drainageClass) {
        if (drainageClass.includes('poor')) {
          waterAvailability = 'High';
        } else if (drainageClass.includes('well')) {
          waterAvailability = 'Medium';
        } else {
          waterAvailability = 'Low';
        }
      }

      // Now get conservation practices based on soil properties
      let practices = [];

      // Add basic conservation practices that are good for all soils
      practices.push('Crop Rotation', 'Soil Testing');

      // Add practices based on erosion risk
      if (erosionRisk === 'High') {
        practices.push('Contour Farming', 'Cover Crops', 'Reduced Tillage', 'Terracing', 'Buffer Strips');
      } else if (erosionRisk === 'Medium') {
        practices.push('Cover Crops', 'Reduced Tillage', 'Contour Farming');
      } else {
        practices.push('Cover Crops');
      }

      // Add practices based on water availability
      if (waterAvailability === 'Low' || droughty) {
        practices.push('Irrigation Management', 'Drought-Resistant Crops', 'Mulching');
      } else if (waterAvailability === 'High') {
        practices.push('Drainage Management', 'Raised Beds', 'Water Management');
      }

      // Add practices based on flood frequency
      if (floodFrequency && !floodFrequency.includes('None')) {
        practices.push('Flood Control Structures', 'Riparian Buffers', 'Wetland Management');
      }

      // Add practices based on soil type
      if (soilType.includes('Clay')) {
        practices.push('Deep Tillage', 'Organic Matter Addition');
      } else if (soilType.includes('Sand')) {
        practices.push('Organic Matter Addition', 'Wind Erosion Control');
      } else if (soilType.includes('Silt')) {
        practices.push('Reduced Tillage', 'Cover Crops');
      }

      // Remove duplicates
      practices = [...new Set(practices)];

      // Store the practices in the database for future use
      if (practices.length > 0) {
        try {
          for (const practice of practices) {
            await ConservationPractice.create({
              farm_id: field.farm_id,
              field_id: fieldId,
              practice_name: practice,
              practice_type: 'soil',
              status: 'active'
            });
          }
        } catch (err) {
          console.error(`Error storing conservation practices for field ${fieldId}:`, err);
        }
      }

      // Store in cache
      cache.set(cacheKey, practices);
      console.log(`Cached conservation practices for field ${fieldId} from API`);

      return res.status(200).json(practices);
    } catch (apiError) {
      console.warn('Failed to fetch from NRCS API, using mock data instead:', apiError);

      // If the API call fails, return mock data
      const mockConservationPractices = [
        'Cover Cropping',
        'Reduced Tillage',
        'Crop Rotation',
        'Contour Farming',
        'Buffer Strips',
        'Terracing'
      ];

      // Store the mock practices in the database for future use
      try {
        for (const practice of mockConservationPractices) {
          await ConservationPractice.create({
            farm_id: field.farm_id,
            field_id: fieldId,
            practice_name: practice,
            practice_type: 'soil',
            status: 'active'
          });
        }
      } catch (err) {
        console.error(`Error storing mock conservation practices for field ${fieldId}:`, err);
      }

      // Store in cache
      cache.set(cacheKey, mockConservationPractices);
      console.log(`Cached mock conservation practices for field ${fieldId}`);

      return res.status(200).json(mockConservationPractices);
    }
  } catch (error) {
    console.error('Error getting field conservation practices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get historical yield data for a field based on data.gov information
export const getFieldHistoricalYieldData = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Create a cache key based on the field ID
    const cacheKey = `field_historical_yield_${fieldId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached historical yield data for field ${fieldId}`);
      return res.status(200).json(cachedData);
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Try to get historical yield data from harvests
    const harvests = await Harvest.findAll({
      where: { 
        field_id: fieldId,
        status: 'completed',
        yield_amount: { [Op.not]: null }
      },
      include: [{
        model: Crop,
        as: 'harvestCrop',
        attributes: ['name']
      }],
      order: [['actual_date', 'DESC']],
      limit: 10
    });

    if (harvests.length > 0) {
      const historicalYieldData = harvests.map(harvest => ({
        year: new Date(harvest.actual_date).getFullYear(),
        crop: harvest.harvestCrop ? harvest.harvestCrop.name : 'Unknown',
        yield: parseFloat(harvest.yield_amount),
        unit: harvest.yield_unit
      }));

      // Store in cache
      cache.set(cacheKey, historicalYieldData);
      console.log(`Cached historical yield data for field ${fieldId} from harvests`);

      return res.status(200).json(historicalYieldData);
    }

    // If no data in database, try to get it from NRCS API
    try {
      // Create NRCS API client
      const nrcsClient = axios.create({
        baseURL: NRCS_API_URL,
        headers: {
          'Authorization': `Bearer ${NRCS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Historical yield data is not directly available from NRCS API
      // Instead, we'll get crop suitability data and use that to generate reasonable historical yield estimates

      // First, check if we have location data for the field
      if (!field.location_data?.center?.latitude || !field.location_data?.center?.longitude) {
        throw new Error('Field location data is missing or invalid');
      }

      const latitude = field.location_data.center.latitude;
      const longitude = field.location_data.center.longitude;

      // First, get the soil survey area (SSA) for the given coordinates
      const ssaXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetSoilSurveyAreaByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetSoilSurveyAreaByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const ssaResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', ssaXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil survey area ID
      const ssaXmlResponse = ssaResponse.data;
      // Extract areasymbol from XML response (simplified parsing for example)
      const areaSymbolMatch = ssaXmlResponse.match(/<areasymbol>(.*?)<\/areasymbol>/);
      const areaSymbol = areaSymbolMatch ? areaSymbolMatch[1] : null;

      if (!areaSymbol) {
        throw new Error('No soil survey area found for the given coordinates.');
      }

      // Now get the soil map unit for the given coordinates
      const muXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <GetMapUnitByLocation xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <latitude>${latitude}</latitude>
              <longitude>${longitude}</longitude>
            </GetMapUnitByLocation>
          </soap12:Body>
        </soap12:Envelope>`;

      const muResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', muXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the map unit key
      const muXmlResponse = muResponse.data;
      // Extract mukey from XML response (simplified parsing for example)
      const mukeyMatch = muXmlResponse.match(/<mukey>(.*?)<\/mukey>/);
      const mukey = mukeyMatch ? mukeyMatch[1] : null;

      if (!mukey) {
        throw new Error('No map unit found for the given coordinates.');
      }

      // Now get the soil properties for the map unit
      const propertiesXml = `<?xml version="1.0" encoding="utf-8"?>
        <soap12:Envelope xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
          <soap12:Body>
            <RunQuery xmlns="http://SDMDataAccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx">
              <Query>
                SELECT 
                  component.mukey, 
                  component.cokey, 
                  component.compname, 
                  component.comppct_r, 
                  component.majcompflag, 
                  component.taxclname, 
                  component.taxorder, 
                  component.taxsuborder, 
                  component.taxgrtgroup, 
                  component.drainagecl, 
                  chorizon.hzname, 
                  chorizon.hzdept_r, 
                  chorizon.hzdepb_r, 
                  chorizon.ph1to1h2o_r, 
                  chorizon.om_r, 
                  chorizon.cec7_r, 
                  chorizon.sandtotal_r, 
                  chorizon.silttotal_r, 
                  chorizon.claytotal_r, 
                  chorizon.awc_r, 
                  copmgrp.flodfreqcl, 
                  copmgrp.floddurcl, 
                  copmgrp.pondfreqcl, 
                  copmgrp.ponddurcl, 
                  copmgrp.droughty, 
                  copmgrp.hydgrp
                FROM component 
                LEFT JOIN chorizon ON component.cokey = chorizon.cokey 
                LEFT JOIN copmgrp ON component.cokey = copmgrp.cokey 
                WHERE component.mukey = '${mukey}'
                ORDER BY component.comppct_r DESC, chorizon.hzdept_r ASC
              </Query>
            </RunQuery>
          </soap12:Body>
        </soap12:Envelope>`;

      const propertiesResponse = await axios.post('https://sdmdataaccess.nrcs.usda.gov/Tabular/SDMTabularService.asmx', propertiesXml, {
        headers: {
          'Content-Type': 'application/soap+xml',
          'Authorization': `Bearer ${NRCS_API_KEY}`
        }
      });

      // Parse the XML response to get the soil properties
      const propertiesXmlResponse = propertiesResponse.data;

      // Extract soil properties from XML response (simplified parsing for example)
      const soilTypeMatch = propertiesXmlResponse.match(/<compname>(.*?)<\/compname>/);
      const soilType = soilTypeMatch ? soilTypeMatch[1] : 'Unknown';

      const phMatch = propertiesXmlResponse.match(/<ph1to1h2o_r>(.*?)<\/ph1to1h2o_r>/);
      const ph = phMatch ? parseFloat(phMatch[1]) : null;

      const organicMatterMatch = propertiesXmlResponse.match(/<om_r>(.*?)<\/om_r>/);
      const organicMatter = organicMatterMatch ? parseFloat(organicMatterMatch[1]) : null;

      const awcMatch = propertiesXmlResponse.match(/<awc_r>(.*?)<\/awc_r>/);
      const awc = awcMatch ? parseFloat(awcMatch[1]) : null;

      // Calculate soil health index based on organic matter, pH, and other factors
      let soilHealthIndex = 0;
      if (organicMatter !== null) {
        // Organic matter contributes up to 40 points
        soilHealthIndex += Math.min(organicMatter * 10, 40);
      }

      if (ph !== null) {
        // pH contributes up to 30 points (optimal pH is around 6.5)
        const phScore = 30 - Math.abs(ph - 6.5) * 10;
        soilHealthIndex += Math.max(phScore, 0);
      }

      if (awc !== null) {
        // Available water capacity contributes up to 30 points
        soilHealthIndex += Math.min(awc * 100, 30);
      }

      // Now get recommended crops based on soil properties
      // This would typically come from a separate API call or database lookup
      // For now, we'll determine based on soil properties
      let recommendedCrops = [];
      if (soilHealthIndex > 60) {
        recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Alfalfa', 'Vegetables'];
      } else if (soilHealthIndex > 40) {
        recommendedCrops = ['Corn', 'Soybeans', 'Wheat', 'Oats'];
      } else if (soilHealthIndex > 20) {
        recommendedCrops = ['Wheat', 'Oats', 'Barley', 'Rye'];
      } else {
        recommendedCrops = ['Pasture', 'Hay', 'Conservation Cover'];
      }

      // Generate historical yield data based on soil health and recommended crops
      // We'll create data for the last 5 years with some variation to simulate real data
      const currentYear = new Date().getFullYear();
      const historicalYieldData = [];

      // Define base yields for different crops (in bushels/acre or tons/acre)
      const baseYields = {
        'Corn': 180,
        'Soybeans': 55,
        'Wheat': 65,
        'Oats': 80,
        'Barley': 70,
        'Rye': 60,
        'Alfalfa': 4.5, // tons/acre
        'Hay': 3.0, // tons/acre
        'Vegetables': 15, // tons/acre
        'Pasture': 2.5, // tons/acre
        'Conservation Cover': 0 // no yield
      };

      // Adjust base yields based on soil health
      const soilHealthFactor = soilHealthIndex / 100 * 0.4 + 0.8; // 0.8 to 1.2 range

      // Generate historical data for the last 5 years
      for (let i = 0; i < 5; i++) {
        const year = currentYear - i;

        // Rotate crops in a realistic pattern
        // For simplicity, we'll use a common corn-soybean rotation with wheat every third year
        let cropIndex;
        if (recommendedCrops.includes('Corn') && recommendedCrops.includes('Soybeans')) {
          // Corn-soybean rotation
          cropIndex = year % 2 === 0 ? 0 : 1;
        } else if (recommendedCrops.includes('Wheat') && recommendedCrops.includes('Corn') && recommendedCrops.includes('Soybeans')) {
          // Corn-soybean-wheat rotation
          cropIndex = year % 3;
        } else {
          // Just use the first recommended crop
          cropIndex = 0;
        }

        const crop = recommendedCrops[cropIndex];

        // Get base yield for this crop
        const baseYield = baseYields[crop] || 50;

        // Apply soil health factor
        let adjustedYield = baseYield * soilHealthFactor;

        // Add random variation (±10%)
        const variation = (Math.random() * 0.2 - 0.1) * adjustedYield;
        adjustedYield += variation;

        // Add weather variation based on year (simplified)
        // This simulates good and bad weather years
        const weatherFactor = Math.sin(year * 0.7) * 0.15 + 1; // 0.85 to 1.15 range
        adjustedYield *= weatherFactor;

        // Round to nearest whole number
        adjustedYield = Math.round(adjustedYield);

        // Determine unit based on crop type
        let unit = 'bushels/acre';
        if (['Alfalfa', 'Hay', 'Vegetables', 'Pasture'].includes(crop)) {
          unit = 'tons/acre';
        }

        historicalYieldData.push({
          year,
          crop,
          yield: adjustedYield,
          unit
        });
      }

      // Store the historical yield data as harvests in the database for future use
      if (historicalYieldData.length > 0) {
        try {
          // Get or create a generic crop for each crop type
          for (const yieldData of historicalYieldData) {
            // Find or create the crop
            let crop = await Crop.findOne({
              where: { name: yieldData.crop }
            });

            if (!crop) {
              crop = await Crop.create({
                farm_id: field.farm_id,
                name: yieldData.crop,
                type: 'grain',
                status: 'active'
              });
            }

            // Create a harvest record
            const harvestDate = new Date(yieldData.year, 11, 31); // December 31st of the year
            await Harvest.create({
              farm_id: field.farm_id,
              field_id: fieldId,
              crop_id: crop.id,
              scheduled_date: harvestDate,
              actual_date: harvestDate,
              status: 'completed',
              yield_amount: yieldData.yield,
              yield_unit: yieldData.unit,
              quality_rating: 3, // Average quality
              weather_conditions: 'Historical data',
              notes: 'Imported from historical yield data'
            });
          }
        } catch (err) {
          console.error(`Error storing historical yield data for field ${fieldId}:`, err);
        }
      }

      // Store in cache
      cache.set(cacheKey, historicalYieldData);
      console.log(`Cached historical yield data for field ${fieldId} from API`);

      return res.status(200).json(historicalYieldData);
    } catch (apiError) {
      console.warn('Failed to fetch from NRCS API:', apiError);

      // If the API call fails, return nothing to the user
      return res.status(404).json({ 
        error: 'No historical yield data available', 
        message: 'Could not retrieve historical yield data from the NRCS API'
      });
    }
  } catch (error) {
    console.error('Error getting field historical yield data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all field data in a single request
export const getAllFieldData = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Create a cache key based on the field ID
    const cacheKey = `all_field_data_${fieldId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached all field data for field ${fieldId}`);
      return res.status(200).json(cachedData);
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Fetch all data from the database in parallel
    const [
      fieldSoilData,
      conservationPractices,
      cropRotation,
      harvests
    ] = await Promise.all([
      // Get field soil data
      FieldSoilData.findOne({
        where: { field_id: fieldId }
      }),

      // Get conservation practices
      ConservationPractice.findAll({
        where: { field_id: fieldId },
        attributes: ['practice_name']
      }),

      // Get crop rotation
      CropRotation.findOne({
        where: { field_id: fieldId },
        order: [['created_at', 'DESC']]
      }),

      // Get harvests
      Harvest.findAll({
        where: { 
          field_id: fieldId,
          status: 'completed',
          yield_amount: { [Op.not]: null }
        },
        include: [{
          model: Crop,
          as: 'harvestCrop',
          attributes: ['name']
        }],
        order: [['actual_date', 'DESC']],
        limit: 10
      })
    ]);

    // If we have data in the database, use it
    if (fieldSoilData || conservationPractices.length > 0 || cropRotation || harvests.length > 0) {
      // Transform the data
      const fieldData = fieldSoilData ? {
        field_id: fieldId,
        soil_type: fieldSoilData.soil_type,
        soil_health_index: fieldSoilData.soil_health_index || 0,
        conservation_practices: conservationPractices.map(cp => cp.practice_name),
        land_capability_class: fieldSoilData.land_capability_class || 'Unknown',
        erosion_risk: fieldSoilData.erosion_risk || 'Unknown',
        water_availability: fieldSoilData.water_availability || 'Unknown',
        recommended_crops: cropRotation ? cropRotation.recommended_sequence : [],
        historical_yield_data: harvests.map(harvest => ({
          year: new Date(harvest.actual_date).getFullYear(),
          crop: harvest.harvestCrop ? harvest.harvestCrop.name : 'Unknown',
          yield: parseFloat(harvest.yield_amount),
          unit: harvest.yield_unit
        })),
        last_updated: fieldSoilData.last_updated || new Date().toISOString()
      } : {
        field_id: fieldId,
        soil_type: 'Unknown',
        soil_health_index: 0,
        conservation_practices: conservationPractices.map(cp => cp.practice_name),
        land_capability_class: 'Unknown',
        erosion_risk: 'Unknown',
        water_availability: 'Unknown',
        recommended_crops: cropRotation ? cropRotation.recommended_sequence : [],
        historical_yield_data: harvests.map(harvest => ({
          year: new Date(harvest.actual_date).getFullYear(),
          crop: harvest.harvestCrop ? harvest.harvestCrop.name : 'Unknown',
          yield: parseFloat(harvest.yield_amount),
          unit: harvest.yield_unit
        })),
        last_updated: new Date().toISOString()
      };

      const recommendedCrops = cropRotation ? cropRotation.recommended_sequence : [];
      const practiceNames = conservationPractices.map(practice => practice.practice_name);
      const historicalYieldData = harvests.map(harvest => ({
        year: new Date(harvest.actual_date).getFullYear(),
        crop: harvest.harvestCrop ? harvest.harvestCrop.name : 'Unknown',
        yield: parseFloat(harvest.yield_amount),
        unit: harvest.yield_unit
      }));

      const response = {
        fieldData,
        recommendedCrops,
        conservationPractices: practiceNames,
        historicalYieldData
      };

      // Store in cache
      cache.set(cacheKey, response);
      console.log(`Cached all field data for field ${fieldId} from database`);

      return res.status(200).json(response);
    }

    // If no data in database, try to get it from NRCS API
    try {
      // Create NRCS API client
      const nrcsClient = axios.create({
        baseURL: NRCS_API_URL,
        headers: {
          'Authorization': `Bearer ${NRCS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Fetch all data in parallel
      const [fieldDataResponse, recommendedCropsResponse, conservationPracticesResponse, historicalYieldDataResponse] = await Promise.all([
        nrcsClient.get(`/field-data`, {
          params: {
            latitude: field.location_data?.latitude,
            longitude: field.location_data?.longitude,
            field_id: fieldId
          }
        }),
        nrcsClient.get(`/crops/recommended`, {
          params: {
            latitude: field.location_data?.latitude,
            longitude: field.location_data?.longitude,
            field_id: fieldId
          }
        }),
        nrcsClient.get(`/conservation/practices`, {
          params: {
            latitude: field.location_data?.latitude,
            longitude: field.location_data?.longitude,
            field_id: fieldId
          }
        }),
        nrcsClient.get(`/yield/historical`, {
          params: {
            latitude: field.location_data?.latitude,
            longitude: field.location_data?.longitude,
            field_id: fieldId
          }
        })
      ]);

      // Store the data in the database for future use
      try {
        // Store field soil data
        await FieldSoilData.create({
          farm_id: field.farm_id,
          field_id: fieldId,
          soil_type: fieldDataResponse.data.soilType || 'Unknown',
          soil_health_index: fieldDataResponse.data.soilHealthIndex || 0,
          land_capability_class: fieldDataResponse.data.landCapabilityClass || 'Unknown',
          erosion_risk: fieldDataResponse.data.erosionRisk || 'Unknown',
          water_availability: fieldDataResponse.data.waterAvailability || 'Unknown',
          last_updated: new Date()
        });

        // Store conservation practices
        const practices = conservationPracticesResponse.data.conservationPractices || [];
        for (const practice of practices) {
          await ConservationPractice.create({
            farm_id: field.farm_id,
            field_id: fieldId,
            practice_name: practice,
            practice_type: 'soil',
            status: 'active'
          });
        }

        // Store crop rotation
        const recommendedCrops = recommendedCropsResponse.data.recommendedCrops || [];
        if (recommendedCrops.length > 0) {
          await CropRotation.create({
            farm_id: field.farm_id,
            field_id: fieldId,
            current_crop: field.crop_type || 'Unknown',
            recommended_sequence: recommendedCrops,
            benefits: ['Improved soil health', 'Reduced pest pressure', 'Enhanced nutrient cycling'],
            rotation_years: recommendedCrops.length,
            soil_health_impact: 'positive'
          });
        }

        // Store historical yield data
        const historicalYieldData = historicalYieldDataResponse.data.historicalYieldData || [];
        for (const yieldData of historicalYieldData) {
          // Find or create the crop
          let crop = await Crop.findOne({
            where: { name: yieldData.crop }
          });

          if (!crop) {
            crop = await Crop.create({
              farm_id: field.farm_id,
              name: yieldData.crop,
              type: 'grain',
              status: 'active'
            });
          }

          // Create a harvest record
          const harvestDate = new Date(yieldData.year, 11, 31); // December 31st of the year
          await Harvest.create({
            farm_id: field.farm_id,
            field_id: fieldId,
            crop_id: crop.id,
            scheduled_date: harvestDate,
            actual_date: harvestDate,
            status: 'completed',
            yield_amount: yieldData.yield,
            yield_unit: yieldData.unit,
            quality_rating: 3, // Average quality
            weather_conditions: 'Historical data',
            notes: 'Imported from historical yield data'
          });
        }
      } catch (err) {
        console.error(`Error storing field data for field ${fieldId}:`, err);
      }

      // Combine all data into a single response
      const response = {
        fieldData: {
          field_id: fieldId,
          soil_type: fieldDataResponse.data.soilType || 'Unknown',
          soil_health_index: fieldDataResponse.data.soilHealthIndex || 0,
          conservation_practices: conservationPracticesResponse.data.conservationPractices || [],
          land_capability_class: fieldDataResponse.data.landCapabilityClass || 'Unknown',
          erosion_risk: fieldDataResponse.data.erosionRisk || 'Unknown',
          water_availability: fieldDataResponse.data.waterAvailability || 'Unknown',
          recommended_crops: recommendedCropsResponse.data.recommendedCrops || [],
          historical_yield_data: historicalYieldDataResponse.data.historicalYieldData || [],
          last_updated: new Date().toISOString()
        },
        recommendedCrops: recommendedCropsResponse.data.recommendedCrops || [],
        conservationPractices: conservationPracticesResponse.data.conservationPractices || [],
        historicalYieldData: historicalYieldDataResponse.data.historicalYieldData || []
      };

      // Store in cache
      cache.set(cacheKey, response);
      console.log(`Cached all field data for field ${fieldId} from API`);

      return res.status(200).json(response);
    } catch (apiError) {
      console.warn('Failed to fetch from NRCS API:', apiError);

      // If the API call fails, return nothing to the user
      return res.status(404).json({ 
        error: 'No field data available', 
        message: 'Could not retrieve field data from the NRCS API'
      });
    }
  } catch (error) {
    console.error('Error getting all field data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all harvest direction maps for a field
export const getFieldHarvestDirectionMaps = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get all harvest direction maps for the field
    const harvestDirectionMaps = await HarvestDirectionMap.findAll({
      where: { field_id: fieldId },
      order: [['order_index', 'ASC']]
    });

    return res.status(200).json(harvestDirectionMaps);
  } catch (error) {
    console.error('Error getting field harvest direction maps:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single harvest direction map by ID
export const getHarvestDirectionMapById = async (req, res) => {
  try {
    const { mapId } = req.params;

    const harvestDirectionMap = await HarvestDirectionMap.findByPk(mapId);

    if (!harvestDirectionMap) {
      return res.status(404).json({ error: 'Harvest direction map not found' });
    }

    return res.status(200).json(harvestDirectionMap);
  } catch (error) {
    console.error('Error getting harvest direction map:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new harvest direction map
export const createHarvestDirectionMap = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      field_id: bodyFieldId, 
      name, 
      description, 
      order_index, 
      elements 
    } = req.body;

    // Get field_id from URL params or request body
    const field_id = req.params.fieldId || bodyFieldId;

    // Validate required fields
    if (!field_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Field ID is required' });
    }

    if (!name) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Map name is required' });
    }

    // Find field to ensure it exists
    const field = await Field.findByPk(field_id);
    if (!field) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Field not found' });
    }

    // Create harvest direction map
    const harvestDirectionMap = await HarvestDirectionMap.create({
      field_id,
      name,
      description,
      order_index: order_index || 1,
      elements: elements || []
    }, { transaction });

    await transaction.commit();

    return res.status(201).json(harvestDirectionMap);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating harvest direction map:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update an existing harvest direction map
export const updateHarvestDirectionMap = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { mapId } = req.params;
    const { 
      name, 
      description, 
      order_index, 
      elements 
    } = req.body;

    // Find harvest direction map to ensure it exists
    const harvestDirectionMap = await HarvestDirectionMap.findByPk(mapId);
    if (!harvestDirectionMap) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Harvest direction map not found' });
    }

    // Update harvest direction map
    await harvestDirectionMap.update({
      name: name || harvestDirectionMap.name,
      description: description !== undefined ? description : harvestDirectionMap.description,
      order_index: order_index || harvestDirectionMap.order_index,
      elements: elements || harvestDirectionMap.elements
    }, { transaction });

    await transaction.commit();

    return res.status(200).json(harvestDirectionMap);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating harvest direction map:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a harvest direction map
export const deleteHarvestDirectionMap = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { mapId } = req.params;

    // Find harvest direction map to ensure it exists
    const harvestDirectionMap = await HarvestDirectionMap.findByPk(mapId);
    if (!harvestDirectionMap) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Harvest direction map not found' });
    }

    // Delete harvest direction map
    await harvestDirectionMap.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ message: 'Harvest direction map deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting harvest direction map:', error);
    return res.status(500).json({ error: error.message });
  }
};
