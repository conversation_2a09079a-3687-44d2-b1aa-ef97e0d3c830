import SubscriptionPlan from '../models/SubscriptionPlan.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import SubscriptionTransaction from '../models/SubscriptionTransaction.js';
import { sequelize } from '../config/database.js';

// Get all subscription plans
export const getAllSubscriptionPlans = async (req, res) => {
  try {
    const plans = await SubscriptionPlan.findAll({
      where: { is_active: true },
      order: [['price_monthly', 'ASC']]
    });

    return res.status(200).json({ plans });
  } catch (error) {
    console.error('Error getting subscription plans:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single subscription plan by ID
export const getSubscriptionPlanById = async (req, res) => {
  try {
    const { planId } = req.params;

    const plan = await SubscriptionPlan.findByPk(planId);

    if (!plan) {
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    return res.status(200).json({ plan });
  } catch (error) {
    console.error('Error getting subscription plan:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new subscription plan (admin only)
export const createSubscriptionPlan = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      name, 
      description, 
      price_monthly, 
      price_yearly, 
      features, 
      max_farms, 
      max_users,
      is_trial,
      trial_duration_days,
      trial_features,
      requires_payment_method,
      is_default
    } = req.body;

    // Validate required fields
    if (!name || !price_monthly || !price_yearly || !features || !max_farms || !max_users) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Name, prices, features, max_farms, and max_users are required' 
      });
    }

    // If this plan is set as default, unset any existing default plans
    if (is_default) {
      await SubscriptionPlan.update(
        { is_default: false },
        { where: { is_default: true }, transaction }
      );
    }

    // Create subscription plan
    const plan = await SubscriptionPlan.create({
      name,
      description,
      price_monthly,
      price_yearly,
      features,
      max_farms,
      max_users,
      is_active: true,
      is_trial: is_trial || false,
      trial_duration_days: trial_duration_days || 14,
      trial_features: trial_features || features,
      requires_payment_method: requires_payment_method || false,
      is_default: is_default || false
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Subscription plan created successfully',
      plan 
    });
  } catch (error) {
    console.error('Error creating subscription plan:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a subscription plan (admin only)
export const updateSubscriptionPlan = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { planId } = req.params;
    const { 
      name, 
      description, 
      price_monthly, 
      price_yearly, 
      features, 
      max_farms, 
      max_users,
      is_active,
      is_trial,
      trial_duration_days,
      trial_features,
      requires_payment_method,
      is_default
    } = req.body;

    // Find plan to ensure it exists
    const plan = await SubscriptionPlan.findByPk(planId, { transaction });
    if (!plan) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    // If this plan is being set as default, unset any existing default plans
    if (is_default && !plan.is_default) {
      await SubscriptionPlan.update(
        { is_default: false },
        { 
          where: { is_default: true },
          transaction 
        }
      );
    }

    // Update plan
    await plan.update({
      name: name || plan.name,
      description: description !== undefined ? description : plan.description,
      price_monthly: price_monthly || plan.price_monthly,
      price_yearly: price_yearly || plan.price_yearly,
      features: features || plan.features,
      max_farms: max_farms || plan.max_farms,
      max_users: max_users || plan.max_users,
      is_active: is_active !== undefined ? is_active : plan.is_active,
      is_trial: is_trial !== undefined ? is_trial : plan.is_trial,
      trial_duration_days: trial_duration_days || plan.trial_duration_days,
      trial_features: trial_features || plan.trial_features,
      requires_payment_method: requires_payment_method !== undefined ? requires_payment_method : plan.requires_payment_method,
      is_default: is_default !== undefined ? is_default : plan.is_default
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Subscription plan updated successfully',
      plan 
    });
  } catch (error) {
    console.error('Error updating subscription plan:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a subscription plan (admin only)
export const deleteSubscriptionPlan = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { planId } = req.params;

    // Find plan to ensure it exists
    const plan = await SubscriptionPlan.findByPk(planId);
    if (!plan) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    // Check if any farms are using this plan
    const farmsUsingPlan = await Farm.count({
      where: { subscription_plan_id: planId }
    });

    if (farmsUsingPlan > 0) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Cannot delete plan that is in use by farms. Deactivate it instead.' 
      });
    }

    // Delete plan
    await plan.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Subscription plan deleted successfully' 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting subscription plan:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get subscription transactions for a farm
export const getFarmSubscriptionTransactions = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all transactions for the farm
    const transactions = await SubscriptionTransaction.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: SubscriptionPlan,
          attributes: ['name', 'price_monthly', 'price_yearly']
        }
      ],
      order: [['transaction_date', 'DESC']]
    });

    return res.status(200).json({ transactions });
  } catch (error) {
    console.error('Error getting farm subscription transactions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a subscription transaction (for payment processing)
export const createSubscriptionTransaction = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farm_id, 
      subscription_plan_id, 
      amount, 
      currency, 
      status, 
      payment_method, 
      payment_reference,
      billing_period_start,
      billing_period_end,
      promo_code
    } = req.body;

    // Validate required fields
    if (!farm_id || !subscription_plan_id || !amount || !status) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Farm ID, subscription plan ID, amount, and status are required' 
      });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farm_id);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find plan to ensure it exists
    const plan = await SubscriptionPlan.findByPk(subscription_plan_id);
    if (!plan) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    let finalAmount = amount;
    let appliedPromoCode = null;
    let discountAmount = 0;

    // Apply promo code if provided
    if (promo_code) {
      try {
        // Import PromoCode model
        const PromoCode = (await import('../models/PromoCode.js')).default;

        // Find the promo code
        const promoCode = await PromoCode.findOne({
          where: { 
            code: promo_code,
            is_active: true,
            [Op.or]: [
              { valid_to: null },
              { valid_to: { [Op.gte]: new Date() } }
            ],
            valid_from: { [Op.lte]: new Date() },
            [Op.or]: [
              { subscription_plan_id: null },
              { subscription_plan_id: subscription_plan_id }
            ]
          },
          transaction
        });

        if (promoCode) {
          // Check if the promo code has reached its maximum uses
          if (promoCode.max_uses === null || promoCode.current_uses < promoCode.max_uses) {
            // Apply discount
            if (promoCode.discount_percent) {
              discountAmount = (amount * promoCode.discount_percent) / 100;
              finalAmount = amount - discountAmount;
            } else if (promoCode.discount_amount) {
              discountAmount = promoCode.discount_amount;
              finalAmount = amount - discountAmount;
              if (finalAmount < 0) finalAmount = 0;
            }

            // Increment the usage counter
            await promoCode.update({
              current_uses: promoCode.current_uses + 1
            }, { transaction });

            appliedPromoCode = {
              id: promoCode.id,
              code: promoCode.code,
              discount_percent: promoCode.discount_percent,
              discount_amount: promoCode.discount_amount,
              discount_months: promoCode.discount_months
            };
          }
        }
      } catch (error) {
        console.error('Error applying promo code:', error);
        // Continue without applying promo code if there's an error
      }
    }

    // Create transaction
    const subscriptionTransaction = await SubscriptionTransaction.create({
      farm_id,
      subscription_plan_id,
      amount: finalAmount,
      original_amount: amount,
      discount_amount: discountAmount,
      promo_code: appliedPromoCode ? appliedPromoCode.code : null,
      promo_code_id: appliedPromoCode ? appliedPromoCode.id : null,
      currency: currency || 'USD',
      status,
      payment_method,
      payment_reference,
      transaction_date: new Date(),
      billing_period_start,
      billing_period_end
    }, { transaction });

    // If payment was successful, update farm's subscription
    if (status === 'succeeded') {
      await farm.update({
        subscription_plan_id,
        subscription_status: 'active',
        subscription_start_date: billing_period_start || new Date(),
        subscription_end_date: billing_period_end,
        promo_code: appliedPromoCode ? appliedPromoCode.code : null,
        promo_code_id: appliedPromoCode ? appliedPromoCode.id : null,
        promo_discount_months: appliedPromoCode ? appliedPromoCode.discount_months : null
      }, { transaction });
    }

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Subscription transaction created successfully',
      transaction: subscriptionTransaction,
      appliedPromoCode
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating subscription transaction:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a free trial subscription for a farm
export const createFarmTrialSubscription = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId, trialDays = 30 } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Farm ID is required' 
      });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find the default trial subscription plan
    let trialPlan = await SubscriptionPlan.findOne({
      where: { 
        is_trial: true,
        is_default: true
      }
    });

    // If no default trial plan exists, look for any trial plan
    if (!trialPlan) {
      trialPlan = await SubscriptionPlan.findOne({
        where: { is_trial: true }
      });
    }

    if (!trialPlan) {
      // Create a default trial plan if none exists
      trialPlan = await SubscriptionPlan.create({
        name: 'Free Trial',
        description: 'Default free trial subscription plan',
        price_monthly: 0,
        price_yearly: 0,
        features: { trial: true },
        max_farms: 1,
        max_users: 5,
        storage_quota_gb: 1,
        max_file_size_mb: 10,
        is_active: true,
        is_trial: true,
        is_default: true,
        trial_duration_days: trialDays || 14,
        trial_features: { trial: true },
        requires_payment_method: false
      }, { transaction });
    }

    // Calculate trial end date
    const trialStartDate = new Date();
    const trialEndDate = new Date();
    // Use the trial_duration_days from the plan if available, otherwise use the provided trialDays or default to 14
    const durationDays = trialPlan.trial_duration_days || trialDays || 14;
    trialEndDate.setDate(trialEndDate.getDate() + durationDays);

    // Create a subscription transaction for the trial
    const subscriptionTransaction = await SubscriptionTransaction.create({
      farm_id: farmId,
      subscription_plan_id: trialPlan.id,
      amount: 0, // Trial is free
      currency: 'USD',
      status: 'succeeded',
      payment_method: 'trial',
      payment_reference: `trial-${Date.now()}`,
      transaction_date: new Date(),
      billing_period_start: trialStartDate,
      billing_period_end: trialEndDate
    }, { transaction });

    // Update farm with trial subscription plan
    await farm.update({
      subscription_plan_id: trialPlan.id,
      subscription_status: 'active',
      subscription_start_date: trialStartDate,
      subscription_end_date: trialEndDate
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Free trial subscription created successfully for farm',
      farm: {
        id: farm.id,
        name: farm.name,
        subscription_plan_id: farm.subscription_plan_id,
        subscription_status: farm.subscription_status,
        subscription_start_date: farm.subscription_start_date,
        subscription_end_date: farm.subscription_end_date
      },
      transaction: subscriptionTransaction
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating free trial subscription for farm:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a farm's trial period
export const updateFarmTrialPeriod = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const { trialDays, isTrial } = req.body;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If isTrial is true, ensure farm has a trial subscription plan
    if (isTrial) {
      // Find the default trial subscription plan
      let trialPlan = await SubscriptionPlan.findOne({
        where: { 
          is_trial: true,
          is_default: true
        }
      });

      // If no default trial plan exists, look for any trial plan
      if (!trialPlan) {
        trialPlan = await SubscriptionPlan.findOne({
          where: { is_trial: true }
        });
      }

      if (!trialPlan) {
        // Create a default trial plan if none exists
        trialPlan = await SubscriptionPlan.create({
          name: 'Free Trial',
          description: 'Default free trial subscription plan',
          price_monthly: 0,
          price_yearly: 0,
          features: { trial: true },
          max_farms: 1,
          max_users: 5,
          storage_quota_gb: 1,
          max_file_size_mb: 10,
          is_active: true,
          is_trial: true,
          is_default: true,
          trial_duration_days: trialDays || 14,
          trial_features: { trial: true },
          requires_payment_method: false
        }, { transaction });
      }

      // Calculate trial start and end dates
      const trialStartDate = new Date();
      const trialEndDate = new Date();
      // Use the trial_duration_days from the plan if available, otherwise use the provided trialDays or default to 14
      const durationDays = trialPlan.trial_duration_days || trialDays || 14;
      trialEndDate.setDate(trialEndDate.getDate() + durationDays);

      // Create a subscription transaction for the trial
      const subscriptionTransaction = await SubscriptionTransaction.create({
        farm_id: farmId,
        subscription_plan_id: trialPlan.id,
        amount: 0, // Trial is free
        currency: 'USD',
        status: 'succeeded',
        payment_method: 'trial',
        payment_reference: `trial-${Date.now()}`,
        transaction_date: new Date(),
        billing_period_start: trialStartDate,
        billing_period_end: trialEndDate
      }, { transaction });

      // Update farm with trial subscription plan
      await farm.update({
        subscription_plan_id: trialPlan.id,
        subscription_status: 'active',
        subscription_start_date: trialStartDate,
        subscription_end_date: trialEndDate
      }, { transaction });
    } else if (isTrial === false) {
      // If isTrial is false, do not change the subscription plan
      // The farm will need to select a paid plan separately
    }

    await transaction.commit();

    // Check if the farm has a trial plan
    const hasTrial = await SubscriptionPlan.findOne({
      where: { 
        id: farm.subscription_plan_id,
        is_trial: true
      }
    });

    // Get the latest subscription transaction for this farm
    const latestTransaction = await SubscriptionTransaction.findOne({
      where: { farm_id: farmId },
      order: [['transaction_date', 'DESC']]
    });

    return res.status(200).json({ 
      message: 'Farm trial period updated successfully',
      farm: {
        id: farm.id,
        name: farm.name,
        subscription_plan_id: farm.subscription_plan_id,
        subscription_status: farm.subscription_status,
        subscription_start_date: farm.subscription_start_date,
        subscription_end_date: farm.subscription_end_date,
        is_trial: !!hasTrial
      },
      transaction: latestTransaction
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating farm trial period:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Upgrade a farm to a full access plan
export const upgradeFarmPlan = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const { subscriptionPlanId, paymentDetails, billingCycle = 'monthly', promoCode } = req.body;

    // Validate required fields
    if (!subscriptionPlanId) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Subscription plan ID is required' 
      });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find subscription plan to ensure it exists
    const plan = await SubscriptionPlan.findByPk(subscriptionPlanId);
    if (!plan) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    // Process payment (this would integrate with a payment processor in a real implementation)
    // For now, we'll just simulate a successful payment
    const paymentSuccessful = true;

    if (!paymentSuccessful) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Payment processing failed' });
    }

    // Calculate billing period
    const billingPeriodStart = new Date();
    const billingPeriodEnd = new Date();

    // Set billing period based on billing cycle
    if (billingCycle === 'yearly') {
      billingPeriodEnd.setFullYear(billingPeriodEnd.getFullYear() + 1); // 1 year from now
    } else {
      billingPeriodEnd.setDate(billingPeriodEnd.getDate() + 30); // 30 days from now for monthly billing
    }

    // Determine base amount based on billing cycle
    const baseAmount = billingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly;

    let finalAmount = baseAmount;
    let appliedPromoCode = null;
    let discountAmount = 0;

    // Apply promo code if provided
    if (promoCode) {
      try {
        // Import PromoCode model
        const PromoCode = (await import('../models/PromoCode.js')).default;
        const { Op } = (await import('sequelize'));

        // Find the promo code
        const promoCodeObj = await PromoCode.findOne({
          where: { 
            code: promoCode,
            is_active: true,
            [Op.or]: [
              { valid_to: null },
              { valid_to: { [Op.gte]: new Date() } }
            ],
            valid_from: { [Op.lte]: new Date() },
            [Op.or]: [
              { subscription_plan_id: null },
              { subscription_plan_id: subscriptionPlanId }
            ],
            [Op.or]: [
              { farm_id: null },
              { farm_id: farmId }
            ]
          },
          transaction
        });

        if (promoCodeObj) {
          // Check if the promo code applies to the selected billing cycle
          const isValidForBillingCycle = 
            (billingCycle === 'monthly' && promoCodeObj.applies_to_monthly) || 
            (billingCycle === 'yearly' && promoCodeObj.applies_to_yearly);

          if (isValidForBillingCycle) {
            // Check if the promo code has reached its maximum uses
            if (promoCodeObj.max_uses === null || promoCodeObj.current_uses < promoCodeObj.max_uses) {
              // Apply discount
              if (promoCodeObj.discount_percent) {
                discountAmount = (baseAmount * promoCodeObj.discount_percent) / 100;
                finalAmount = baseAmount - discountAmount;
              } else if (promoCodeObj.discount_amount) {
                discountAmount = promoCodeObj.discount_amount;
                finalAmount = baseAmount - discountAmount;
                if (finalAmount < 0) finalAmount = 0;
              }

              // Increment the usage counter
              await promoCodeObj.update({
                current_uses: promoCodeObj.current_uses + 1
              }, { transaction });

              appliedPromoCode = {
                id: promoCodeObj.id,
                code: promoCodeObj.code,
                discount_percent: promoCodeObj.discount_percent,
                discount_amount: promoCodeObj.discount_amount,
                discount_months: promoCodeObj.discount_months
              };
            }
          }
        }
      } catch (error) {
        console.error('Error applying promo code:', error);
        // Continue without applying promo code if there's an error
      }
    }

    // Create subscription transaction record
    const subscriptionTransaction = await SubscriptionTransaction.create({
      farm_id: farmId,
      subscription_plan_id: subscriptionPlanId,
      amount: finalAmount,
      original_amount: baseAmount,
      discount_amount: discountAmount,
      promo_code: appliedPromoCode ? appliedPromoCode.code : null,
      promo_code_id: appliedPromoCode ? appliedPromoCode.id : null,
      currency: 'USD',
      status: 'succeeded',
      payment_method: paymentDetails?.paymentMethod || 'credit_card',
      payment_reference: paymentDetails?.paymentReference || `manual-${Date.now()}`,
      transaction_date: new Date(),
      billing_period_start: billingPeriodStart,
      billing_period_end: billingPeriodEnd,
      billing_cycle: billingCycle
    }, { transaction });

    // Update farm with subscription information
    await farm.update({
      subscription_plan_id: subscriptionPlanId,
      subscription_status: 'active',
      subscription_start_date: billingPeriodStart,
      subscription_end_date: billingPeriodEnd,
      billing_cycle: billingCycle,
      promo_code: appliedPromoCode ? appliedPromoCode.code : null,
      promo_code_id: appliedPromoCode ? appliedPromoCode.id : null,
      promo_discount_months: appliedPromoCode ? appliedPromoCode.discount_months : null
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({ 
      message: 'Farm upgraded to full access plan successfully',
      farm: {
        id: farm.id,
        name: farm.name,
        subscription_plan_id: farm.subscription_plan_id,
        subscription_status: farm.subscription_status,
        subscription_start_date: farm.subscription_start_date,
        subscription_end_date: farm.subscription_end_date,
        billing_cycle: billingCycle
      },
      transaction: subscriptionTransaction,
      appliedPromoCode
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error upgrading farm plan:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Upgrade a business user (supplier or vet) to a full access plan
export const upgradeBusinessUserPlan = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { userId } = req.params;
    const { subscriptionPlanId, paymentDetails } = req.body;

    // Validate required fields
    if (!subscriptionPlanId) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Subscription plan ID is required' 
      });
    }

    // Find user to ensure it exists
    const user = await User.findByPk(userId);
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify user is a business owner (supplier or vet)
    if (!user.is_business_owner || (user.user_type !== 'supplier' && user.user_type !== 'vet')) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Only business users (suppliers or vets) can be upgraded to a full access plan' 
      });
    }

    // Find subscription plan to ensure it exists
    const plan = await SubscriptionPlan.findByPk(subscriptionPlanId);
    if (!plan) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Subscription plan not found' });
    }

    // Process payment (this would integrate with a payment processor in a real implementation)
    // For now, we'll just simulate a successful payment
    const paymentSuccessful = true;

    if (!paymentSuccessful) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Payment processing failed' });
    }

    // Create subscription transaction record
    const subscriptionTransaction = await SubscriptionTransaction.create({
      user_id: userId,
      subscription_plan_id: subscriptionPlanId,
      amount: plan.price_monthly, // Assuming monthly billing by default
      currency: 'USD',
      status: 'succeeded',
      payment_method: paymentDetails?.paymentMethod || 'credit_card',
      payment_reference: paymentDetails?.paymentReference || `manual-${Date.now()}`,
      transaction_date: new Date(),
      billing_period_start: new Date(),
      billing_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    }, { transaction });

    // Update user with subscription information and approve their account
    await user.update({
      subscription_plan_id: subscriptionPlanId,
      is_approved: true // Approve the business account
    }, { transaction });

    await transaction.commit();

    // If this is a supplier, update their supplier record
    if (user.user_type === 'supplier') {
      try {
        const Supplier = require('../models/Supplier.js').default;
        const supplier = await Supplier.findOne({ where: { user_id: userId } });
        if (supplier) {
          await supplier.update({ is_active: true });
        }
      } catch (error) {
        console.error('Error updating supplier record:', error);
        // Continue even if supplier record update fails
      }
    }

    // If this is a vet, update their vet record
    if (user.user_type === 'vet') {
      try {
        const Vet = require('../models/Vet.js').default;
        const vet = await Vet.findOne({ where: { user_id: userId } });
        if (vet) {
          await vet.update({ is_active: true });
        }
      } catch (error) {
        console.error('Error updating vet record:', error);
        // Continue even if vet record update fails
      }
    }

    return res.status(200).json({ 
      message: 'Business user upgraded to full access plan successfully',
      user: {
        id: user.id,
        email: user.email,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        isApproved: user.is_approved,
        subscriptionPlanId: user.subscription_plan_id
      },
      transaction: subscriptionTransaction
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error upgrading business user plan:', error);
    return res.status(500).json({ error: error.message });
  }
};
