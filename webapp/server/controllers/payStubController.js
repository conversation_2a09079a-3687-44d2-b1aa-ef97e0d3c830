import PayStub from '../models/PayStub.js';
import Employee from '../models/Employee.js';
import { Op } from 'sequelize';

// Get all pay stubs with optional filtering
export const getPayStubs = async (req, res) => {
  try {
    const { employee_id, start_date, end_date, year, month } = req.query;
    
    // Build filter conditions
    const whereConditions = {};
    
    if (employee_id) {
      whereConditions.employee_id = employee_id;
    }
    
    // Filter by payment date range
    if (start_date || end_date) {
      whereConditions.payment_date = {};
      
      if (start_date) {
        whereConditions.payment_date[Op.gte] = new Date(start_date);
      }
      
      if (end_date) {
        const endDateTime = new Date(end_date);
        endDateTime.setHours(23, 59, 59, 999);
        whereConditions.payment_date[Op.lte] = endDateTime;
      }
    }
    
    // Filter by year and/or month
    if (year || month) {
      if (!whereConditions.payment_date) {
        whereConditions.payment_date = {};
      }
      
      if (year && month) {
        // Filter by specific year and month
        const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
        const endDate = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59, 999);
        
        whereConditions.payment_date[Op.between] = [startDate, endDate];
      } else if (year) {
        // Filter by year only
        const startDate = new Date(parseInt(year), 0, 1);
        const endDate = new Date(parseInt(year), 11, 31, 23, 59, 59, 999);
        
        whereConditions.payment_date[Op.between] = [startDate, endDate];
      } else if (month) {
        // Filter by month only (current year)
        const currentYear = new Date().getFullYear();
        const startDate = new Date(currentYear, parseInt(month) - 1, 1);
        const endDate = new Date(currentYear, parseInt(month), 0, 23, 59, 59, 999);
        
        whereConditions.payment_date[Op.between] = [startDate, endDate];
      }
    }
    
    // Find pay stubs with filters
    const payStubs = await PayStub.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ],
      order: [['payment_date', 'DESC']]
    });
    
    res.status(200).json(payStubs);
  } catch (error) {
    console.error('Error fetching pay stubs:', error);
    res.status(500).json({ message: 'Failed to fetch pay stubs', error: error.message });
  }
};

// Get pay stub by ID
export const getPayStubById = async (req, res) => {
  try {
    const { payStubId } = req.params;
    
    const payStub = await PayStub.findByPk(payStubId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    if (!payStub) {
      return res.status(404).json({ message: 'Pay stub not found' });
    }
    
    res.status(200).json(payStub);
  } catch (error) {
    console.error('Error fetching pay stub:', error);
    res.status(500).json({ message: 'Failed to fetch pay stub', error: error.message });
  }
};

// Create a new pay stub
export const createPayStub = async (req, res) => {
  try {
    const payStubData = req.body;
    
    // Validate required fields
    if (!payStubData.employee_id || !payStubData.pay_period_start || !payStubData.pay_period_end || 
        !payStubData.payment_date || !payStubData.gross_pay || !payStubData.net_pay) {
      return res.status(400).json({ 
        message: 'Employee ID, pay period start, pay period end, payment date, gross pay, and net pay are required' 
      });
    }
    
    // Create the pay stub
    const newPayStub = await PayStub.create(payStubData);
    
    // Fetch the created pay stub with associations
    const payStub = await PayStub.findByPk(newPayStub.id, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(201).json(payStub);
  } catch (error) {
    console.error('Error creating pay stub:', error);
    res.status(500).json({ message: 'Failed to create pay stub', error: error.message });
  }
};

// Update a pay stub
export const updatePayStub = async (req, res) => {
  try {
    const { payStubId } = req.params;
    const payStubData = req.body;
    
    // Find the pay stub
    const payStub = await PayStub.findByPk(payStubId);
    
    if (!payStub) {
      return res.status(404).json({ message: 'Pay stub not found' });
    }
    
    // Update the pay stub
    await payStub.update(payStubData);
    
    // Fetch the updated pay stub with associations
    const updatedPayStub = await PayStub.findByPk(payStubId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedPayStub);
  } catch (error) {
    console.error('Error updating pay stub:', error);
    res.status(500).json({ message: 'Failed to update pay stub', error: error.message });
  }
};

// Delete a pay stub
export const deletePayStub = async (req, res) => {
  try {
    const { payStubId } = req.params;
    
    // Find the pay stub
    const payStub = await PayStub.findByPk(payStubId);
    
    if (!payStub) {
      return res.status(404).json({ message: 'Pay stub not found' });
    }
    
    // Delete the pay stub
    await payStub.destroy();
    
    res.status(200).json({ message: 'Pay stub deleted successfully' });
  } catch (error) {
    console.error('Error deleting pay stub:', error);
    res.status(500).json({ message: 'Failed to delete pay stub', error: error.message });
  }
};

// Get pay stubs by employee ID
export const getPayStubsByEmployee = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { year } = req.query;
    
    // Build filter conditions
    const whereConditions = {
      employee_id: employeeId
    };
    
    // Filter by year if provided
    if (year) {
      const startDate = new Date(parseInt(year), 0, 1);
      const endDate = new Date(parseInt(year), 11, 31, 23, 59, 59, 999);
      
      whereConditions.payment_date = {
        [Op.between]: [startDate, endDate]
      };
    }
    
    const payStubs = await PayStub.findAll({
      where: whereConditions,
      order: [['payment_date', 'DESC']]
    });
    
    res.status(200).json(payStubs);
  } catch (error) {
    console.error('Error fetching employee pay stubs:', error);
    res.status(500).json({ message: 'Failed to fetch employee pay stubs', error: error.message });
  }
};

// Mark pay stub as viewed
export const markPayStubAsViewed = async (req, res) => {
  try {
    const { payStubId } = req.params;
    
    // Find the pay stub
    const payStub = await PayStub.findByPk(payStubId);
    
    if (!payStub) {
      return res.status(404).json({ message: 'Pay stub not found' });
    }
    
    // Update the pay stub status
    await payStub.update({
      status: 'viewed'
    });
    
    res.status(200).json({ message: 'Pay stub marked as viewed' });
  } catch (error) {
    console.error('Error marking pay stub as viewed:', error);
    res.status(500).json({ message: 'Failed to mark pay stub as viewed', error: error.message });
  }
};

// Get pay stub summary by employee
export const getPayStubSummary = async (req, res) => {
  try {
    const { employee_id, year } = req.query;
    
    if (!employee_id) {
      return res.status(400).json({ message: 'Employee ID is required' });
    }
    
    const currentYear = year ? parseInt(year) : new Date().getFullYear();
    const startOfYear = new Date(currentYear, 0, 1);
    const endOfYear = new Date(currentYear, 11, 31, 23, 59, 59, 999);
    
    // Find all pay stubs for the employee in the specified year
    const payStubs = await PayStub.findAll({
      where: {
        employee_id,
        payment_date: {
          [Op.between]: [startOfYear, endOfYear]
        }
      },
      order: [['payment_date', 'ASC']]
    });
    
    // Calculate summary
    const summary = {
      total_gross_pay: 0,
      total_net_pay: 0,
      total_regular_hours: 0,
      total_overtime_hours: 0,
      total_regular_pay: 0,
      total_overtime_pay: 0,
      total_deductions: 0,
      total_taxes: 0,
      pay_stubs_count: payStubs.length,
      quarterly_earnings: [0, 0, 0, 0], // Q1, Q2, Q3, Q4
      monthly_earnings: Array(12).fill(0) // Jan-Dec
    };
    
    payStubs.forEach(payStub => {
      summary.total_gross_pay += parseFloat(payStub.gross_pay) || 0;
      summary.total_net_pay += parseFloat(payStub.net_pay) || 0;
      summary.total_regular_hours += parseFloat(payStub.regular_hours) || 0;
      summary.total_overtime_hours += parseFloat(payStub.overtime_hours) || 0;
      summary.total_regular_pay += parseFloat(payStub.regular_pay) || 0;
      summary.total_overtime_pay += parseFloat(payStub.overtime_pay) || 0;
      
      // Calculate total deductions and taxes if available
      if (payStub.deductions) {
        const deductions = typeof payStub.deductions === 'string' 
          ? JSON.parse(payStub.deductions) 
          : payStub.deductions;
          
        if (deductions && typeof deductions === 'object') {
          Object.values(deductions).forEach(value => {
            if (typeof value === 'number') {
              summary.total_deductions += value;
            }
          });
        }
      }
      
      if (payStub.taxes) {
        const taxes = typeof payStub.taxes === 'string' 
          ? JSON.parse(payStub.taxes) 
          : payStub.taxes;
          
        if (taxes && typeof taxes === 'object') {
          Object.values(taxes).forEach(value => {
            if (typeof value === 'number') {
              summary.total_taxes += value;
            }
          });
        }
      }
      
      // Calculate quarterly and monthly earnings
      const paymentDate = new Date(payStub.payment_date);
      const month = paymentDate.getMonth();
      const quarter = Math.floor(month / 3);
      
      summary.quarterly_earnings[quarter] += parseFloat(payStub.gross_pay) || 0;
      summary.monthly_earnings[month] += parseFloat(payStub.gross_pay) || 0;
    });
    
    res.status(200).json(summary);
  } catch (error) {
    console.error('Error generating pay stub summary:', error);
    res.status(500).json({ message: 'Failed to generate pay stub summary', error: error.message });
  }
};