import InventoryCategory from '../models/InventoryCategory.js';
import { Op } from 'sequelize';

// Get all inventory categories for a farm
export const getInventoryCategories = async (req, res) => {
  try {
    const { farmId } = req.query;
    
    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const categories = await InventoryCategory.findAll({
      where: { farm_id: farmId },
      order: [['name', 'ASC']]
    });

    res.status(200).json(categories);
  } catch (error) {
    console.error('Error fetching inventory categories:', error);
    res.status(500).json({ message: 'Failed to fetch inventory categories', error: error.message });
  }
};

// Get a single inventory category by ID
export const getInventoryCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    const category = await InventoryCategory.findByPk(id);
    
    if (!category) {
      return res.status(404).json({ message: 'Inventory category not found' });
    }
    
    res.status(200).json(category);
  } catch (error) {
    console.error('Error fetching inventory category:', error);
    res.status(500).json({ message: 'Failed to fetch inventory category', error: error.message });
  }
};

// Create a new inventory category
export const createInventoryCategory = async (req, res) => {
  try {
    const { farm_id, name, description } = req.body;
    
    if (!farm_id || !name) {
      return res.status(400).json({ message: 'Farm ID and name are required' });
    }
    
    const newCategory = await InventoryCategory.create({
      farm_id,
      name,
      description
    });
    
    res.status(201).json(newCategory);
  } catch (error) {
    console.error('Error creating inventory category:', error);
    res.status(500).json({ message: 'Failed to create inventory category', error: error.message });
  }
};

// Update an inventory category
export const updateInventoryCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    
    const category = await InventoryCategory.findByPk(id);
    
    if (!category) {
      return res.status(404).json({ message: 'Inventory category not found' });
    }
    
    // Update the category
    await category.update({
      name: name || category.name,
      description: description !== undefined ? description : category.description,
      updated_at: new Date()
    });
    
    res.status(200).json(category);
  } catch (error) {
    console.error('Error updating inventory category:', error);
    res.status(500).json({ message: 'Failed to update inventory category', error: error.message });
  }
};

// Delete an inventory category
export const deleteInventoryCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    const category = await InventoryCategory.findByPk(id);
    
    if (!category) {
      return res.status(404).json({ message: 'Inventory category not found' });
    }
    
    await category.destroy();
    
    res.status(200).json({ message: 'Inventory category deleted successfully' });
  } catch (error) {
    console.error('Error deleting inventory category:', error);
    res.status(500).json({ message: 'Failed to delete inventory category', error: error.message });
  }
};