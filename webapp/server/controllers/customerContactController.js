import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_PORT === '465',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// Send a contact message to the farm
export const contactFarm = async (req, res) => {
  try {
    const { subject, message } = req.body;
    const customer = req.customer;
    const farmId = req.farmId;

    // Validate required fields
    if (!subject) {
      return res.status(400).json({ error: 'Subject is required' });
    }

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get farm email (use billing email if available, otherwise use a default)
    const farmEmail = farm.billing_email || process.env.EMAIL_FROM || '<EMAIL>';

    // Send email to farm
    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: farmEmail,
      subject: `Customer Contact: ${subject}`,
      html: `
        <h1>Customer Contact Message</h1>
        <p><strong>From:</strong> ${customer.name} (${customer.email})</p>
        <p><strong>Subject:</strong> ${subject}</p>
        <h2>Message:</h2>
        <p>${message}</p>
      `
    });

    // TODO: In a real implementation, we would also store this message in a database table

    return res.status(200).json({ 
      message: 'Contact message sent successfully'
    });
  } catch (error) {
    console.error('Error sending contact message:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get farm contact information
export const getFarmContactInfo = async (req, res) => {
  try {
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Return farm contact information
    // Note: In a real implementation, you might want to have a separate table for farm contact info
    return res.status(200).json({
      farmName: farm.name,
      email: farm.billing_email || null,
      address: farm.billing_address || null,
      city: farm.billing_city || null,
      state: farm.billing_state || null,
      zipCode: farm.billing_zip_code || null,
      country: farm.billing_country || null
    });
  } catch (error) {
    console.error('Error getting farm contact info:', error);
    return res.status(500).json({ error: error.message });
  }
};