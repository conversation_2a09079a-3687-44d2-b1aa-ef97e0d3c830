import TimeEntry from '../models/TimeEntry.js';
import Employee from '../models/Employee.js';
import User from '../models/User.js';
import Task from '../models/Task.js';
import { Op } from 'sequelize';

// Get all time entries with optional filtering
export const getTimeEntries = async (req, res) => {
  try {
    const { employee_id, start_date, end_date, approved, category, activity_type } = req.query;
    
    // Build filter conditions
    const whereConditions = {};
    
    if (employee_id) {
      whereConditions.employee_id = employee_id;
    }
    
    if (start_date || end_date) {
      whereConditions.start_time = {};
      
      if (start_date) {
        whereConditions.start_time[Op.gte] = new Date(start_date);
      }
      
      if (end_date) {
        const endDateTime = new Date(end_date);
        endDateTime.setHours(23, 59, 59, 999);
        whereConditions.start_time[Op.lte] = endDateTime;
      }
    }
    
    if (approved !== undefined) {
      whereConditions.approved = approved === 'true';
    }
    
    if (category) {
      whereConditions.category = category;
    }
    
    if (activity_type) {
      whereConditions.activity_type = activity_type;
    }
    
    // Find time entries with filters
    const timeEntries = await TimeEntry.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: Task,
          attributes: ['id', 'title'],
        },
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ],
      order: [['start_time', 'DESC']]
    });
    
    res.status(200).json(timeEntries);
  } catch (error) {
    console.error('Error fetching time entries:', error);
    res.status(500).json({ message: 'Failed to fetch time entries', error: error.message });
  }
};

// Get time entry by ID
export const getTimeEntryById = async (req, res) => {
  try {
    const { timeEntryId } = req.params;
    
    const timeEntry = await TimeEntry.findByPk(timeEntryId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: Task,
          attributes: ['id', 'title'],
        },
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }
    
    res.status(200).json(timeEntry);
  } catch (error) {
    console.error('Error fetching time entry:', error);
    res.status(500).json({ message: 'Failed to fetch time entry', error: error.message });
  }
};

// Create a new time entry
export const createTimeEntry = async (req, res) => {
  try {
    const timeEntryData = req.body;
    
    // Validate required fields
    if (!timeEntryData.employee_id || !timeEntryData.start_time) {
      return res.status(400).json({ message: 'Employee ID and start time are required' });
    }
    
    // Calculate duration if end_time is provided
    if (timeEntryData.end_time && !timeEntryData.duration) {
      const startTime = new Date(timeEntryData.start_time);
      const endTime = new Date(timeEntryData.end_time);
      const durationMs = endTime - startTime;
      const durationHours = durationMs / (1000 * 60 * 60);
      
      // Subtract break duration if provided
      if (timeEntryData.break_duration) {
        timeEntryData.duration = durationHours - parseFloat(timeEntryData.break_duration);
      } else {
        timeEntryData.duration = durationHours;
      }
    }
    
    // Create the time entry
    const newTimeEntry = await TimeEntry.create(timeEntryData);
    
    // Fetch the created time entry with associations
    const timeEntry = await TimeEntry.findByPk(newTimeEntry.id, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: Task,
          attributes: ['id', 'title'],
        }
      ]
    });
    
    res.status(201).json(timeEntry);
  } catch (error) {
    console.error('Error creating time entry:', error);
    res.status(500).json({ message: 'Failed to create time entry', error: error.message });
  }
};

// Update a time entry
export const updateTimeEntry = async (req, res) => {
  try {
    const { timeEntryId } = req.params;
    const timeEntryData = req.body;
    
    // Find the time entry
    const timeEntry = await TimeEntry.findByPk(timeEntryId);
    
    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }
    
    // Calculate duration if end_time is provided and duration is not
    if (timeEntryData.end_time && !timeEntryData.duration) {
      const startTime = new Date(timeEntryData.start_time || timeEntry.start_time);
      const endTime = new Date(timeEntryData.end_time);
      const durationMs = endTime - startTime;
      const durationHours = durationMs / (1000 * 60 * 60);
      
      // Subtract break duration if provided
      const breakDuration = timeEntryData.break_duration !== undefined 
        ? parseFloat(timeEntryData.break_duration) 
        : (timeEntry.break_duration || 0);
      
      timeEntryData.duration = durationHours - breakDuration;
    }
    
    // Update the time entry
    await timeEntry.update(timeEntryData);
    
    // Fetch the updated time entry with associations
    const updatedTimeEntry = await TimeEntry.findByPk(timeEntryId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: Task,
          attributes: ['id', 'title'],
        },
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedTimeEntry);
  } catch (error) {
    console.error('Error updating time entry:', error);
    res.status(500).json({ message: 'Failed to update time entry', error: error.message });
  }
};

// Delete a time entry
export const deleteTimeEntry = async (req, res) => {
  try {
    const { timeEntryId } = req.params;
    
    // Find the time entry
    const timeEntry = await TimeEntry.findByPk(timeEntryId);
    
    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }
    
    // Delete the time entry
    await timeEntry.destroy();
    
    res.status(200).json({ message: 'Time entry deleted successfully' });
  } catch (error) {
    console.error('Error deleting time entry:', error);
    res.status(500).json({ message: 'Failed to delete time entry', error: error.message });
  }
};

// Get time entries by employee ID
export const getTimeEntriesByEmployee = async (req, res) => {
  try {
    const { employeeId } = req.params;
    const { start_date, end_date } = req.query;
    
    // Build filter conditions
    const whereConditions = {
      employee_id: employeeId
    };
    
    if (start_date || end_date) {
      whereConditions.start_time = {};
      
      if (start_date) {
        whereConditions.start_time[Op.gte] = new Date(start_date);
      }
      
      if (end_date) {
        const endDateTime = new Date(end_date);
        endDateTime.setHours(23, 59, 59, 999);
        whereConditions.start_time[Op.lte] = endDateTime;
      }
    }
    
    const timeEntries = await TimeEntry.findAll({
      where: whereConditions,
      include: [
        {
          model: Task,
          attributes: ['id', 'title'],
        }
      ],
      order: [['start_time', 'DESC']]
    });
    
    res.status(200).json(timeEntries);
  } catch (error) {
    console.error('Error fetching employee time entries:', error);
    res.status(500).json({ message: 'Failed to fetch employee time entries', error: error.message });
  }
};

// Approve a time entry
export const approveTimeEntry = async (req, res) => {
  try {
    const { timeEntryId } = req.params;
    const { approved, approved_by, notes } = req.body;
    
    // Find the time entry
    const timeEntry = await TimeEntry.findByPk(timeEntryId);
    
    if (!timeEntry) {
      return res.status(404).json({ message: 'Time entry not found' });
    }
    
    // Update approval status
    await timeEntry.update({
      approved: approved !== undefined ? approved : true,
      approved_by: approved_by,
      approved_at: approved ? new Date() : null,
      notes: notes || timeEntry.notes
    });
    
    // Fetch the updated time entry with associations
    const updatedTimeEntry = await TimeEntry.findByPk(timeEntryId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: Task,
          attributes: ['id', 'title'],
        },
        {
          model: User,
          as: 'approver',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedTimeEntry);
  } catch (error) {
    console.error('Error approving time entry:', error);
    res.status(500).json({ message: 'Failed to approve time entry', error: error.message });
  }
};

// Get time entry summary by employee
export const getTimeEntrySummary = async (req, res) => {
  try {
    const { employee_id, start_date, end_date } = req.query;
    
    if (!start_date || !end_date) {
      return res.status(400).json({ message: 'Start date and end date are required' });
    }
    
    // Build filter conditions
    const whereConditions = {};
    
    if (employee_id) {
      whereConditions.employee_id = employee_id;
    }
    
    whereConditions.start_time = {
      [Op.gte]: new Date(start_date),
      [Op.lte]: new Date(end_date)
    };
    
    // Get time entries
    const timeEntries = await TimeEntry.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name', 'hourly_rate'],
        }
      ]
    });
    
    // Calculate summary
    const summary = {
      total_hours: 0,
      total_approved_hours: 0,
      total_unapproved_hours: 0,
      categories: {},
      activity_types: {}
    };
    
    timeEntries.forEach(entry => {
      const hours = parseFloat(entry.duration) || 0;
      
      summary.total_hours += hours;
      
      if (entry.approved) {
        summary.total_approved_hours += hours;
      } else {
        summary.total_unapproved_hours += hours;
      }
      
      // Summarize by category
      if (entry.category) {
        if (!summary.categories[entry.category]) {
          summary.categories[entry.category] = 0;
        }
        summary.categories[entry.category] += hours;
      }
      
      // Summarize by activity type
      if (entry.activity_type) {
        if (!summary.activity_types[entry.activity_type]) {
          summary.activity_types[entry.activity_type] = 0;
        }
        summary.activity_types[entry.activity_type] += hours;
      }
    });
    
    res.status(200).json(summary);
  } catch (error) {
    console.error('Error generating time entry summary:', error);
    res.status(500).json({ message: 'Failed to generate time entry summary', error: error.message });
  }
};