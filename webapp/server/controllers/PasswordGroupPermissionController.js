import PasswordGroupPermission from '../models/PasswordGroupPermission.js';
import PasswordGroup from '../models/PasswordGroup.js';
import Role from '../models/Role.js';
import User from '../models/User.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';

/**
 * Controller for managing password group permissions
 */
class PasswordGroupPermissionController {
  /**
   * Get all permissions for a password group
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPermissions(req, res) {
    try {
      const { groupId } = req.params;
      const userId = req.user.id;
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(groupId);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has manage permission for this group
      const hasManagePermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: groupId,
          user_id: userId,
          permission_type: 'manage'
        }
      });
      
      if (!hasManagePermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view permissions for this group'
        });
      }
      
      // Get all permissions for the group
      const permissions = await PasswordGroupPermission.findAll({
        where: { group_id: groupId },
        include: [
          {
            model: Role,
            as: 'role',
            attributes: ['id', 'name', 'description']
          },
          {
            model: User,
            as: 'user',
            attributes: ['id', 'email', 'first_name', 'last_name']
          }
        ]
      });
      
      return res.status(200).json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('Error getting permissions:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while getting permissions',
        error: error.message
      });
    }
  }
  
  /**
   * Add a permission to a password group
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async addPermission(req, res) {
    const transaction = await sequelize.transaction();
    
    try {
      const { groupId } = req.params;
      const { roleId, userId: targetUserId, permissionType } = req.body;
      const currentUserId = req.user.id;
      
      // Validate permission type
      if (!['view', 'edit', 'manage'].includes(permissionType)) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid permission type. Must be one of: view, edit, manage'
        });
      }
      
      // Ensure either roleId or userId is provided, but not both
      if ((roleId && targetUserId) || (!roleId && !targetUserId)) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Either roleId or userId must be provided, but not both'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(groupId);
      
      if (!passwordGroup) {
        await transaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        await transaction.rollback();
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has manage permission for this group
      const hasManagePermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: groupId,
          user_id: currentUserId,
          permission_type: 'manage'
        }
      });
      
      if (!hasManagePermission) {
        await transaction.rollback();
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to manage permissions for this group'
        });
      }
      
      // If roleId is provided, check if the role exists and is associated with the farm
      if (roleId) {
        const role = await Role.findByPk(roleId);
        
        if (!role) {
          await transaction.rollback();
          return res.status(404).json({
            success: false,
            message: 'Role not found'
          });
        }
        
        // Check if the role is associated with the farm or is a global role
        if (role.farm_id && role.farm_id !== passwordGroup.farm_id) {
          await transaction.rollback();
          return res.status(403).json({
            success: false,
            message: 'Role is not associated with this farm'
          });
        }
        
        // Check if permission already exists for this role
        const existingRolePermission = await PasswordGroupPermission.findOne({
          where: {
            group_id: groupId,
            role_id: roleId
          }
        });
        
        if (existingRolePermission) {
          // Update existing permission
          await existingRolePermission.update({
            permission_type: permissionType
          }, { transaction });
          
          await transaction.commit();
          
          return res.status(200).json({
            success: true,
            data: existingRolePermission,
            message: 'Permission updated successfully'
          });
        }
        
        // Create new permission for role
        const newPermission = await PasswordGroupPermission.create({
          group_id: groupId,
          role_id: roleId,
          permission_type: permissionType
        }, { transaction });
        
        await transaction.commit();
        
        return res.status(201).json({
          success: true,
          data: newPermission,
          message: 'Permission added successfully'
        });
      }
      
      // If userId is provided, check if the user exists
      if (targetUserId) {
        const user = await User.findByPk(targetUserId);
        
        if (!user) {
          await transaction.rollback();
          return res.status(404).json({
            success: false,
            message: 'User not found'
          });
        }
        
        // Check if the user has access to the farm
        const targetUserFarm = await user.getUserFarms({
          where: { farm_id: passwordGroup.farm_id }
        });
        
        if (!targetUserFarm || targetUserFarm.length === 0) {
          await transaction.rollback();
          return res.status(403).json({
            success: false,
            message: 'Target user does not have access to this farm'
          });
        }
        
        // Check if permission already exists for this user
        const existingUserPermission = await PasswordGroupPermission.findOne({
          where: {
            group_id: groupId,
            user_id: targetUserId
          }
        });
        
        if (existingUserPermission) {
          // Update existing permission
          await existingUserPermission.update({
            permission_type: permissionType
          }, { transaction });
          
          await transaction.commit();
          
          return res.status(200).json({
            success: true,
            data: existingUserPermission,
            message: 'Permission updated successfully'
          });
        }
        
        // Create new permission for user
        const newPermission = await PasswordGroupPermission.create({
          group_id: groupId,
          user_id: targetUserId,
          permission_type: permissionType
        }, { transaction });
        
        await transaction.commit();
        
        return res.status(201).json({
          success: true,
          data: newPermission,
          message: 'Permission added successfully'
        });
      }
    } catch (error) {
      await transaction.rollback();
      console.error('Error adding permission:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while adding permission',
        error: error.message
      });
    }
  }
  
  /**
   * Update a permission
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updatePermission(req, res) {
    try {
      const { id } = req.params;
      const { permissionType } = req.body;
      const userId = req.user.id;
      
      // Validate permission type
      if (!['view', 'edit', 'manage'].includes(permissionType)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid permission type. Must be one of: view, edit, manage'
        });
      }
      
      // Find the permission
      const permission = await PasswordGroupPermission.findByPk(id);
      
      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission not found'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(permission.group_id);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has manage permission for this group
      const hasManagePermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: permission.group_id,
          user_id: userId,
          permission_type: 'manage'
        }
      });
      
      if (!hasManagePermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update permissions for this group'
        });
      }
      
      // Don't allow updating the last manage permission
      if (permission.permission_type === 'manage' && permissionType !== 'manage') {
        // Count how many manage permissions exist for this group
        const managePermissionsCount = await PasswordGroupPermission.count({
          where: {
            group_id: permission.group_id,
            permission_type: 'manage'
          }
        });
        
        if (managePermissionsCount <= 1) {
          return res.status(400).json({
            success: false,
            message: 'Cannot remove the last manage permission for a group'
          });
        }
      }
      
      // Update the permission
      await permission.update({
        permission_type: permissionType
      });
      
      return res.status(200).json({
        success: true,
        data: permission,
        message: 'Permission updated successfully'
      });
    } catch (error) {
      console.error('Error updating permission:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while updating the permission',
        error: error.message
      });
    }
  }
  
  /**
   * Delete a permission
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deletePermission(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      // Find the permission
      const permission = await PasswordGroupPermission.findByPk(id);
      
      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission not found'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(permission.group_id);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has manage permission for this group
      const hasManagePermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: permission.group_id,
          user_id: userId,
          permission_type: 'manage'
        }
      });
      
      if (!hasManagePermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete permissions for this group'
        });
      }
      
      // Don't allow deleting your own manage permission
      if (permission.user_id === userId && permission.permission_type === 'manage') {
        // Count how many manage permissions exist for this group
        const managePermissionsCount = await PasswordGroupPermission.count({
          where: {
            group_id: permission.group_id,
            permission_type: 'manage'
          }
        });
        
        if (managePermissionsCount <= 1) {
          return res.status(400).json({
            success: false,
            message: 'Cannot delete the last manage permission for a group'
          });
        }
      }
      
      // Delete the permission
      await permission.destroy();
      
      return res.status(200).json({
        success: true,
        message: 'Permission deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting permission:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while deleting the permission',
        error: error.message
      });
    }
  }
}

export default new PasswordGroupPermissionController();