import DriverSchedule from '../models/DriverSchedule.js';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import Delivery from '../models/Delivery.js';
import Pickup from '../models/Pickup.js';
import { sequelize } from '../config/database.js';

// Create a new driver schedule
export const createDriverSchedule = async (req, res) => {
  try {
    const { 
      farmId,
      driverId,
      deliveryId,
      pickupId,
      scheduleType,
      title,
      description,
      startTime,
      endTime,
      isAllDay = false,
      status = 'scheduled',
      location,
      locationLat,
      locationLng,
      priority = 'medium',
      recurrencePattern,
      recurrenceEndDate,
      notes
    } = req.body;

    // Validate required fields
    if (!farmId || !driverId || !scheduleType || !title || !startTime || !endTime) {
      return res.status(400).json({ 
        error: 'Farm ID, driver ID, schedule type, title, start time, and end time are required' 
      });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if driver exists
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // If deliveryId is provided, check if delivery exists
    if (deliveryId) {
      const delivery = await Delivery.findByPk(deliveryId);
      if (!delivery) {
        return res.status(404).json({ error: 'Delivery not found' });
      }
    }

    // If pickupId is provided, check if pickup exists
    if (pickupId) {
      const pickup = await Pickup.findByPk(pickupId);
      if (!pickup) {
        return res.status(404).json({ error: 'Pickup not found' });
      }
    }

    // Create new driver schedule
    const driverSchedule = await DriverSchedule.create({
      farm_id: farmId,
      driver_id: driverId,
      delivery_id: deliveryId || null,
      pickup_id: pickupId || null,
      schedule_type: scheduleType,
      title,
      description: description || null,
      start_time: startTime,
      end_time: endTime,
      is_all_day: isAllDay,
      status,
      location: location || null,
      location_lat: locationLat || null,
      location_lng: locationLng || null,
      priority,
      recurrence_pattern: recurrencePattern || null,
      recurrence_end_date: recurrenceEndDate || null,
      notes: notes || null
    });

    return res.status(201).json({
      driverSchedule: {
        id: driverSchedule.id,
        farmId: driverSchedule.farm_id,
        driverId: driverSchedule.driver_id,
        deliveryId: driverSchedule.delivery_id,
        pickupId: driverSchedule.pickup_id,
        scheduleType: driverSchedule.schedule_type,
        title: driverSchedule.title,
        description: driverSchedule.description,
        startTime: driverSchedule.start_time,
        endTime: driverSchedule.end_time,
        isAllDay: driverSchedule.is_all_day,
        status: driverSchedule.status,
        location: driverSchedule.location,
        locationLat: driverSchedule.location_lat,
        locationLng: driverSchedule.location_lng,
        priority: driverSchedule.priority,
        recurrencePattern: driverSchedule.recurrence_pattern,
        recurrenceEndDate: driverSchedule.recurrence_end_date,
        notes: driverSchedule.notes,
        createdAt: driverSchedule.created_at,
        updatedAt: driverSchedule.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating driver schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all driver schedules with optional filtering
export const getDriverSchedules = async (req, res) => {
  try {
    const { 
      farmId, 
      driverId,
      deliveryId,
      pickupId,
      scheduleType,
      status,
      startDate,
      endDate,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (farmId) {
      whereClause.farm_id = farmId;
    }

    if (driverId) {
      whereClause.driver_id = driverId;
    }

    if (deliveryId) {
      whereClause.delivery_id = deliveryId;
    }

    if (pickupId) {
      whereClause.pickup_id = pickupId;
    }

    if (scheduleType) {
      whereClause.schedule_type = scheduleType;
    }

    if (status) {
      whereClause.status = status;
    }

    // Date range filtering for start_time
    if (startDate || endDate) {
      whereClause.start_time = {};

      if (startDate) {
        whereClause.start_time[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.start_time[sequelize.Op.lte] = new Date(endDate);
      }
    }

    if (search) {
      whereClause[sequelize.Op.or] = [
        { title: { [sequelize.Op.iLike]: `%${search}%` } },
        { description: { [sequelize.Op.iLike]: `%${search}%` } },
        { location: { [sequelize.Op.iLike]: `%${search}%` } },
        { notes: { [sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // Get driver schedules with pagination
    const driverSchedules = await DriverSchedule.findAll({
      where: whereClause,
      include: [
        { model: Farm, as: 'driverScheduleFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'driver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Delivery, as: 'delivery', attributes: ['id', 'status', 'scheduled_date'] },
        { model: Pickup, as: 'pickup', attributes: ['id', 'status', 'scheduled_date'] }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['start_time', 'ASC']]
    });

    // Get total count for pagination
    const totalCount = await DriverSchedule.count({ where: whereClause });

    return res.status(200).json({
      driverSchedules: driverSchedules.map(schedule => ({
        id: schedule.id,
        farmId: schedule.farm_id,
        driverId: schedule.driver_id,
        deliveryId: schedule.delivery_id,
        pickupId: schedule.pickup_id,
        scheduleType: schedule.schedule_type,
        title: schedule.title,
        description: schedule.description,
        startTime: schedule.start_time,
        endTime: schedule.end_time,
        isAllDay: schedule.is_all_day,
        status: schedule.status,
        location: schedule.location,
        locationLat: schedule.location_lat,
        locationLng: schedule.location_lng,
        priority: schedule.priority,
        recurrencePattern: schedule.recurrence_pattern,
        recurrenceEndDate: schedule.recurrence_end_date,
        notes: schedule.notes,
        farm: schedule.driverScheduleFarm,
        driver: schedule.driver,
        delivery: schedule.delivery,
        pickup: schedule.pickup,
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting driver schedules:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get driver schedule by ID
export const getDriverScheduleById = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    const driverSchedule = await DriverSchedule.findByPk(scheduleId, {
      include: [
        { model: Farm, as: 'driverScheduleFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'driver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Delivery, as: 'delivery', attributes: ['id', 'status', 'scheduled_date'] },
        { model: Pickup, as: 'pickup', attributes: ['id', 'status', 'scheduled_date'] }
      ]
    });

    if (!driverSchedule) {
      return res.status(404).json({ error: 'Driver schedule not found' });
    }

    return res.status(200).json({
      driverSchedule: {
        id: driverSchedule.id,
        farmId: driverSchedule.farm_id,
        driverId: driverSchedule.driver_id,
        deliveryId: driverSchedule.delivery_id,
        pickupId: driverSchedule.pickup_id,
        scheduleType: driverSchedule.schedule_type,
        title: driverSchedule.title,
        description: driverSchedule.description,
        startTime: driverSchedule.start_time,
        endTime: driverSchedule.end_time,
        isAllDay: driverSchedule.is_all_day,
        status: driverSchedule.status,
        location: driverSchedule.location,
        locationLat: driverSchedule.location_lat,
        locationLng: driverSchedule.location_lng,
        priority: driverSchedule.priority,
        recurrencePattern: driverSchedule.recurrence_pattern,
        recurrenceEndDate: driverSchedule.recurrence_end_date,
        notes: driverSchedule.notes,
        farm: driverSchedule.driverScheduleFarm,
        driver: driverSchedule.driver,
        delivery: driverSchedule.delivery,
        pickup: driverSchedule.pickup,
        createdAt: driverSchedule.created_at,
        updatedAt: driverSchedule.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting driver schedule by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update driver schedule
export const updateDriverSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { 
      driverId,
      deliveryId,
      pickupId,
      scheduleType,
      title,
      description,
      startTime,
      endTime,
      isAllDay,
      status,
      location,
      locationLat,
      locationLng,
      priority,
      recurrencePattern,
      recurrenceEndDate,
      notes
    } = req.body;

    const driverSchedule = await DriverSchedule.findByPk(scheduleId);
    if (!driverSchedule) {
      return res.status(404).json({ error: 'Driver schedule not found' });
    }

    // If driverId is provided, check if driver exists
    if (driverId !== undefined) {
      if (driverId) {
        const driver = await Driver.findByPk(driverId);
        if (!driver) {
          return res.status(404).json({ error: 'Driver not found' });
        }
        driverSchedule.driver_id = driverId;
      } else {
        return res.status(400).json({ error: 'Driver ID cannot be null' });
      }
    }

    // If deliveryId is provided, check if delivery exists
    if (deliveryId !== undefined) {
      if (deliveryId) {
        const delivery = await Delivery.findByPk(deliveryId);
        if (!delivery) {
          return res.status(404).json({ error: 'Delivery not found' });
        }
        driverSchedule.delivery_id = deliveryId;
      } else {
        driverSchedule.delivery_id = null;
      }
    }

    // If pickupId is provided, check if pickup exists
    if (pickupId !== undefined) {
      if (pickupId) {
        const pickup = await Pickup.findByPk(pickupId);
        if (!pickup) {
          return res.status(404).json({ error: 'Pickup not found' });
        }
        driverSchedule.pickup_id = pickupId;
      } else {
        driverSchedule.pickup_id = null;
      }
    }

    // Update driver schedule fields if provided
    if (scheduleType !== undefined) driverSchedule.schedule_type = scheduleType;
    if (title !== undefined) driverSchedule.title = title;
    if (description !== undefined) driverSchedule.description = description;
    if (startTime !== undefined) driverSchedule.start_time = startTime;
    if (endTime !== undefined) driverSchedule.end_time = endTime;
    if (isAllDay !== undefined) driverSchedule.is_all_day = isAllDay;
    if (status !== undefined) driverSchedule.status = status;
    if (location !== undefined) driverSchedule.location = location;
    if (locationLat !== undefined) driverSchedule.location_lat = locationLat;
    if (locationLng !== undefined) driverSchedule.location_lng = locationLng;
    if (priority !== undefined) driverSchedule.priority = priority;
    if (recurrencePattern !== undefined) driverSchedule.recurrence_pattern = recurrencePattern;
    if (recurrenceEndDate !== undefined) driverSchedule.recurrence_end_date = recurrenceEndDate;
    if (notes !== undefined) driverSchedule.notes = notes;

    await driverSchedule.save();

    return res.status(200).json({
      driverSchedule: {
        id: driverSchedule.id,
        farmId: driverSchedule.farm_id,
        driverId: driverSchedule.driver_id,
        deliveryId: driverSchedule.delivery_id,
        pickupId: driverSchedule.pickup_id,
        scheduleType: driverSchedule.schedule_type,
        title: driverSchedule.title,
        description: driverSchedule.description,
        startTime: driverSchedule.start_time,
        endTime: driverSchedule.end_time,
        isAllDay: driverSchedule.is_all_day,
        status: driverSchedule.status,
        location: driverSchedule.location,
        locationLat: driverSchedule.location_lat,
        locationLng: driverSchedule.location_lng,
        priority: driverSchedule.priority,
        recurrencePattern: driverSchedule.recurrence_pattern,
        recurrenceEndDate: driverSchedule.recurrence_end_date,
        notes: driverSchedule.notes,
        createdAt: driverSchedule.created_at,
        updatedAt: driverSchedule.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating driver schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete driver schedule
export const deleteDriverSchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    const driverSchedule = await DriverSchedule.findByPk(scheduleId);
    if (!driverSchedule) {
      return res.status(404).json({ error: 'Driver schedule not found' });
    }

    await driverSchedule.destroy();

    return res.status(200).json({
      message: 'Driver schedule deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting driver schedule:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get driver schedules by driver ID
export const getDriverSchedulesByDriverId = async (req, res) => {
  try {
    const { driverId } = req.params;
    const { startDate, endDate } = req.query;

    // Check if driver exists
    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Build the where clause
    const whereClause = {
      driver_id: driverId
    };

    // Date range filtering
    if (startDate || endDate) {
      whereClause.start_time = {};

      if (startDate) {
        whereClause.start_time[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.start_time[sequelize.Op.lte] = new Date(endDate);
      }
    }

    const driverSchedules = await DriverSchedule.findAll({
      where: whereClause,
      include: [
        { model: Delivery, as: 'delivery', attributes: ['id', 'status', 'scheduled_date'] },
        { model: Pickup, as: 'pickup', attributes: ['id', 'status', 'scheduled_date'] }
      ],
      order: [['start_time', 'ASC']]
    });

    return res.status(200).json({
      driverSchedules: driverSchedules.map(schedule => ({
        id: schedule.id,
        farmId: schedule.farm_id,
        driverId: schedule.driver_id,
        deliveryId: schedule.delivery_id,
        pickupId: schedule.pickup_id,
        scheduleType: schedule.schedule_type,
        title: schedule.title,
        description: schedule.description,
        startTime: schedule.start_time,
        endTime: schedule.end_time,
        isAllDay: schedule.is_all_day,
        status: schedule.status,
        location: schedule.location,
        locationLat: schedule.location_lat,
        locationLng: schedule.location_lng,
        priority: schedule.priority,
        recurrencePattern: schedule.recurrence_pattern,
        recurrenceEndDate: schedule.recurrence_end_date,
        notes: schedule.notes,
        delivery: schedule.delivery,
        pickup: schedule.pickup,
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at
      }))
    });
  } catch (error) {
    console.error('Error getting driver schedules by driver ID:', error);
    return res.status(500).json({ error: error.message });
  }
};
