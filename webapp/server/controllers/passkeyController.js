import User from '../models/User.js';
import { generateTokens } from './authController.js';
import { createSession } from './sessionController.js';
import { getClientIp } from '../utils/requestUtils.js';
import { setAuthTokenCookie, setRefreshTokenCookie } from '../utils/cookieUtils.js';

/**
 * Generate a challenge for passkey registration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateRegistrationChallenge = async (req, res) => {
  try {
    const { userId } = req.params;

    // Verify the user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify the user is the authenticated user or an admin
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // Generate a challenge for passkey registration
    const challenge = await user.generatePasskeyChallenge();

    // Return the challenge to the client
    return res.status(200).json({
      challenge,
      userId: user.id
    });
  } catch (error) {
    console.error('Error generating passkey challenge:', error);
    return res.status(500).json({ error: 'Failed to generate passkey challenge' });
  }
};

/**
 * Register a passkey for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const registerPasskey = async (req, res) => {
  try {
    const { userId } = req.params;
    const { credentialId, publicKey, metadata } = req.body;

    // Verify the user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify the user is the authenticated user or an admin
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // Verify the challenge was generated for this user
    if (!user.passkey_challenge) {
      return res.status(400).json({ error: 'No passkey challenge found for this user' });
    }

    // Store the passkey credentials
    await user.storePasskeyCredentials(credentialId, publicKey, metadata);

    return res.status(200).json({
      success: true,
      message: 'Passkey registered successfully'
    });
  } catch (error) {
    console.error('Error registering passkey:', error);
    return res.status(500).json({ error: 'Failed to register passkey' });
  }
};

/**
 * Authenticate a user with a passkey
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const authenticateWithPasskey = async (req, res) => {
  try {
    const { credentialId } = req.body;

    // Find the user by credential ID
    const user = await User.findByPasskeyCredentialId(credentialId);
    if (!user) {
      return res.status(404).json({ error: 'User not found for this passkey' });
    }

    // Verify the passkey is enabled
    if (!user.hasPasskeyEnabled()) {
      return res.status(400).json({ error: 'Passkey authentication is not enabled for this user' });
    }

    // Update the last used timestamp
    await user.verifyPasskey();

    // Get client IP address
    const ipAddress = getClientIp(req);

    // Generate tokens with IP tracking
    const { token, refreshToken, refreshTokenExpires } = await generateTokens(user.id, ipAddress);

    // Create a session record
    await createSession(user.id, token, req);

    // Calculate cookie expiration times based on token expiration
    const accessTokenMaxAge = 15 * 60 * 1000; // 15 minutes in milliseconds
    const refreshTokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    // Set auth cookies that will be available on all subdomains
    setAuthTokenCookie(res, token, {
      maxAge: accessTokenMaxAge
    });

    setRefreshTokenCookie(res, refreshToken, {
      maxAge: refreshTokenMaxAge
    });

    // Return user info and tokens
    return res.status(200).json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        userType: user.user_type,
        isBusinessOwner: user.is_business_owner,
        twoFactorEnabled: user.two_factor_enabled,
        is_global_admin: user.is_global_admin
      },
      token,
      refreshToken,
      message: 'Authenticated successfully with passkey'
    });
  } catch (error) {
    console.error('Error authenticating with passkey:', error);
    return res.status(500).json({ error: 'Failed to authenticate with passkey' });
  }
};

/**
 * Remove a passkey from a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const removePasskey = async (req, res) => {
  try {
    const { userId } = req.params;

    // Verify the user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify the user is the authenticated user or an admin
    if (req.user.id !== userId && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // Remove the passkey credentials
    user.passkey_enabled = false;
    user.passkey_credential_id = null;
    user.passkey_public_key = null;
    user.passkey_metadata = null;
    user.passkey_created_at = null;
    user.passkey_last_used_at = null;
    await user.save();

    return res.status(200).json({
      success: true,
      message: 'Passkey removed successfully'
    });
  } catch (error) {
    console.error('Error removing passkey:', error);
    return res.status(500).json({ error: 'Failed to remove passkey' });
  }
};
