import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import Delivery from '../models/Delivery.js';
import Pickup from '../models/Pickup.js';
import DriverSchedule from '../models/DriverSchedule.js';

/**
 * Get optimized routes for a driver's schedule
 */
export const getOptimizedRoute = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Find the schedule
    const schedule = await DriverSchedule.findByPk(scheduleId, {
      include: [
        { model: Driver, as: 'driver' }
      ]
    });

    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    // Get all stops (deliveries and pickups) for this schedule
    const deliveries = await Delivery.findAll({
      where: {
        driver_id: schedule.driver_id,
        scheduled_date: schedule.start_date,
        status: { [Op.notIn]: ['completed', 'cancelled'] }
      }
    });

    const pickups = await Pickup.findAll({
      where: {
        driver_id: schedule.driver_id,
        scheduled_date: schedule.start_date,
        status: { [Op.notIn]: ['completed', 'cancelled'] }
      }
    });

    // Combine all stops
    const allStops = [
      ...deliveries.map(delivery => ({
        id: delivery.id,
        type: 'delivery',
        locationName: delivery.destination_name,
        address: delivery.destination_address,
        latitude: delivery.destination_latitude,
        longitude: delivery.destination_longitude,
        scheduledTime: delivery.scheduled_time,
        priority: delivery.priority || 'medium',
        timeWindow: delivery.time_window || '2 hours'
      })),
      ...pickups.map(pickup => ({
        id: pickup.id,
        type: 'pickup',
        locationName: pickup.source_name,
        address: pickup.source_address,
        latitude: pickup.source_latitude,
        longitude: pickup.source_longitude,
        scheduledTime: pickup.scheduled_time,
        priority: pickup.priority || 'medium',
        timeWindow: pickup.time_window || '2 hours'
      }))
    ];

    // If no stops, return empty route
    if (allStops.length === 0) {
      return res.status(200).json({
        optimizedRoute: [],
        totalDistance: 0,
        totalTime: 0,
        fuelConsumption: 0
      });
    }

    // Get the driver's starting location (farm location or custom starting point)
    const farm = await Farm.findByPk(schedule.farm_id);
    const startingPoint = {
      id: 'start',
      type: 'start',
      locationName: 'Starting Point',
      address: farm.address,
      latitude: farm.latitude,
      longitude: farm.longitude,
      scheduledTime: schedule.start_time
    };

    // In a real implementation, this would use a routing algorithm (like Google Maps Directions API)
    // to calculate the optimal route. For now, we'll use a simple greedy algorithm.
    const optimizedRoute = optimizeRouteGreedy(startingPoint, allStops);

    // Calculate route metrics
    const { totalDistance, totalTime, fuelConsumption } = calculateRouteMetrics(optimizedRoute, schedule.driver);

    return res.status(200).json({
      optimizedRoute,
      totalDistance,
      totalTime,
      fuelConsumption
    });
  } catch (error) {
    console.error('Error getting optimized route:', error);
    return res.status(500).json({ error: 'Failed to get optimized route' });
  }
};

/**
 * Update a driver's schedule with optimized route
 */
export const updateScheduleWithOptimizedRoute = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { optimizedRoute } = req.body;

    // Validate required fields
    if (!optimizedRoute || !Array.isArray(optimizedRoute)) {
      return res.status(400).json({ error: 'Optimized route is required and must be an array' });
    }

    // Find the schedule
    const schedule = await DriverSchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    // Update the schedule with the optimized route
    schedule.route_details = JSON.stringify(optimizedRoute);
    await schedule.save();

    // Update the scheduled times for deliveries and pickups
    for (const stop of optimizedRoute) {
      if (stop.type === 'delivery') {
        const delivery = await Delivery.findByPk(stop.id);
        if (delivery) {
          delivery.scheduled_time = stop.scheduledTime;
          await delivery.save();
        }
      } else if (stop.type === 'pickup') {
        const pickup = await Pickup.findByPk(stop.id);
        if (pickup) {
          pickup.scheduled_time = stop.scheduledTime;
          await pickup.save();
        }
      }
    }

    return res.status(200).json({
      message: 'Schedule updated with optimized route',
      schedule: {
        id: schedule.id,
        driverId: schedule.driver_id,
        startDate: schedule.start_date,
        endDate: schedule.end_date,
        status: schedule.status,
        routeDetails: JSON.parse(schedule.route_details),
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating schedule with optimized route:', error);
    return res.status(500).json({ error: 'Failed to update schedule with optimized route' });
  }
};

/**
 * Get alternative routes for a driver's schedule
 */
export const getAlternativeRoutes = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { count = 3 } = req.query;

    // Find the schedule
    const schedule = await DriverSchedule.findByPk(scheduleId, {
      include: [
        { model: Driver, as: 'driver' }
      ]
    });

    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    // Get all stops (deliveries and pickups) for this schedule
    const deliveries = await Delivery.findAll({
      where: {
        driver_id: schedule.driver_id,
        scheduled_date: schedule.start_date,
        status: { [Op.notIn]: ['completed', 'cancelled'] }
      }
    });

    const pickups = await Pickup.findAll({
      where: {
        driver_id: schedule.driver_id,
        scheduled_date: schedule.start_date,
        status: { [Op.notIn]: ['completed', 'cancelled'] }
      }
    });

    // Combine all stops
    const allStops = [
      ...deliveries.map(delivery => ({
        id: delivery.id,
        type: 'delivery',
        locationName: delivery.destination_name,
        address: delivery.destination_address,
        latitude: delivery.destination_latitude,
        longitude: delivery.destination_longitude,
        scheduledTime: delivery.scheduled_time,
        priority: delivery.priority || 'medium',
        timeWindow: delivery.time_window || '2 hours'
      })),
      ...pickups.map(pickup => ({
        id: pickup.id,
        type: 'pickup',
        locationName: pickup.source_name,
        address: pickup.source_address,
        latitude: pickup.source_latitude,
        longitude: pickup.source_longitude,
        scheduledTime: pickup.scheduled_time,
        priority: pickup.priority || 'medium',
        timeWindow: pickup.time_window || '2 hours'
      }))
    ];

    // If no stops, return empty routes
    if (allStops.length === 0) {
      return res.status(200).json({
        alternativeRoutes: []
      });
    }

    // Get the driver's starting location (farm location or custom starting point)
    const farm = await Farm.findByPk(schedule.farm_id);
    const startingPoint = {
      id: 'start',
      type: 'start',
      locationName: 'Starting Point',
      address: farm.address,
      latitude: farm.latitude,
      longitude: farm.longitude,
      scheduledTime: schedule.start_time
    };

    // Generate alternative routes
    // In a real implementation, this would use a routing algorithm with different parameters
    // For now, we'll generate slightly different routes by shuffling stops
    const alternativeRoutes = [];
    for (let i = 0; i < count; i++) {
      // Shuffle stops slightly differently for each alternative
      const shuffledStops = [...allStops].sort(() => 0.5 - Math.random());
      const route = optimizeRouteGreedy(startingPoint, shuffledStops);
      const { totalDistance, totalTime, fuelConsumption } = calculateRouteMetrics(route, schedule.driver);
      
      alternativeRoutes.push({
        routeId: i + 1,
        route,
        totalDistance,
        totalTime,
        fuelConsumption
      });
    }

    // Sort by total distance
    alternativeRoutes.sort((a, b) => a.totalDistance - b.totalDistance);

    return res.status(200).json({
      alternativeRoutes
    });
  } catch (error) {
    console.error('Error getting alternative routes:', error);
    return res.status(500).json({ error: 'Failed to get alternative routes' });
  }
};

/**
 * Helper function to optimize route using a greedy algorithm
 * In a real implementation, this would use a more sophisticated algorithm
 */
const optimizeRouteGreedy = (startingPoint, stops) => {
  // Start with the starting point
  const optimizedRoute = [startingPoint];
  const remainingStops = [...stops];
  
  let currentStop = startingPoint;
  
  // While there are stops remaining, find the closest one
  while (remainingStops.length > 0) {
    let closestStopIndex = 0;
    let closestDistance = calculateDistance(
      currentStop.latitude, 
      currentStop.longitude, 
      remainingStops[0].latitude, 
      remainingStops[0].longitude
    );
    
    // Find the closest stop
    for (let i = 1; i < remainingStops.length; i++) {
      const distance = calculateDistance(
        currentStop.latitude, 
        currentStop.longitude, 
        remainingStops[i].latitude, 
        remainingStops[i].longitude
      );
      
      if (distance < closestDistance) {
        closestDistance = distance;
        closestStopIndex = i;
      }
    }
    
    // Add the closest stop to the route
    currentStop = remainingStops[closestStopIndex];
    optimizedRoute.push(currentStop);
    
    // Remove the stop from remaining stops
    remainingStops.splice(closestStopIndex, 1);
  }
  
  // Calculate estimated arrival times for each stop
  let currentTime = new Date(startingPoint.scheduledTime);
  
  for (let i = 1; i < optimizedRoute.length; i++) {
    const prevStop = optimizedRoute[i - 1];
    const currentStop = optimizedRoute[i];
    
    // Calculate distance between stops
    const distance = calculateDistance(
      prevStop.latitude, 
      prevStop.longitude, 
      currentStop.latitude, 
      currentStop.longitude
    );
    
    // Estimate travel time (assume 50 km/h average speed)
    const travelTimeMinutes = (distance / 50) * 60;
    
    // Add travel time to current time
    currentTime = new Date(currentTime.getTime() + travelTimeMinutes * 60000);
    
    // Add stop time (15 minutes for deliveries, 30 minutes for pickups)
    const stopTimeMinutes = currentStop.type === 'delivery' ? 15 : 30;
    
    // Update scheduled time for the stop
    optimizedRoute[i].scheduledTime = new Date(currentTime).toISOString();
    
    // Add stop time to current time
    currentTime = new Date(currentTime.getTime() + stopTimeMinutes * 60000);
  }
  
  return optimizedRoute;
};

/**
 * Helper function to calculate distance between two points using Haversine formula
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2); 
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
  const distance = R * c; // Distance in km
  return distance;
};

/**
 * Helper function to convert degrees to radians
 */
const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

/**
 * Helper function to calculate route metrics
 */
const calculateRouteMetrics = (route, driver) => {
  let totalDistance = 0;
  
  // Calculate total distance
  for (let i = 1; i < route.length; i++) {
    const prevStop = route[i - 1];
    const currentStop = route[i];
    
    totalDistance += calculateDistance(
      prevStop.latitude, 
      prevStop.longitude, 
      currentStop.latitude, 
      currentStop.longitude
    );
  }
  
  // Estimate total time (assume 50 km/h average speed)
  const drivingTimeHours = totalDistance / 50;
  
  // Add stop times (15 minutes for deliveries, 30 minutes for pickups)
  const deliveryCount = route.filter(stop => stop.type === 'delivery').length;
  const pickupCount = route.filter(stop => stop.type === 'pickup').length;
  const stopTimeHours = (deliveryCount * 15 + pickupCount * 30) / 60;
  
  const totalTime = drivingTimeHours + stopTimeHours;
  
  // Estimate fuel consumption (assume 10 liters per 100 km)
  const fuelConsumption = (totalDistance / 100) * 10;
  
  return {
    totalDistance, // in km
    totalTime, // in hours
    fuelConsumption // in liters
  };
};