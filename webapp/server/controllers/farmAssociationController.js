import FarmAssociation from '../models/FarmAssociation.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import Role from '../models/Role.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import { sendFarmAssociationRequestEmail, sendFarmAssociationPermissionRequestEmail } from '../utils/emailUtils.js';
import { getFrontendUrl } from '../utils/emailUtils.js';

// Create a new farm association
export const createFarmAssociation = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { initiator_farm_id, associated_farm_id, association_type } = req.body;

    // Validate required fields
    if (!initiator_farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Initiator farm ID is required' });
    }

    if (!associated_farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Associated farm ID is required' });
    }

    // Check if farms are the same
    if (initiator_farm_id === associated_farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'A farm cannot associate with itself' });
    }

    // Check if initiator farm exists
    const initiatorFarm = await Farm.findByPk(initiator_farm_id);
    if (!initiatorFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Initiator farm not found' });
    }

    // Check if associated farm exists
    const associatedFarm = await Farm.findByPk(associated_farm_id);
    if (!associatedFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Associated farm not found' });
    }

    // Check if association already exists
    const existingAssociation = await FarmAssociation.findOne({
      where: {
        [Op.or]: [
          {
            initiator_farm_id,
            associated_farm_id
          },
          {
            initiator_farm_id: associated_farm_id,
            associated_farm_id: initiator_farm_id
          }
        ]
      }
    });

    if (existingAssociation) {
      await transaction.rollback();
      return res.status(409).json({ error: 'Association already exists between these farms' });
    }

    // Create the association
    const association = await FarmAssociation.create({
      initiator_farm_id,
      associated_farm_id,
      association_type: association_type || 'general',
      status: 'pending'
    }, { transaction });

    await transaction.commit();

    // Send email notification to farm administrators of the associated farm
    try {
      // Find farm administrators of the associated farm
      const adminRoles = await Role.findAll({
        where: {
          name: {
            [Op.in]: ['farm_owner', 'farm_admin']
          }
        },
        attributes: ['id']
      });

      const adminRoleIds = adminRoles.map(role => role.id);

      const farmAdmins = await UserFarm.findAll({
        where: {
          farm_id: associated_farm_id,
          role_id: {
            [Op.in]: adminRoleIds
          },
          is_approved: true
        },
        include: [
          {
            model: User,
            attributes: ['id', 'first_name', 'last_name', 'email']
          }
        ]
      });

      // Get the frontend URL
      const frontendUrl = await getFrontendUrl(null, associated_farm_id);

      // Send email notification to each farm administrator
      for (const admin of farmAdmins) {
        if (admin.User && admin.User.email) {
          await sendFarmAssociationRequestEmail(
            initiatorFarm,
            associatedFarm,
            association_type || 'general',
            admin.User,
            frontendUrl
          );
        }
      }
    } catch (emailError) {
      // Log the error but don't fail the request
      console.error('Error sending farm association email notifications:', emailError);
    }

    return res.status(201).json(association);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating farm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all associations for a farm (both initiated and received)
export const getFarmAssociations = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all associations for the farm
    const associations = await FarmAssociation.findAll({
      where: {
        [Op.or]: [
          { initiator_farm_id: farmId },
          { associated_farm_id: farmId }
        ]
      },
      include: [
        {
          model: Farm,
          as: 'initiatorFarm',
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          as: 'associatedFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json(associations);
  } catch (error) {
    console.error('Error getting farm associations:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all pending associations for a farm
export const getPendingAssociations = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all pending associations for the farm
    const associations = await FarmAssociation.findAll({
      where: {
        associated_farm_id: farmId,
        status: 'pending'
      },
      include: [
        {
          model: Farm,
          as: 'initiatorFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json(associations);
  } catch (error) {
    console.error('Error getting pending farm associations:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a farm association status
export const updateFarmAssociation = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { associationId } = req.params;
    const { status } = req.body;

    // Validate status
    if (!['pending', 'active', 'rejected', 'revoked'].includes(status)) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid status. Must be pending, active, rejected, or revoked' });
    }

    // Find the association
    const association = await FarmAssociation.findByPk(associationId, { transaction });
    if (!association) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Association not found' });
    }

    // Update the association
    association.status = status;
    await association.save({ transaction });

    // If this association is being approved (status = active), check if there's an old association
    // that's pending replacement and should be removed
    if (status === 'active') {
      // Find any associations between the same farms that are pending replacement
      const oldAssociations = await FarmAssociation.findAll({
        where: {
          [Op.or]: [
            {
              initiator_farm_id: association.initiator_farm_id,
              associated_farm_id: association.associated_farm_id,
              status: 'pending_replacement'
            },
            {
              initiator_farm_id: association.associated_farm_id,
              associated_farm_id: association.initiator_farm_id,
              status: 'pending_replacement'
            }
          ]
        },
        transaction
      });

      // Remove the old associations
      for (const oldAssociation of oldAssociations) {
        await oldAssociation.destroy({ transaction });
      }
    }

    await transaction.commit();
    return res.status(200).json(association);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating farm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Remove a farm association
export const removeFarmAssociation = async (req, res) => {
  try {
    const { associationId } = req.params;

    // Find the association
    const association = await FarmAssociation.findByPk(associationId);
    if (!association) {
      return res.status(404).json({ error: 'Association not found' });
    }

    // Delete the association
    await association.destroy();

    return res.status(204).send();
  } catch (error) {
    console.error('Error removing farm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Check if two farms are associated
export const checkFarmAssociation = async (req, res) => {
  try {
    const { farmId1, farmId2 } = req.params;

    // Check if farms exist
    const farm1 = await Farm.findByPk(farmId1);
    if (!farm1) {
      return res.status(404).json({ error: 'First farm not found' });
    }

    const farm2 = await Farm.findByPk(farmId2);
    if (!farm2) {
      return res.status(404).json({ error: 'Second farm not found' });
    }

    // Check if farms are associated
    const isAssociated = await FarmAssociation.areFarmsAssociated(farmId1, farmId2);

    return res.status(200).json({ isAssociated });
  } catch (error) {
    console.error('Error checking farm association:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a farm association type (requires approval)
export const updateAssociationType = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { associationId } = req.params;
    const { association_type, initiator_farm_id } = req.body;

    // Validate required fields
    if (!association_type) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Association type is required' });
    }

    if (!initiator_farm_id) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Initiator farm ID is required' });
    }

    // Find the association
    const association = await FarmAssociation.findByPk(associationId, {
      include: [
        {
          model: Farm,
          as: 'initiatorFarm',
          attributes: ['id', 'name']
        },
        {
          model: Farm,
          as: 'associatedFarm',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!association) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Association not found' });
    }

    // Check if the association is active
    if (association.status !== 'active') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Only active associations can be updated' });
    }

    // Check if the association type is the same
    if (association.association_type === association_type) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Association type is already set to this value' });
    }

    // Determine which farm is requesting the change and which farm needs to approve
    const requestingFarm = await Farm.findByPk(initiator_farm_id);
    if (!requestingFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Requesting farm not found' });
    }

    const otherFarmId = initiator_farm_id === association.initiator_farm_id 
      ? association.associated_farm_id 
      : association.initiator_farm_id;

    const otherFarm = await Farm.findByPk(otherFarmId);
    if (!otherFarm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Other farm not found' });
    }

    // Create a new association with pending status
    const newAssociation = await FarmAssociation.create({
      initiator_farm_id: initiator_farm_id,
      associated_farm_id: otherFarmId,
      association_type: association_type,
      status: 'pending'
    }, { transaction });

    // Mark the old association for deletion once the new one is approved
    association.status = 'pending_replacement';
    await association.save({ transaction });

    await transaction.commit();

    // Send email notification to farm administrators of the other farm
    try {
      // Find farm administrators of the other farm
      const adminRoles = await Role.findAll({
        where: {
          name: {
            [Op.in]: ['farm_owner', 'farm_admin']
          }
        },
        attributes: ['id']
      });

      const adminRoleIds = adminRoles.map(role => role.id);

      const farmAdmins = await UserFarm.findAll({
        where: {
          farm_id: otherFarmId,
          role_id: {
            [Op.in]: adminRoleIds
          },
          is_approved: true
        },
        include: [
          {
            model: User,
            attributes: ['id', 'first_name', 'last_name', 'email']
          }
        ]
      });

      // Get the frontend URL
      const frontendUrl = await getFrontendUrl(null, otherFarmId);

      // Send email notification to each farm administrator
      for (const admin of farmAdmins) {
        if (admin.User && admin.User.email) {
          await sendFarmAssociationRequestEmail(
            requestingFarm,
            otherFarm,
            association_type,
            admin.User,
            frontendUrl
          );
        }
      }
    } catch (emailError) {
      // Log the error but don't fail the request
      console.error('Error sending farm association type change email notifications:', emailError);
    }

    return res.status(200).json(newAssociation);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating farm association type:', error);
    return res.status(500).json({ error: error.message });
  }
};
