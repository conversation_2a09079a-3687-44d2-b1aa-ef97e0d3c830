import Pickup from '../models/Pickup.js';
import Farm from '../models/Farm.js';
import Driver from '../models/Driver.js';
import Supplier from '../models/Supplier.js';
import Customer from '../models/Customer.js';
import Order from '../models/Order.js';
import { sequelize } from '../config/database.js';

// Create a new pickup
export const createPickup = async (req, res) => {
  try {
    const { 
      farmId,
      driverId,
      supplierId,
      customerId,
      orderId,
      pickupType = 'product',
      status = 'scheduled',
      scheduledDate,
      estimatedCompletion,
      actualPickupDate,
      pickupAddress,
      pickupCity,
      pickupState,
      pickupZip,
      pickupCountry = 'USA',
      pickupInstructions,
      confirmationRequired = false,
      notes
    } = req.body;

    // Validate required fields
    if (!farmId || !scheduledDate || !pickupAddress || !pickupCity || !pickupState || !pickupZip) {
      return res.status(400).json({ 
        error: 'Farm ID, scheduled date, and pickup address details are required' 
      });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If driverId is provided, check if driver exists
    if (driverId) {
      const driver = await Driver.findByPk(driverId);
      if (!driver) {
        return res.status(404).json({ error: 'Driver not found' });
      }
    }

    // If supplierId is provided, check if supplier exists
    if (supplierId) {
      const supplier = await Supplier.findByPk(supplierId);
      if (!supplier) {
        return res.status(404).json({ error: 'Supplier not found' });
      }
    }

    // If customerId is provided, check if customer exists
    if (customerId) {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        return res.status(404).json({ error: 'Customer not found' });
      }
    }

    // If orderId is provided, check if order exists
    if (orderId) {
      const order = await Order.findByPk(orderId);
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }
    }

    // Create new pickup
    const pickup = await Pickup.create({
      farm_id: farmId,
      driver_id: driverId || null,
      supplier_id: supplierId || null,
      customer_id: customerId || null,
      order_id: orderId || null,
      pickup_type: pickupType,
      status,
      scheduled_date: scheduledDate,
      estimated_completion: estimatedCompletion || null,
      actual_pickup_date: actualPickupDate || null,
      pickup_address: pickupAddress,
      pickup_city: pickupCity,
      pickup_state: pickupState,
      pickup_zip: pickupZip,
      pickup_country: pickupCountry,
      pickup_instructions: pickupInstructions || null,
      confirmation_required: confirmationRequired,
      notes: notes || null
    });

    return res.status(201).json({
      pickup: {
        id: pickup.id,
        farmId: pickup.farm_id,
        driverId: pickup.driver_id,
        supplierId: pickup.supplier_id,
        customerId: pickup.customer_id,
        orderId: pickup.order_id,
        pickupType: pickup.pickup_type,
        status: pickup.status,
        scheduledDate: pickup.scheduled_date,
        estimatedCompletion: pickup.estimated_completion,
        actualPickupDate: pickup.actual_pickup_date,
        pickupAddress: pickup.pickup_address,
        pickupCity: pickup.pickup_city,
        pickupState: pickup.pickup_state,
        pickupZip: pickup.pickup_zip,
        pickupCountry: pickup.pickup_country,
        pickupInstructions: pickup.pickup_instructions,
        confirmationRequired: pickup.confirmation_required,
        confirmationSignature: pickup.confirmation_signature,
        proofOfPickupImage: pickup.proof_of_pickup_image,
        notes: pickup.notes,
        createdAt: pickup.created_at,
        updatedAt: pickup.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating pickup:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all pickups with optional filtering
export const getPickups = async (req, res) => {
  try {
    const { 
      farmId, 
      driverId,
      supplierId,
      customerId,
      orderId,
      status,
      pickupType,
      startDate,
      endDate,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (farmId) {
      whereClause.farm_id = farmId;
    }

    if (driverId) {
      whereClause.driver_id = driverId;
    }

    if (supplierId) {
      whereClause.supplier_id = supplierId;
    }

    if (customerId) {
      whereClause.customer_id = customerId;
    }

    if (orderId) {
      whereClause.order_id = orderId;
    }

    if (status) {
      whereClause.status = status;
    }

    if (pickupType) {
      whereClause.pickup_type = pickupType;
    }

    // Date range filtering
    if (startDate || endDate) {
      whereClause.scheduled_date = {};

      if (startDate) {
        whereClause.scheduled_date[sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        whereClause.scheduled_date[sequelize.Op.lte] = new Date(endDate);
      }
    }

    if (search) {
      whereClause[sequelize.Op.or] = [
        { pickup_address: { [sequelize.Op.iLike]: `%${search}%` } },
        { pickup_city: { [sequelize.Op.iLike]: `%${search}%` } },
        { pickup_state: { [sequelize.Op.iLike]: `%${search}%` } },
        { pickup_zip: { [sequelize.Op.iLike]: `%${search}%` } },
        { pickup_instructions: { [sequelize.Op.iLike]: `%${search}%` } },
        { notes: { [sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // Get pickups with pagination
    const pickups = await Pickup.findAll({
      where: whereClause,
      include: [
        { model: Farm, as: 'pickupFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'pickupDriver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Supplier, as: 'supplier', attributes: ['id', 'name', 'email'] },
        { model: Customer, as: 'pickupCustomer', attributes: ['id', 'name', 'email'] },
        { model: Order, as: 'order', attributes: ['id', 'order_number', 'total'] }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['scheduled_date', 'ASC']]
    });

    // Get total count for pagination
    const totalCount = await Pickup.count({ where: whereClause });

    return res.status(200).json({
      pickups: pickups.map(pickup => ({
        id: pickup.id,
        farmId: pickup.farm_id,
        driverId: pickup.driver_id,
        supplierId: pickup.supplier_id,
        customerId: pickup.customer_id,
        orderId: pickup.order_id,
        pickupType: pickup.pickup_type,
        status: pickup.status,
        scheduledDate: pickup.scheduled_date,
        estimatedCompletion: pickup.estimated_completion,
        actualPickupDate: pickup.actual_pickup_date,
        pickupAddress: pickup.pickup_address,
        pickupCity: pickup.pickup_city,
        pickupState: pickup.pickup_state,
        pickupZip: pickup.pickup_zip,
        pickupCountry: pickup.pickup_country,
        pickupInstructions: pickup.pickup_instructions,
        confirmationRequired: pickup.confirmation_required,
        confirmationSignature: pickup.confirmation_signature,
        proofOfPickupImage: pickup.proof_of_pickup_image,
        notes: pickup.notes,
        farm: pickup.pickupFarm,
        driver: pickup.pickupDriver,
        supplier: pickup.supplier,
        customer: pickup.pickupCustomer,
        order: pickup.order,
        createdAt: pickup.created_at,
        updatedAt: pickup.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting pickups:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get pickup by ID
export const getPickupById = async (req, res) => {
  try {
    const { pickupId } = req.params;

    const pickup = await Pickup.findByPk(pickupId, {
      include: [
        { model: Farm, as: 'pickupFarm', attributes: ['id', 'name'] },
        { model: Driver, as: 'pickupDriver', attributes: ['id', 'first_name', 'last_name'] },
        { model: Supplier, as: 'supplier', attributes: ['id', 'name', 'email'] },
        { model: Customer, as: 'pickupCustomer', attributes: ['id', 'name', 'email'] },
        { model: Order, as: 'order', attributes: ['id', 'order_number', 'total'] }
      ]
    });

    if (!pickup) {
      return res.status(404).json({ error: 'Pickup not found' });
    }

    return res.status(200).json({
      pickup: {
        id: pickup.id,
        farmId: pickup.farm_id,
        driverId: pickup.driver_id,
        supplierId: pickup.supplier_id,
        customerId: pickup.customer_id,
        orderId: pickup.order_id,
        pickupType: pickup.pickup_type,
        status: pickup.status,
        scheduledDate: pickup.scheduled_date,
        estimatedCompletion: pickup.estimated_completion,
        actualPickupDate: pickup.actual_pickup_date,
        pickupAddress: pickup.pickup_address,
        pickupCity: pickup.pickup_city,
        pickupState: pickup.pickup_state,
        pickupZip: pickup.pickup_zip,
        pickupCountry: pickup.pickup_country,
        pickupInstructions: pickup.pickup_instructions,
        confirmationRequired: pickup.confirmation_required,
        confirmationSignature: pickup.confirmation_signature,
        proofOfPickupImage: pickup.proof_of_pickup_image,
        notes: pickup.notes,
        farm: pickup.pickupFarm,
        driver: pickup.pickupDriver,
        supplier: pickup.supplier,
        customer: pickup.pickupCustomer,
        order: pickup.order,
        createdAt: pickup.created_at,
        updatedAt: pickup.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting pickup by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update pickup
export const updatePickup = async (req, res) => {
  try {
    const { pickupId } = req.params;
    const { 
      driverId,
      supplierId,
      customerId,
      orderId,
      pickupType,
      status,
      scheduledDate,
      estimatedCompletion,
      actualPickupDate,
      pickupAddress,
      pickupCity,
      pickupState,
      pickupZip,
      pickupCountry,
      pickupInstructions,
      confirmationRequired,
      confirmationSignature,
      proofOfPickupImage,
      notes
    } = req.body;

    const pickup = await Pickup.findByPk(pickupId);
    if (!pickup) {
      return res.status(404).json({ error: 'Pickup not found' });
    }

    // If driverId is provided, check if driver exists
    if (driverId !== undefined) {
      if (driverId) {
        const driver = await Driver.findByPk(driverId);
        if (!driver) {
          return res.status(404).json({ error: 'Driver not found' });
        }
        pickup.driver_id = driverId;
      } else {
        pickup.driver_id = null;
      }
    }

    // If supplierId is provided, check if supplier exists
    if (supplierId !== undefined) {
      if (supplierId) {
        const supplier = await Supplier.findByPk(supplierId);
        if (!supplier) {
          return res.status(404).json({ error: 'Supplier not found' });
        }
        pickup.supplier_id = supplierId;
      } else {
        pickup.supplier_id = null;
      }
    }

    // If customerId is provided, check if customer exists
    if (customerId !== undefined) {
      if (customerId) {
        const customer = await Customer.findByPk(customerId);
        if (!customer) {
          return res.status(404).json({ error: 'Customer not found' });
        }
        pickup.customer_id = customerId;
      } else {
        pickup.customer_id = null;
      }
    }

    // If orderId is provided, check if order exists
    if (orderId !== undefined) {
      if (orderId) {
        const order = await Order.findByPk(orderId);
        if (!order) {
          return res.status(404).json({ error: 'Order not found' });
        }
        pickup.order_id = orderId;
      } else {
        pickup.order_id = null;
      }
    }

    // Update pickup fields if provided
    if (pickupType !== undefined) pickup.pickup_type = pickupType;
    if (status !== undefined) pickup.status = status;
    if (scheduledDate !== undefined) pickup.scheduled_date = scheduledDate;
    if (estimatedCompletion !== undefined) pickup.estimated_completion = estimatedCompletion;
    if (actualPickupDate !== undefined) pickup.actual_pickup_date = actualPickupDate;
    if (pickupAddress !== undefined) pickup.pickup_address = pickupAddress;
    if (pickupCity !== undefined) pickup.pickup_city = pickupCity;
    if (pickupState !== undefined) pickup.pickup_state = pickupState;
    if (pickupZip !== undefined) pickup.pickup_zip = pickupZip;
    if (pickupCountry !== undefined) pickup.pickup_country = pickupCountry;
    if (pickupInstructions !== undefined) pickup.pickup_instructions = pickupInstructions;
    if (confirmationRequired !== undefined) pickup.confirmation_required = confirmationRequired;
    if (confirmationSignature !== undefined) pickup.confirmation_signature = confirmationSignature;
    if (proofOfPickupImage !== undefined) pickup.proof_of_pickup_image = proofOfPickupImage;
    if (notes !== undefined) pickup.notes = notes;

    await pickup.save();

    return res.status(200).json({
      pickup: {
        id: pickup.id,
        farmId: pickup.farm_id,
        driverId: pickup.driver_id,
        supplierId: pickup.supplier_id,
        customerId: pickup.customer_id,
        orderId: pickup.order_id,
        pickupType: pickup.pickup_type,
        status: pickup.status,
        scheduledDate: pickup.scheduled_date,
        estimatedCompletion: pickup.estimated_completion,
        actualPickupDate: pickup.actual_pickup_date,
        pickupAddress: pickup.pickup_address,
        pickupCity: pickup.pickup_city,
        pickupState: pickup.pickup_state,
        pickupZip: pickup.pickup_zip,
        pickupCountry: pickup.pickup_country,
        pickupInstructions: pickup.pickup_instructions,
        confirmationRequired: pickup.confirmation_required,
        confirmationSignature: pickup.confirmation_signature,
        proofOfPickupImage: pickup.proof_of_pickup_image,
        notes: pickup.notes,
        createdAt: pickup.created_at,
        updatedAt: pickup.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating pickup:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete pickup
export const deletePickup = async (req, res) => {
  try {
    const { pickupId } = req.params;

    const pickup = await Pickup.findByPk(pickupId);
    if (!pickup) {
      return res.status(404).json({ error: 'Pickup not found' });
    }

    await pickup.destroy();

    return res.status(200).json({
      message: 'Pickup deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting pickup:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update pickup status
export const updatePickupStatus = async (req, res) => {
  try {
    const { pickupId } = req.params;
    const { status, actualPickupDate, confirmationSignature, proofOfPickupImage, notes } = req.body;

    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }

    const pickup = await Pickup.findByPk(pickupId);
    if (!pickup) {
      return res.status(404).json({ error: 'Pickup not found' });
    }

    // Update status and related fields
    pickup.status = status;

    if (status === 'completed' && !pickup.actual_pickup_date) {
      pickup.actual_pickup_date = actualPickupDate || new Date();
    }

    if (confirmationSignature) {
      pickup.confirmation_signature = confirmationSignature;
    }

    if (proofOfPickupImage) {
      pickup.proof_of_pickup_image = proofOfPickupImage;
    }

    if (notes) {
      pickup.notes = pickup.notes 
        ? `${pickup.notes}\n${new Date().toISOString()}: ${notes}`
        : `${new Date().toISOString()}: ${notes}`;
    }

    await pickup.save();

    return res.status(200).json({
      pickup: {
        id: pickup.id,
        status: pickup.status,
        actualPickupDate: pickup.actual_pickup_date,
        confirmationSignature: pickup.confirmation_signature,
        proofOfPickupImage: pickup.proof_of_pickup_image,
        notes: pickup.notes,
        updatedAt: pickup.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating pickup status:', error);
    return res.status(500).json({ error: error.message });
  }
};
