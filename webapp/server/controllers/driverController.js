import Driver from '../models/Driver.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import { sequelize } from '../config/database.js';

// Create a new driver
export const createDriver = async (req, res) => {
  try {
    const { 
      farmId,
      userId,
      firstName, 
      lastName, 
      email, 
      phoneNumber, 
      licenseNumber,
      licenseExpiry,
      vehicleType,
      vehiclePlate,
      status = 'active',
      notes
    } = req.body;

    // Validate required fields
    if (!farmId || !firstName || !lastName) {
      return res.status(400).json({ error: 'Farm ID, first name, and last name are required' });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // If userId is provided, check if user exists
    if (userId) {
      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
    }

    // Create new driver
    const driver = await Driver.create({
      farm_id: farmId,
      user_id: userId || null,
      first_name: firstName,
      last_name: lastName,
      email: email || null,
      phone_number: phoneNumber || null,
      license_number: licenseNumber || null,
      license_expiry: licenseExpiry || null,
      vehicle_type: vehicleType || null,
      vehicle_plate: vehiclePlate || null,
      status,
      notes: notes || null
    });

    return res.status(201).json({
      driver: {
        id: driver.id,
        farmId: driver.farm_id,
        userId: driver.user_id,
        firstName: driver.first_name,
        lastName: driver.last_name,
        email: driver.email,
        phoneNumber: driver.phone_number,
        licenseNumber: driver.license_number,
        licenseExpiry: driver.license_expiry,
        vehicleType: driver.vehicle_type,
        vehiclePlate: driver.vehicle_plate,
        status: driver.status,
        notes: driver.notes,
        createdAt: driver.created_at,
        updatedAt: driver.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating driver:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all drivers with optional filtering
export const getDrivers = async (req, res) => {
  try {
    const { 
      farmId, 
      status, 
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (farmId) {
      whereClause.farm_id = farmId;
    }

    if (status) {
      whereClause.status = status;
    }

    if (search) {
      whereClause[sequelize.Op.or] = [
        { first_name: { [sequelize.Op.iLike]: `%${search}%` } },
        { last_name: { [sequelize.Op.iLike]: `%${search}%` } },
        { email: { [sequelize.Op.iLike]: `%${search}%` } },
        { phone_number: { [sequelize.Op.iLike]: `%${search}%` } },
        { license_number: { [sequelize.Op.iLike]: `%${search}%` } },
        { vehicle_plate: { [sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // Get drivers with pagination
    const drivers = await Driver.findAll({
      where: whereClause,
      include: [
        { model: Farm, as: 'driverFarm', attributes: ['id', 'name'] },
        { model: User, as: 'driverUser', attributes: ['id', 'email', 'first_name', 'last_name'] }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    // Get total count for pagination
    const totalCount = await Driver.count({ where: whereClause });

    return res.status(200).json({
      drivers: drivers.map(driver => ({
        id: driver.id,
        farmId: driver.farm_id,
        userId: driver.user_id,
        firstName: driver.first_name,
        lastName: driver.last_name,
        email: driver.email,
        phoneNumber: driver.phone_number,
        licenseNumber: driver.license_number,
        licenseExpiry: driver.license_expiry,
        vehicleType: driver.vehicle_type,
        vehiclePlate: driver.vehicle_plate,
        status: driver.status,
        notes: driver.notes,
        farm: driver.driverFarm,
        user: driver.driverUser,
        createdAt: driver.created_at,
        updatedAt: driver.updated_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting drivers:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get driver by ID
export const getDriverById = async (req, res) => {
  try {
    const { driverId } = req.params;

    const driver = await Driver.findByPk(driverId, {
      include: [
        { model: Farm, as: 'driverFarm', attributes: ['id', 'name'] },
        { model: User, as: 'driverUser', attributes: ['id', 'email', 'first_name', 'last_name'] }
      ]
    });

    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    return res.status(200).json({
      driver: {
        id: driver.id,
        farmId: driver.farm_id,
        userId: driver.user_id,
        firstName: driver.first_name,
        lastName: driver.last_name,
        email: driver.email,
        phoneNumber: driver.phone_number,
        licenseNumber: driver.license_number,
        licenseExpiry: driver.license_expiry,
        vehicleType: driver.vehicle_type,
        vehiclePlate: driver.vehicle_plate,
        status: driver.status,
        notes: driver.notes,
        farm: driver.driverFarm,
        user: driver.driverUser,
        createdAt: driver.created_at,
        updatedAt: driver.updated_at
      }
    });
  } catch (error) {
    console.error('Error getting driver by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update driver
export const updateDriver = async (req, res) => {
  try {
    const { driverId } = req.params;
    const { 
      userId,
      firstName, 
      lastName, 
      email, 
      phoneNumber, 
      licenseNumber,
      licenseExpiry,
      vehicleType,
      vehiclePlate,
      status,
      notes
    } = req.body;

    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // If userId is provided, check if user exists
    if (userId !== undefined) {
      if (userId) {
        const user = await User.findByPk(userId);
        if (!user) {
          return res.status(404).json({ error: 'User not found' });
        }
        driver.user_id = userId;
      } else {
        driver.user_id = null;
      }
    }

    // Update driver fields if provided
    if (firstName !== undefined) driver.first_name = firstName;
    if (lastName !== undefined) driver.last_name = lastName;
    if (email !== undefined) driver.email = email;
    if (phoneNumber !== undefined) driver.phone_number = phoneNumber;
    if (licenseNumber !== undefined) driver.license_number = licenseNumber;
    if (licenseExpiry !== undefined) driver.license_expiry = licenseExpiry;
    if (vehicleType !== undefined) driver.vehicle_type = vehicleType;
    if (vehiclePlate !== undefined) driver.vehicle_plate = vehiclePlate;
    if (status !== undefined) driver.status = status;
    if (notes !== undefined) driver.notes = notes;

    await driver.save();

    return res.status(200).json({
      driver: {
        id: driver.id,
        farmId: driver.farm_id,
        userId: driver.user_id,
        firstName: driver.first_name,
        lastName: driver.last_name,
        email: driver.email,
        phoneNumber: driver.phone_number,
        licenseNumber: driver.license_number,
        licenseExpiry: driver.license_expiry,
        vehicleType: driver.vehicle_type,
        vehiclePlate: driver.vehicle_plate,
        status: driver.status,
        notes: driver.notes,
        createdAt: driver.created_at,
        updatedAt: driver.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating driver:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete driver
export const deleteDriver = async (req, res) => {
  try {
    const { driverId } = req.params;

    const driver = await Driver.findByPk(driverId);
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    await driver.destroy();

    return res.status(200).json({
      message: 'Driver deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting driver:', error);
    return res.status(500).json({ error: error.message });
  }
};
