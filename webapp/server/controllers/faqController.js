import FAQ from '../models/FAQ.js';
import { sequelize } from '../config/database.js';

// Create a new FAQ entry
export const createFAQ = async (req, res) => {
  try {
    const { 
      question, 
      answer, 
      category, 
      order = 0,
      isActive = true
    } = req.body;

    // Validate required fields
    if (!question || !answer) {
      return res.status(400).json({ error: 'Question and answer are required' });
    }

    // Create the FAQ entry
    const faq = await FAQ.create({
      question,
      answer,
      category,
      order,
      is_active: isActive
    });

    return res.status(201).json({
      faq: {
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        category: faq.category,
        order: faq.order,
        isActive: faq.is_active,
        createdAt: faq.created_at,
        updatedAt: faq.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating FAQ entry:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all FAQ entries with optional filtering
export const getFAQs = async (req, res) => {
  try {
    const { 
      category, 
      isActive,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (category) {
      whereClause.category = category;
    }

    if (isActive !== undefined) {
      whereClause.is_active = isActive === 'true';
    }

    if (search) {
      whereClause[sequelize.Op.or] = [
        { question: { [sequelize.Op.iLike]: `%${search}%` } },
        { answer: { [sequelize.Op.iLike]: `%${search}%` } }
      ];
    }

    // Get FAQs with pagination
    const faqs = await FAQ.findAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [
        ['order', 'ASC'],
        ['created_at', 'DESC']
      ]
    });

    // Get total count for pagination
    const totalCount = await FAQ.count({ where: whereClause });

    // Transform the data for the response
    const transformedFaqs = faqs.map(faq => ({
      id: faq.id,
      question: faq.question,
      answer: faq.answer,
      category: faq.category,
      order: faq.order,
      isActive: faq.is_active,
      createdAt: faq.created_at,
      updatedAt: faq.updated_at
    }));

    return res.status(200).json({
      faqs: transformedFaqs,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single FAQ entry by ID
export const getFAQ = async (req, res) => {
  try {
    const { faqId } = req.params;

    // Find the FAQ by ID
    const faq = await FAQ.findByPk(faqId);

    if (!faq) {
      return res.status(404).json({ error: 'FAQ not found' });
    }

    return res.status(200).json({
      faq: {
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        category: faq.category,
        order: faq.order,
        isActive: faq.is_active,
        createdAt: faq.created_at,
        updatedAt: faq.updated_at
      }
    });
  } catch (error) {
    console.error('Error fetching FAQ:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a FAQ entry
export const updateFAQ = async (req, res) => {
  try {
    const { faqId } = req.params;
    const { 
      question, 
      answer, 
      category, 
      order,
      isActive
    } = req.body;

    // Find the FAQ by ID
    const faq = await FAQ.findByPk(faqId);

    if (!faq) {
      return res.status(404).json({ error: 'FAQ not found' });
    }

    // Update the FAQ
    await faq.update({
      question: question !== undefined ? question : faq.question,
      answer: answer !== undefined ? answer : faq.answer,
      category: category !== undefined ? category : faq.category,
      order: order !== undefined ? order : faq.order,
      is_active: isActive !== undefined ? isActive : faq.is_active
    });

    return res.status(200).json({
      faq: {
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        category: faq.category,
        order: faq.order,
        isActive: faq.is_active,
        createdAt: faq.created_at,
        updatedAt: faq.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating FAQ:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a FAQ entry
export const deleteFAQ = async (req, res) => {
  try {
    const { faqId } = req.params;

    // Find the FAQ by ID
    const faq = await FAQ.findByPk(faqId);

    if (!faq) {
      return res.status(404).json({ error: 'FAQ not found' });
    }

    // Delete the FAQ
    await faq.destroy();

    return res.status(200).json({ message: 'FAQ deleted successfully' });
  } catch (error) {
    console.error('Error deleting FAQ:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all categories
export const getFAQCategories = async (req, res) => {
  try {
    // Check if there are any FAQs in the database
    const faqCount = await FAQ.count();

    if (faqCount === 0) {
      // If no FAQs exist, return an empty array
      return res.status(200).json({
        categories: []
      });
    }

    // Get distinct categories
    const categories = await FAQ.findAll({
      attributes: [[sequelize.fn('DISTINCT', sequelize.col('category')), 'category']],
      where: {
        category: {
          [sequelize.Op.not]: null
        }
      }
    });

    // Transform the data for the response
    const transformedCategories = categories.map(item => item.category);

    return res.status(200).json({
      categories: transformedCategories
    });
  } catch (error) {
    console.error('Error fetching FAQ categories:', error);
    return res.status(500).json({ error: error.message });
  }
};
