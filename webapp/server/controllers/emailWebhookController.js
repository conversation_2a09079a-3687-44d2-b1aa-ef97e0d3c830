import SupportTicket from '../models/SupportTicket.js';
import SupportTicketComment from '../models/SupportTicketComment.js';
import User from '../models/User.js';
import { sequelize } from '../config/database.js';

/**
 * Process an email reply to a support ticket
 * This endpoint will be called by the email service webhook
 */
export const processTicketEmailReply = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      from, 
      subject, 
      text, 
      html, 
      headers,
      timestamp 
    } = req.body;

    console.log('Received email webhook:', { from, subject, headers });

    // Extract email address from the "from" field
    const fromEmail = extractEmailAddress(from);
    if (!fromEmail) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid sender email format' });
    }

    // Extract ticket ID from subject or headers
    let ticketId = extractTicketIdFromSubject(subject);
    
    // If not found in subject, try to get it from headers
    if (!ticketId && headers) {
      ticketId = headers['X-Ticket-ID'] || headers['x-ticket-id'] || 
                 headers['In-Reply-To'] || headers['in-reply-to'] ||
                 headers['References'] || headers['references'];
      
      // If it's a reference or in-reply-to, we need to extract the ticket ID
      if (ticketId && (ticketId.includes('ticket-'))) {
        const match = ticketId.match(/ticket-([a-f0-9-]+)/i);
        if (match && match[1]) {
          ticketId = match[1];
        }
      }
    }

    if (!ticketId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Could not determine ticket ID from email' });
    }

    // Find the ticket
    const ticket = await SupportTicket.findByPk(ticketId);
    if (!ticket) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Find the user by email
    const user = await User.findOne({
      where: { email: fromEmail }
    });

    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found for the given email address' });
    }

    // Extract content from the email
    let content = '';
    
    // Prefer plain text over HTML for simplicity
    if (text) {
      content = cleanEmailContent(text);
    } else if (html) {
      // In a real implementation, you would use an HTML parser to extract text
      content = 'HTML content received (not parsed)';
    } else {
      content = 'Empty email content';
    }

    // Create the comment
    const comment = await SupportTicketComment.create({
      content,
      ticket_id: ticket.id,
      user_id: user.id,
      is_internal: false,
      is_from_email: true,
      email_source: fromEmail,
      email_message_id: headers && (headers['Message-ID'] || headers['message-id'])
    }, { transaction });

    // Update the ticket's updated_at timestamp
    await ticket.update({ 
      updated_at: new Date(),
      // If the ticket was closed, reopen it
      status: ticket.status === 'closed' ? 'open' : ticket.status
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Email reply processed successfully',
      comment: {
        id: comment.id,
        content: comment.content,
        ticketId: comment.ticket_id,
        userId: comment.user_id,
        isFromEmail: comment.is_from_email,
        createdAt: comment.created_at
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing email reply:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Extract email address from a string like "John Doe <<EMAIL>>"
 */
function extractEmailAddress(from) {
  if (!from) return null;
  
  // Check if the from string contains an email address in angle brackets
  const match = from.match(/<([^>]+)>/);
  if (match && match[1]) {
    return match[1];
  }
  
  // If no angle brackets, check if it's a plain email address
  const emailMatch = from.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
  if (emailMatch && emailMatch[1]) {
    return emailMatch[1];
  }
  
  return null;
}

/**
 * Extract ticket ID from subject like "Re: [Ticket #abc-123] Subject"
 */
function extractTicketIdFromSubject(subject) {
  if (!subject) return null;
  
  const match = subject.match(/\[Ticket #([^\]]+)\]/);
  if (match && match[1]) {
    return match[1];
  }
  
  return null;
}

/**
 * Clean email content by removing quoted replies and signatures
 */
function cleanEmailContent(text) {
  if (!text) return '';
  
  // Split the text into lines
  const lines = text.split('\n');
  
  // Find the first line that starts a quoted reply
  let quoteStart = lines.findIndex(line => 
    line.trim().startsWith('>') || 
    line.trim().startsWith('On ') && line.includes('wrote:') ||
    line.includes('From:') && line.includes('Sent:') && line.includes('To:')
  );
  
  // If no quote markers found, look for common reply separators
  if (quoteStart === -1) {
    quoteStart = lines.findIndex(line => 
      line.trim() === '---' || 
      line.trim() === '___' || 
      line.trim() === '***' ||
      line.trim().startsWith('--') && line.trim().length < 5
    );
  }
  
  // If a quote or separator was found, only keep the content before it
  if (quoteStart !== -1) {
    lines.splice(quoteStart);
  }
  
  // Join the remaining lines and trim
  return lines.join('\n').trim();
}