import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import SignableDocument from '../models/SignableDocument.js';
import DocumentSigner from '../models/DocumentSigner.js';
import DocumentSignature from '../models/DocumentSignature.js';
import DocumentField from '../models/DocumentField.js';
import DocumentAuditLog from '../models/DocumentAuditLog.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import Tenant from '../models/Tenant.js';
import UserFarm from '../models/UserFarm.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  validateFileType,
  generateStoragePath,
  saveFile,
  deleteFile,
  checkStorageQuota,
  updateStorageUsage
} from '../utils/fileUtils.js';
import { v4 as uuidv4 } from 'uuid';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

// Helper function to check if a user has access to a farm
export const checkUserFarmAccess = async (userId, farmId) => {
  if (!userId || !farmId) return false;

  const userFarm = await UserFarm.findOne({
    where: {
      user_id: userId,
      farm_id: farmId
    }
  });

  return !!userFarm;
};

// Helper function to create an audit log entry
export const createAuditLog = async (documentId, userId, signerId, eventType, eventDetails, ipAddress, userAgent, transaction) => {
  await DocumentAuditLog.create({
    document_id: documentId,
    user_id: userId,
    signer_id: signerId,
    event_type: eventType,
    event_details: eventDetails,
    ip_address: ipAddress,
    user_agent: userAgent
  }, { transaction });
};

/**
 * Get all signable documents for a farm
 */
export const getAllSignableDocuments = async (req, res) => {
  try {
    const { farmId } = req.params;
    const {
      search,
      documentType,
      status,
      page = 1,
      limit = 50,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { farm_id: farmId };

    // Add search condition if provided
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Filter by document type if provided
    if (documentType) {
      where.document_type = documentType;
    }

    // Filter by status if provided
    if (status) {
      where.status = status;
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    const order = [[sortBy, sortOrder.toUpperCase()]];

    let documents, count;

    try {
      // Get documents with associations
      const result = await SignableDocument.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'first_name', 'last_name', 'email'],
            required: false // Use LEFT JOIN to avoid association errors
          },
          {
            model: Farm,
            as: 'signableDocumentFarm',
            attributes: ['id', 'name'],
            required: false // Use LEFT JOIN to avoid association errors
          }
        ],
        order,
        limit,
        offset
      });

      documents = result.rows;
      count = result.count;
    } catch (associationError) {
      console.error('Association error in getAllSignableDocuments:', associationError);
      console.error('Error details:', {
        message: associationError.message,
        name: associationError.name,
        sql: associationError.sql
      });

      // If it's an association error, try a fallback approach
      if (associationError.message.includes('is not associated')) {
        console.log('Attempting fallback query without associations...');

        // Get documents without associations first
        const result = await SignableDocument.findAndCountAll({
          where,
          order,
          limit,
          offset
        });

        documents = result.rows;
        count = result.count;

        // Manually fetch associated data for each document
        for (let i = 0; i < documents.length; i++) {
          const doc = documents[i];

          // Fetch creator information
          try {
            const creator = await User.findByPk(doc.created_by, {
              attributes: ['id', 'first_name', 'last_name', 'email']
            });
            doc.dataValues.creator = creator;
          } catch (creatorError) {
            console.error(`Error fetching creator for document ${doc.id}:`, creatorError);
            doc.dataValues.creator = null;
          }

          // Fetch farm information
          try {
            const farm = await Farm.findByPk(doc.farm_id, {
              attributes: ['id', 'name']
            });
            doc.dataValues.signableDocumentFarm = farm;
          } catch (farmError) {
            console.error(`Error fetching farm for document ${doc.id}:`, farmError);
            doc.dataValues.signableDocumentFarm = null;
          }
        }
      } else {
        // If it's not an association error, re-throw it
        throw associationError;
      }
    }

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      documents,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error getting signable documents:', error);
    console.error('Error details:', {
      message: error.message,
      name: error.name,
      stack: error.stack
    });
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a signable document by ID
 */
export const getSignableDocumentById = async (req, res) => {
  try {
    const { id } = req.params;

    let document;

    try {
      // Get document with associations
      document = await SignableDocument.findByPk(id, {
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'first_name', 'last_name', 'email'],
            required: false // Use LEFT JOIN to avoid association errors
          },
          {
            model: Farm,
            as: 'signableDocumentFarm',
            attributes: ['id', 'name'],
            required: false // Use LEFT JOIN to avoid association errors
          },
          {
            model: DocumentSigner,
            as: 'signers',
            required: false // Use LEFT JOIN to avoid association errors
          }
        ]
      });
    } catch (associationError) {
      console.error('Association error in getSignableDocumentById:', associationError);
      console.error('Error details:', {
        message: associationError.message,
        name: associationError.name,
        sql: associationError.sql
      });

      // If it's an association error, try a fallback approach
      if (associationError.message.includes('is not associated')) {
        console.log('Attempting fallback query without associations...');

        // Get document without associations first
        document = await SignableDocument.findByPk(id);

        if (document) {
          // Manually fetch associated data
          try {
            const creator = await User.findByPk(document.created_by, {
              attributes: ['id', 'first_name', 'last_name', 'email']
            });
            document.dataValues.creator = creator;
          } catch (creatorError) {
            console.error(`Error fetching creator for document ${document.id}:`, creatorError);
            document.dataValues.creator = null;
          }

          try {
            const farm = await Farm.findByPk(document.farm_id, {
              attributes: ['id', 'name']
            });
            document.dataValues.signableDocumentFarm = farm;
          } catch (farmError) {
            console.error(`Error fetching farm for document ${document.id}:`, farmError);
            document.dataValues.signableDocumentFarm = null;
          }

          try {
            const signers = await DocumentSigner.findAll({
              where: { document_id: document.id }
            });
            document.dataValues.signers = signers;
          } catch (signersError) {
            console.error(`Error fetching signers for document ${document.id}:`, signersError);
            document.dataValues.signers = [];
          }
        }
      } else {
        // If it's not an association error, re-throw it
        throw associationError;
      }
    }

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    return res.status(200).json(document);
  } catch (error) {
    console.error('Error getting signable document:', error);
    console.error('Error details:', {
      message: error.message,
      name: error.name,
      stack: error.stack
    });
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a new signable document
 */
export const createSignableDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const {
      title,
      description,
      documentType,
      templateId,
      signers = [],
      encrypt = false
    } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if file was uploaded
    if (!req.files || !req.files.file) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.files.file;

    // Validate file type
    const fileBuffer = Buffer.from(file.data);
    const fileTypeValidation = await validateFileType(file.name, fileBuffer);

    if (!fileTypeValidation.valid) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Invalid file type',
        reason: fileTypeValidation.reason
      });
    }

    // Check storage quota
    const quotaCheck = await checkStorageQuota(farmId, file.size, req.user.is_global_admin);

    if (!quotaCheck.allowed) {
      await transaction.rollback();
      return res.status(400).json({
        error: quotaCheck.reason,
        currentUsage: quotaCheck.currentUsage,
        quota: quotaCheck.quota
      });
    }

    // Verify farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate storage path
    const storagePath = generateStoragePath(farmId, req.user.id, file.name);

    let fullPath;
    let encryptionDetails = null;

    // Save file to disk (encrypted or not)
    if (encrypt) {
      // Save encrypted file
      const result = await saveEncryptedFile(file.data, storagePath);
      fullPath = result.storagePath;
      encryptionDetails = result.encryptionDetails;
    } else {
      // Save unencrypted file
      fullPath = await saveFile(file.data, storagePath);
    }

    // Create document record
    const document = await SignableDocument.create({
      title: title || file.name,
      description: description || '',
      document_type: documentType || 'other',
      status: 'draft',
      file_path: storagePath,
      file_size: file.size,
      file_type: path.extname(file.name).substring(1) || 'unknown',
      mime_type: fileTypeValidation.detectedType || file.mimetype,
      version: 1,
      is_template: false,
      template_id: templateId || null,
      // tenant_id is now nullable and no longer used
      tenant_id: null,
      farm_id: farmId,
      created_by: req.user.id,
      is_encrypted: encrypt,
      encryption_method: encrypt ? encryptionDetails.method : null,
      encryption_key_id: encrypt ? encryptionDetails.key : null,
      encryption_iv: encrypt ? encryptionDetails.iv : null
    }, { transaction });

    // Create signers if provided
    if (signers.length > 0) {
      for (let i = 0; i < signers.length; i++) {
        const signer = signers[i];
        await DocumentSigner.create({
          document_id: document.id,
          signer_email: signer.email,
          signer_name: signer.name,
          signer_role: signer.role || null,
          signer_order: signer.order || i + 1,
          status: 'pending',
          verification_method: signer.verificationMethod || 'email'
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'document_created',
      { title: document.title, encrypted: encrypt },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Update storage usage
    await updateStorageUsage(farmId, file.size, false, 1);

    await transaction.commit();

    return res.status(201).json({
      message: 'Document created successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating signable document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Update a signable document
 */
export const updateSignableDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { title, description, documentType } = req.body;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Only allow updates if document is in draft status
    if (document.status !== 'draft') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document can only be updated in draft status' });
    }

    // Update document
    const updates = {};

    if (title) updates.title = title;
    if (description !== undefined) updates.description = description;
    if (documentType) updates.document_type = documentType;

    await document.update(updates, { transaction });

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'document_updated',
      { updates },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(200).json({
      message: 'Document updated successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating signable document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a signable document
 */
export const deleteSignableDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Only allow deletion if document is in draft status or if user is admin
    if (document.status !== 'draft' && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document can only be deleted in draft status' });
    }

    // Delete file from disk
    await deleteFile(document.file_path);

    // Update storage usage
    await updateStorageUsage(
      document.farm_id,
      -document.file_size,
      false,
      -1
    );

    // Create audit log entry before deleting the document
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'document_deleted',
      { title: document.title },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Delete document record (this will cascade to signers, signatures, fields, and audit logs)
    await document.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      message: 'Document deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting signable document:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Additional controller functions will be added in documentSigningControllerPart2.js
