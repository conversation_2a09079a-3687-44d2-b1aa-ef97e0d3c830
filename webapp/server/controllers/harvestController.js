import Harvest from '../models/Harvest.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import Crop from '../models/Crop.js';
import { sequelize } from '../config/database.js';

// Get all harvests for a farm
export const getFarmHarvests = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all harvests for the farm
    const harvests = await Harvest.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          as: 'harvestCrop',
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'DESC']]
    });

    return res.status(200).json({ harvests });
  } catch (error) {
    console.error('Error getting farm harvests:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get harvests for a specific field
export const getFieldHarvests = async (req, res) => {
  try {
    const { fieldId } = req.params;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Get all harvests for the field
    const harvests = await Harvest.findAll({
      where: { field_id: fieldId },
      include: [
        {
          model: Crop,
          as: 'harvestCrop',
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'DESC']]
    });

    return res.status(200).json({ harvests });
  } catch (error) {
    console.error('Error getting field harvests:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get harvests for a specific crop
export const getCropHarvests = async (req, res) => {
  try {
    const { cropId } = req.params;

    // Find crop to ensure it exists
    const crop = await Crop.findByPk(cropId);
    if (!crop) {
      return res.status(404).json({ error: 'Crop not found' });
    }

    // Get all harvests for the crop
    const harvests = await Harvest.findAll({
      where: { crop_id: cropId },
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        }
      ],
      order: [['scheduled_date', 'DESC']]
    });

    return res.status(200).json({ harvests });
  } catch (error) {
    console.error('Error getting crop harvests:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single harvest by ID
export const getHarvestById = async (req, res) => {
  try {
    const { harvestId } = req.params;

    const harvest = await Harvest.findByPk(harvestId, {
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          as: 'harvestCrop',
          attributes: ['id', 'name', 'variety']
        }
      ]
    });

    if (!harvest) {
      return res.status(404).json({ error: 'Harvest not found' });
    }

    return res.status(200).json({ harvest });
  } catch (error) {
    console.error('Error getting harvest:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new harvest
export const createHarvest = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      farmId, 
      fieldId, 
      cropId, 
      scheduledDate, 
      actualDate, 
      status,
      yieldAmount,
      yieldUnit,
      qualityRating,
      weatherConditions,
      notes
    } = req.body;

    // Validate required fields
    if (!farmId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    if (!fieldId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Field ID is required' });
    }

    if (!cropId) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Crop ID is required' });
    }

    if (!scheduledDate) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Scheduled date is required' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find field to ensure it exists and belongs to the farm
    const field = await Field.findOne({ where: { id: fieldId, farm_id: farmId } });
    if (!field) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Field not found or does not belong to the farm' });
    }

    // Find crop to ensure it exists and belongs to the farm
    const crop = await Crop.findOne({ where: { id: cropId, farm_id: farmId } });
    if (!crop) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Crop not found or does not belong to the farm' });
    }

    // Create harvest
    const harvest = await Harvest.create({
      farm_id: farmId,
      field_id: fieldId,
      crop_id: cropId,
      scheduled_date: scheduledDate,
      actual_date: actualDate,
      status: status || 'scheduled',
      yield_amount: yieldAmount,
      yield_unit: yieldUnit,
      quality_rating: qualityRating,
      weather_conditions: weatherConditions,
      notes
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({ 
      message: 'Harvest created successfully',
      harvest 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating harvest:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a harvest
export const updateHarvest = async (req, res) => {
  try {
    const { harvestId } = req.params;
    const { 
      scheduledDate, 
      actualDate, 
      status,
      yieldAmount,
      yieldUnit,
      qualityRating,
      weatherConditions,
      notes
    } = req.body;

    // Find harvest to ensure it exists
    const harvest = await Harvest.findByPk(harvestId);
    if (!harvest) {
      return res.status(404).json({ error: 'Harvest not found' });
    }

    // Update harvest
    await harvest.update({
      scheduled_date: scheduledDate || harvest.scheduled_date,
      actual_date: actualDate !== undefined ? actualDate : harvest.actual_date,
      status: status || harvest.status,
      yield_amount: yieldAmount !== undefined ? yieldAmount : harvest.yield_amount,
      yield_unit: yieldUnit !== undefined ? yieldUnit : harvest.yield_unit,
      quality_rating: qualityRating !== undefined ? qualityRating : harvest.quality_rating,
      weather_conditions: weatherConditions !== undefined ? weatherConditions : harvest.weather_conditions,
      notes: notes !== undefined ? notes : harvest.notes
    });

    return res.status(200).json({ 
      message: 'Harvest updated successfully',
      harvest 
    });
  } catch (error) {
    console.error('Error updating harvest:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete a harvest
export const deleteHarvest = async (req, res) => {
  try {
    const { harvestId } = req.params;

    // Find harvest to ensure it exists
    const harvest = await Harvest.findByPk(harvestId);
    if (!harvest) {
      return res.status(404).json({ error: 'Harvest not found' });
    }

    // Delete harvest
    await harvest.destroy();

    return res.status(200).json({ 
      message: 'Harvest deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting harvest:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get harvests by status
export const getHarvestsByStatus = async (req, res) => {
  try {
    const { status, farmId } = req.params;

    // Validate status
    const validStatuses = ['scheduled', 'in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status. Must be one of: scheduled, in_progress, completed, cancelled' });
    }

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all harvests with the specified status for the farm
    const harvests = await Harvest.findAll({
      where: { 
        farm_id: farmId,
        status 
      },
      include: [
        {
          model: Field,
          attributes: ['id', 'name', 'size', 'size_unit']
        },
        {
          model: Crop,
          as: 'harvestCrop',
          attributes: ['id', 'name', 'variety']
        }
      ],
      order: [['scheduled_date', 'DESC']]
    });

    return res.status(200).json({ harvests });
  } catch (error) {
    console.error('Error getting harvests by status:', error);
    return res.status(500).json({ error: error.message });
  }
};
