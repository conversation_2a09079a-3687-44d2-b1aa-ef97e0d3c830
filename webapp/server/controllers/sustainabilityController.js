import db from '../db.js';
import { validationResult } from 'express-validator';

// Carbon Footprint Calculator
export const calculateCarbonFootprint = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      calculationDate, 
      fuelEmissions, 
      electricityEmissions, 
      fertilizerEmissions, 
      livestockEmissions, 
      wasteEmissions, 
      notes 
    } = req.body;

    // Calculate total emissions
    const totalEmissions = 
      parseFloat(fuelEmissions || 0) + 
      parseFloat(electricityEmissions || 0) + 
      parseFloat(fertilizerEmissions || 0) + 
      parseFloat(livestockEmissions || 0) + 
      parseFloat(wasteEmissions || 0);

    const result = await db.query(
      `INSERT INTO carbon_footprints (
        farm_id, 
        calculation_date, 
        total_emissions, 
        fuel_emissions, 
        electricity_emissions, 
        fertilizer_emissions, 
        livestock_emissions, 
        waste_emissions, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *`,
      [
        farmId, 
        calculationDate, 
        totalEmissions, 
        fuelEmissions, 
        electricityEmissions, 
        fertilizerEmissions, 
        livestockEmissions, 
        wasteEmissions, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error calculating carbon footprint:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getCarbonFootprints = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const result = await db.query(
      'SELECT * FROM carbon_footprints WHERE farm_id = $1 ORDER BY calculation_date DESC',
      [farmId]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting carbon footprints:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getCarbonFootprint = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM carbon_footprints WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Carbon footprint not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting carbon footprint:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateCarbonFootprint = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      calculationDate, 
      fuelEmissions, 
      electricityEmissions, 
      fertilizerEmissions, 
      livestockEmissions, 
      wasteEmissions, 
      notes 
    } = req.body;

    // Calculate total emissions
    const totalEmissions = 
      parseFloat(fuelEmissions || 0) + 
      parseFloat(electricityEmissions || 0) + 
      parseFloat(fertilizerEmissions || 0) + 
      parseFloat(livestockEmissions || 0) + 
      parseFloat(wasteEmissions || 0);

    const result = await db.query(
      `UPDATE carbon_footprints SET 
        calculation_date = $1, 
        total_emissions = $2, 
        fuel_emissions = $3, 
        electricity_emissions = $4, 
        fertilizer_emissions = $5, 
        livestock_emissions = $6, 
        waste_emissions = $7, 
        notes = $8
      WHERE id = $9 RETURNING *`,
      [
        calculationDate, 
        totalEmissions, 
        fuelEmissions, 
        electricityEmissions, 
        fertilizerEmissions, 
        livestockEmissions, 
        wasteEmissions, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Carbon footprint not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating carbon footprint:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteCarbonFootprint = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM carbon_footprints WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Carbon footprint not found' });
    }

    res.json({ message: 'Carbon footprint deleted successfully' });
  } catch (error) {
    console.error('Error deleting carbon footprint:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Sustainable Practices
export const createSustainablePractice = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      name, 
      description, 
      category, 
      implementationDate, 
      status, 
      impact, 
      notes 
    } = req.body;

    const result = await db.query(
      `INSERT INTO sustainable_practices (
        farm_id, 
        name, 
        description, 
        category, 
        implementation_date, 
        status, 
        impact, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [
        farmId, 
        name, 
        description, 
        category, 
        implementationDate, 
        status, 
        impact, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating sustainable practice:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getSustainablePractices = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const result = await db.query(
      'SELECT * FROM sustainable_practices WHERE farm_id = $1 ORDER BY implementation_date DESC',
      [farmId]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting sustainable practices:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getSustainablePractice = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM sustainable_practices WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Sustainable practice not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting sustainable practice:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateSustainablePractice = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      name, 
      description, 
      category, 
      implementationDate, 
      status, 
      impact, 
      notes 
    } = req.body;

    const result = await db.query(
      `UPDATE sustainable_practices SET 
        name = $1, 
        description = $2, 
        category = $3, 
        implementation_date = $4, 
        status = $5, 
        impact = $6, 
        notes = $7
      WHERE id = $8 RETURNING *`,
      [
        name, 
        description, 
        category, 
        implementationDate, 
        status, 
        impact, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Sustainable practice not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating sustainable practice:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteSustainablePractice = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM sustainable_practices WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Sustainable practice not found' });
    }

    res.json({ message: 'Sustainable practice deleted successfully' });
  } catch (error) {
    console.error('Error deleting sustainable practice:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Certifications
export const createCertification = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      name, 
      certifyingBody, 
      issueDate, 
      expirationDate, 
      status, 
      documentUrl, 
      notes 
    } = req.body;

    const result = await db.query(
      `INSERT INTO certifications (
        farm_id, 
        name, 
        certifying_body, 
        issue_date, 
        expiration_date, 
        status, 
        document_url, 
        notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [
        farmId, 
        name, 
        certifyingBody, 
        issueDate, 
        expirationDate, 
        status, 
        documentUrl, 
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getCertifications = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const result = await db.query(
      'SELECT * FROM certifications WHERE farm_id = $1 ORDER BY issue_date DESC',
      [farmId]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error getting certifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getCertification = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM certifications WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Certification not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error getting certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateCertification = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      name, 
      certifyingBody, 
      issueDate, 
      expirationDate, 
      status, 
      documentUrl, 
      notes 
    } = req.body;

    const result = await db.query(
      `UPDATE certifications SET 
        name = $1, 
        certifying_body = $2, 
        issue_date = $3, 
        expiration_date = $4, 
        status = $5, 
        document_url = $6, 
        notes = $7
      WHERE id = $8 RETURNING *`,
      [
        name, 
        certifyingBody, 
        issueDate, 
        expirationDate, 
        status, 
        documentUrl, 
        notes,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Certification not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteCertification = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM certifications WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Certification not found' });
    }

    res.json({ message: 'Certification deleted successfully' });
  } catch (error) {
    console.error('Error deleting certification:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Environmental Impact Reports
export const generateEnvironmentalImpactReport = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      farmId, 
      reportDate, 
      reportType, 
      metrics, 
      summary, 
      recommendations 
    } = req.body;

    const result = await db.query(
      `INSERT INTO environmental_impact_reports (
        farm_id, 
        report_date, 
        report_type, 
        metrics, 
        summary, 
        recommendations
      ) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
      [
        farmId, 
        reportDate, 
        reportType, 
        JSON.stringify(metrics), 
        summary, 
        recommendations
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error generating environmental impact report:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getEnvironmentalImpactReports = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({ message: 'Farm ID is required' });
    }

    const result = await db.query(
      'SELECT * FROM environmental_impact_reports WHERE farm_id = $1 ORDER BY report_date DESC',
      [farmId]
    );

    // Parse metrics JSON for each report
    const reports = result.rows.map(report => ({
      ...report,
      metrics: JSON.parse(report.metrics)
    }));

    res.json(reports);
  } catch (error) {
    console.error('Error getting environmental impact reports:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const getEnvironmentalImpactReport = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM environmental_impact_reports WHERE id = $1',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Environmental impact report not found' });
    }

    // Parse metrics JSON
    const report = {
      ...result.rows[0],
      metrics: JSON.parse(result.rows[0].metrics)
    };

    res.json(report);
  } catch (error) {
    console.error('Error getting environmental impact report:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const updateEnvironmentalImpactReport = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { 
      reportDate, 
      reportType, 
      metrics, 
      summary, 
      recommendations 
    } = req.body;

    const result = await db.query(
      `UPDATE environmental_impact_reports SET 
        report_date = $1, 
        report_type = $2, 
        metrics = $3, 
        summary = $4, 
        recommendations = $5
      WHERE id = $6 RETURNING *`,
      [
        reportDate, 
        reportType, 
        JSON.stringify(metrics), 
        summary, 
        recommendations,
        id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Environmental impact report not found' });
    }

    // Parse metrics JSON
    const report = {
      ...result.rows[0],
      metrics: JSON.parse(result.rows[0].metrics)
    };

    res.json(report);
  } catch (error) {
    console.error('Error updating environmental impact report:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

export const deleteEnvironmentalImpactReport = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM environmental_impact_reports WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Environmental impact report not found' });
    }

    res.json({ message: 'Environmental impact report deleted successfully' });
  } catch (error) {
    console.error('Error deleting environmental impact report:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
