import { sequelize, Op } from '../config/database.js';
import DigitalCertificate from '../models/DigitalCertificate.js';
import SignableDocument from '../models/SignableDocument.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import { verifyDigitalSignature, createDigitalCertificate } from '../utils/digitalSignatureUtils.js';
import { verifyDocumentOnBlockchain } from '../utils/blockchainUtils.js';
import forge from 'node-forge';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { uploadToSpaces } from '../utils/spacesUtils.js';

/**
 * Get all digital certificates (admin only)
 */
export const getAllCertificates = async (req, res) => {
  try {
    const certificates = await DigitalCertificate.findAll({
      attributes: {
        exclude: ['private_key', 'certificate_data', 'passphrase']
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      certificates
    });
  } catch (error) {
    console.error('Error getting all certificates:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get all documents with digital signatures (admin only)
 */
export const getDocumentsWithDigitalSignatures = async (req, res) => {
  try {
    const documents = await SignableDocument.findAll({
      where: {
        has_digital_certificate_signature: true
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      documents
    });
  } catch (error) {
    console.error('Error getting documents with digital signatures:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Verify a document's digital signature (admin only)
 */
export const verifyDocumentSignature = async (req, res) => {
  try {
    const { documentId } = req.params;

    // Get document
    const document = await SignableDocument.findByPk(documentId);

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    // Check if document has digital certificate signature
    if (!document.has_digital_certificate_signature) {
      return res.status(400).json({ error: 'Document does not have a digital certificate signature' });
    }

    // Verify the digital signature
    const signatureVerificationResult = await verifyDigitalSignature(document.file_path);

    // If document also has blockchain verification, verify that too
    let blockchainVerificationResult = null;
    if (document.has_blockchain_verification) {
      blockchainVerificationResult = await verifyDocumentOnBlockchain(document.id);
    }

    return res.status(200).json({
      document: {
        id: document.id,
        title: document.title,
        status: document.status
      },
      signatureVerification: signatureVerificationResult,
      blockchainVerification: blockchainVerificationResult
    });
  } catch (error) {
    console.error('Error verifying document signature:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get certificate statistics (admin only)
 */
export const getCertificateStats = async (req, res) => {
  try {
    // Get total number of certificates
    const totalCertificates = await DigitalCertificate.count();

    // Get number of active certificates
    const activeCertificates = await DigitalCertificate.count({
      where: {
        is_active: true
      }
    });

    // Get number of expired certificates
    const expiredCertificates = await DigitalCertificate.count({
      where: {
        valid_to: {
          [Op.lt]: new Date()
        }
      }
    });

    // Get number of documents with digital signatures
    const documentsWithSignatures = await SignableDocument.count({
      where: {
        has_digital_certificate_signature: true
      }
    });

    // Get number of documents with blockchain verification
    const documentsWithBlockchain = await SignableDocument.count({
      where: {
        has_blockchain_verification: true
      }
    });

    return res.status(200).json({
      stats: {
        totalCertificates,
        activeCertificates,
        expiredCertificates,
        documentsWithSignatures,
        documentsWithBlockchain
      }
    });
  } catch (error) {
    console.error('Error getting certificate stats:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a new digital certificate (admin only)
 */
export const createCertificate = async (req, res) => {
  try {
    const { 
      farmId, 
      certificateName, 
      commonName, 
      organization, 
      organizationalUnit, 
      locality, 
      state, 
      country, 
      validityDays,
      passphrase
    } = req.body;

    // Validate required fields
    if (!farmId || !certificateName || !commonName || !organization || !validityDays) {
      return res.status(400).json({ error: 'Missing required certificate information' });
    }

    // Get the farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate a new key pair
    const keys = forge.pki.rsa.generateKeyPair(2048);

    // Create a certificate
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey;
    cert.serialNumber = '01' + Math.floor(Math.random() * 100000000).toString();

    // Set certificate validity period
    const now = new Date();
    cert.validity.notBefore = now;
    cert.validity.notAfter = new Date();
    cert.validity.notAfter.setDate(now.getDate() + parseInt(validityDays, 10));

    // Set certificate subject
    const attrs = [
      { name: 'commonName', value: commonName },
      { name: 'organizationName', value: organization }
    ];

    if (organizationalUnit) attrs.push({ name: 'organizationalUnitName', value: organizationalUnit });
    if (locality) attrs.push({ name: 'localityName', value: locality });
    if (state) attrs.push({ name: 'stateOrProvinceName', value: state });
    if (country) attrs.push({ name: 'countryName', value: country });

    cert.setSubject(attrs);

    // Set certificate issuer (self-signed)
    cert.setIssuer(attrs);

    // Set certificate extensions
    cert.setExtensions([
      {
        name: 'basicConstraints',
        cA: true
      },
      {
        name: 'keyUsage',
        digitalSignature: true,
        nonRepudiation: true,
        keyEncipherment: true,
        dataEncipherment: true
      },
      {
        name: 'extKeyUsage',
        serverAuth: true,
        clientAuth: true,
        codeSigning: true,
        emailProtection: true,
        timeStamping: true
      },
      {
        name: 'nsCertType',
        client: true,
        server: true,
        email: true,
        objsign: true
      }
    ]);

    // Self-sign the certificate
    cert.sign(keys.privateKey, forge.md.sha256.create());

    // Convert to PEM format
    const certPem = forge.pki.certificateToPem(cert);

    // Encrypt the private key with passphrase if provided
    let privateKeyPem;
    if (passphrase) {
      const encryptedPrivateKey = forge.pki.encryptRsaPrivateKey(keys.privateKey, passphrase);
      privateKeyPem = encryptedPrivateKey;
    } else {
      privateKeyPem = forge.pki.privateKeyToPem(keys.privateKey);
    }

    // Create certificate in database
    const certificateData = {
      farm_id: farmId,
      user_id: req.user.id, // Current admin user
      certificate_name: certificateName,
      certificate_data: Buffer.from(certPem),
      private_key: Buffer.from(privateKeyPem),
      passphrase: passphrase || null,
      certificate_type: 'X.509',
      issuer: organization,
      subject: commonName,
      valid_from: cert.validity.notBefore,
      valid_to: cert.validity.notAfter,
      serial_number: cert.serialNumber,
      is_active: true
    };

    const certificate = await createDigitalCertificate(certificateData);

    return res.status(201).json({
      message: 'Certificate created successfully',
      certificate: {
        id: certificate.id,
        name: certificate.certificate_name,
        issuer: certificate.issuer,
        subject: certificate.subject,
        validFrom: certificate.valid_from,
        validTo: certificate.valid_to,
        serialNumber: certificate.serial_number
      }
    });
  } catch (error) {
    console.error('Error creating certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Upload an existing certificate (admin only)
 */
export const uploadCertificate = async (req, res) => {
  try {
    const { farmId, certificateName, passphrase } = req.body;

    // Validate required fields
    if (!farmId || !certificateName) {
      return res.status(400).json({ error: 'Missing required certificate information' });
    }

    // Check if certificate file was uploaded
    if (!req.files || !req.files.certificate) {
      return res.status(400).json({ error: 'Certificate file is required' });
    }

    // Check if private key file was uploaded
    if (!req.files || !req.files.privateKey) {
      return res.status(400).json({ error: 'Private key file is required' });
    }

    // Get the farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get uploaded files
    const certificateFile = req.files.certificate;
    const privateKeyFile = req.files.privateKey;

    // Read certificate
    const certPem = certificateFile.data.toString();
    let cert;
    try {
      cert = forge.pki.certificateFromPem(certPem);
    } catch (error) {
      return res.status(400).json({ error: 'Invalid certificate file' });
    }

    // Read private key
    const privateKeyPem = privateKeyFile.data.toString();
    try {
      // Try to parse the private key to validate it
      if (passphrase) {
        forge.pki.decryptRsaPrivateKey(privateKeyPem, passphrase);
      } else {
        forge.pki.privateKeyFromPem(privateKeyPem);
      }
    } catch (error) {
      return res.status(400).json({ 
        error: passphrase 
          ? 'Invalid private key or incorrect passphrase' 
          : 'Invalid private key'
      });
    }

    // Extract certificate information
    const subject = cert.subject.getField('CN') ? cert.subject.getField('CN').value : 'Unknown';
    const issuer = cert.issuer.getField('O') ? cert.issuer.getField('O').value : 'Unknown';

    // Create certificate in database
    const certificateData = {
      farm_id: farmId,
      user_id: req.user.id, // Current admin user
      certificate_name: certificateName,
      certificate_data: Buffer.from(certPem),
      private_key: Buffer.from(privateKeyPem),
      passphrase: passphrase || null,
      certificate_type: 'X.509',
      issuer: issuer,
      subject: subject,
      valid_from: cert.validity.notBefore,
      valid_to: cert.validity.notAfter,
      serial_number: cert.serialNumber,
      is_active: true
    };

    const certificate = await createDigitalCertificate(certificateData);

    return res.status(201).json({
      message: 'Certificate uploaded successfully',
      certificate: {
        id: certificate.id,
        name: certificate.certificate_name,
        issuer: certificate.issuer,
        subject: certificate.subject,
        validFrom: certificate.valid_from,
        validTo: certificate.valid_to,
        serialNumber: certificate.serial_number
      }
    });
  } catch (error) {
    console.error('Error uploading certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a digital certificate (admin only)
 */
export const deleteCertificate = async (req, res) => {
  try {
    const { certificateId } = req.params;

    // Find the certificate
    const certificate = await DigitalCertificate.findByPk(certificateId);

    if (!certificate) {
      return res.status(404).json({ error: 'Certificate not found' });
    }

    // Delete the certificate
    await certificate.destroy();

    return res.status(200).json({
      message: 'Certificate deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting certificate:', error);
    return res.status(500).json({ error: error.message });
  }
};

export default {
  getAllCertificates,
  getDocumentsWithDigitalSignatures,
  verifyDocumentSignature,
  getCertificateStats,
  createCertificate,
  uploadCertificate,
  deleteCertificate
};
