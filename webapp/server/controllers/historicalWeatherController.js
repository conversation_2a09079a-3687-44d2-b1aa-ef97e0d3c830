import { Op } from 'sequelize';
import Weather from '../models/Weather.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import { sequelize } from '../config/database.js';

// Get historical weather data for a farm
export const getFarmHistoricalWeather = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate, interval = 'daily' } = req.query;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const where = { 
      farm_id: farmId,
      field_id: null // Only get farm-level data
    };

    // Add date range if provided
    if (startDate || endDate) {
      where.timestamp = {};

      if (startDate) {
        where.timestamp[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.timestamp[Op.lte] = endDateTime;
      }
    }

    // Get historical weather data
    const historicalData = await Weather.findAll({
      where,
      order: [['timestamp', 'ASC']]
    });

    // Process data based on interval
    const processedData = processHistoricalData(historicalData, interval);

    // Analyze the data
    const analysis = analyzeHistoricalData(processedData);

    return res.status(200).json({
      data: processedData,
      analysis
    });
  } catch (error) {
    console.error('Error getting farm historical weather:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get historical weather data for a field
export const getFieldHistoricalWeather = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { startDate, endDate, interval = 'daily' } = req.query;

    // Find field to ensure it exists
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }

    // Build query conditions
    const where = { 
      field_id: fieldId
    };

    // Add date range if provided
    if (startDate || endDate) {
      where.timestamp = {};

      if (startDate) {
        where.timestamp[Op.gte] = new Date(startDate);
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.timestamp[Op.lte] = endDateTime;
      }
    }

    // Get historical weather data
    const historicalData = await Weather.findAll({
      where,
      order: [['timestamp', 'ASC']]
    });

    // Process data based on interval
    const processedData = processHistoricalData(historicalData, interval);

    // Analyze the data
    const analysis = analyzeHistoricalData(processedData);

    return res.status(200).json({
      data: processedData,
      analysis
    });
  } catch (error) {
    console.error('Error getting field historical weather:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to process historical data based on interval
export const processHistoricalData = (data, interval) => {
  // Group data by interval
  const groupedData = {};

  data.forEach(record => {
    const date = new Date(record.timestamp);
    let key;

    switch (interval) {
      case 'hourly':
        // Group by hour
        key = date.toISOString().split(':')[0] + ':00:00Z';
        break;
      case 'daily':
        // Group by day
        key = date.toISOString().split('T')[0];
        break;
      case 'weekly':
        // Group by week (use the first day of the week)
        const dayOfWeek = date.getDay();
        const firstDayOfWeek = new Date(date);
        firstDayOfWeek.setDate(date.getDate() - dayOfWeek);
        key = firstDayOfWeek.toISOString().split('T')[0];
        break;
      case 'monthly':
        // Group by month
        key = date.toISOString().substring(0, 7); // YYYY-MM
        break;
      default:
        // Default to daily
        key = date.toISOString().split('T')[0];
    }

    if (!groupedData[key]) {
      groupedData[key] = {
        interval_start: key,
        temperature_sum: 0,
        temperature_count: 0,
        precipitation_sum: 0,
        precipitation_count: 0,
        humidity_sum: 0,
        humidity_count: 0,
        wind_speed_sum: 0,
        wind_speed_count: 0,
        conditions: {}
      };
    }

    // Sum up numeric values
    if (record.temperature !== null && !isNaN(record.temperature)) {
      groupedData[key].temperature_sum += record.temperature;
      groupedData[key].temperature_count++;
    }

    if (record.precipitation !== null && !isNaN(record.precipitation)) {
      groupedData[key].precipitation_sum += record.precipitation;
      groupedData[key].precipitation_count++;
    }

    if (record.humidity !== null && !isNaN(record.humidity)) {
      groupedData[key].humidity_sum += record.humidity;
      groupedData[key].humidity_count++;
    }

    if (record.wind_speed !== null && !isNaN(record.wind_speed)) {
      groupedData[key].wind_speed_sum += record.wind_speed;
      groupedData[key].wind_speed_count++;
    }

    // Count conditions
    if (record.condition) {
      if (!groupedData[key].conditions[record.condition]) {
        groupedData[key].conditions[record.condition] = 0;
      }
      groupedData[key].conditions[record.condition]++;
    }
  });

  // Calculate averages and find most common condition
  const processedData = Object.values(groupedData).map(group => {
    // Calculate averages
    const temperature_avg = group.temperature_count > 0 ? group.temperature_sum / group.temperature_count : null;
    const precipitation_avg = group.precipitation_count > 0 ? group.precipitation_sum / group.precipitation_count : null;
    const humidity_avg = group.humidity_count > 0 ? group.humidity_sum / group.humidity_count : null;
    const wind_speed_avg = group.wind_speed_count > 0 ? group.wind_speed_sum / group.wind_speed_count : null;

    // Find most common condition
    let most_common_condition = null;
    let max_count = 0;

    Object.entries(group.conditions).forEach(([condition, count]) => {
      if (count > max_count) {
        most_common_condition = condition;
        max_count = count;
      }
    });

    return {
      interval_start: group.interval_start,
      temperature_avg,
      precipitation_avg,
      humidity_avg,
      wind_speed_avg,
      most_common_condition
    };
  });

  return processedData;
};

// Helper function to analyze historical data
export const analyzeHistoricalData = (data) => {
  // Skip analysis if not enough data
  if (!data || data.length < 2) {
    return {
      temperature_trend: 'insufficient_data',
      precipitation_trend: 'insufficient_data',
      humidity_trend: 'insufficient_data',
      wind_speed_trend: 'insufficient_data',
      condition_summary: {}
    };
  }

  // Calculate trends
  const firstRecord = data[0];
  const lastRecord = data[data.length - 1];

  // Temperature trend
  let temperature_trend = 'stable';
  if (lastRecord.temperature_avg > firstRecord.temperature_avg * 1.1) {
    temperature_trend = 'increasing';
  } else if (lastRecord.temperature_avg < firstRecord.temperature_avg * 0.9) {
    temperature_trend = 'decreasing';
  }

  // Precipitation trend
  let precipitation_trend = 'stable';
  if (lastRecord.precipitation_avg > firstRecord.precipitation_avg * 1.1) {
    precipitation_trend = 'increasing';
  } else if (lastRecord.precipitation_avg < firstRecord.precipitation_avg * 0.9) {
    precipitation_trend = 'decreasing';
  }

  // Humidity trend
  let humidity_trend = 'stable';
  if (lastRecord.humidity_avg > firstRecord.humidity_avg * 1.1) {
    humidity_trend = 'increasing';
  } else if (lastRecord.humidity_avg < firstRecord.humidity_avg * 0.9) {
    humidity_trend = 'decreasing';
  }

  // Wind speed trend
  let wind_speed_trend = 'stable';
  if (lastRecord.wind_speed_avg > firstRecord.wind_speed_avg * 1.1) {
    wind_speed_trend = 'increasing';
  } else if (lastRecord.wind_speed_avg < firstRecord.wind_speed_avg * 0.9) {
    wind_speed_trend = 'decreasing';
  }

  // Condition summary
  const condition_summary = {};
  data.forEach(record => {
    if (record.most_common_condition) {
      if (!condition_summary[record.most_common_condition]) {
        condition_summary[record.most_common_condition] = 0;
      }
      condition_summary[record.most_common_condition]++;
    }
  });

  // Calculate averages
  const temperature_values = data.map(record => record.temperature_avg).filter(val => val !== null && !isNaN(val));
  const precipitation_values = data.map(record => record.precipitation_avg).filter(val => val !== null && !isNaN(val));
  const humidity_values = data.map(record => record.humidity_avg).filter(val => val !== null && !isNaN(val));
  const wind_speed_values = data.map(record => record.wind_speed_avg).filter(val => val !== null && !isNaN(val));

  const temperature_avg = temperature_values.length > 0 ? temperature_values.reduce((sum, val) => sum + val, 0) / temperature_values.length : null;
  const precipitation_avg = precipitation_values.length > 0 ? precipitation_values.reduce((sum, val) => sum + val, 0) / precipitation_values.length : null;
  const humidity_avg = humidity_values.length > 0 ? humidity_values.reduce((sum, val) => sum + val, 0) / humidity_values.length : null;
  const wind_speed_avg = wind_speed_values.length > 0 ? wind_speed_values.reduce((sum, val) => sum + val, 0) / wind_speed_values.length : null;

  return {
    temperature_trend,
    precipitation_trend,
    humidity_trend,
    wind_speed_trend,
    condition_summary,
    temperature_avg,
    precipitation_avg,
    humidity_avg,
    wind_speed_avg
  };
};
