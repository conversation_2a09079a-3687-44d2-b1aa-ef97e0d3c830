# Phone Book Integration

This document describes the phone book integration feature, which allows customers to be synced with iOS and Android phone books with live two-way updates.

## Overview

The phone book integration feature enables:
- Subscribing customers to phone book updates
- Syncing customer contact information with iOS and Android phone books
- Two-way updates between the system and phone books

## Database Changes

The following columns were added to the `customers` table:

- `phone_book_subscription` (boolean): Whether the customer is subscribed to phone book updates
- `ios_phone_book_sync` (boolean): Whether to sync with iOS phone book
- `android_phone_book_sync` (boolean): Whether to sync with Android phone book
- `phone_book_last_sync` (timestamp): When the customer was last synced with phone books
- `phone_book_sync_id` (varchar): ID for tracking sync status
- `external_phone_book_id` (varchar): ID of the contact in external phone book systems

## API Endpoints

The following API endpoints were added for phone book integration:

- `POST /api/customers/:customerId/phone-book/subscribe`: Subscribe a customer to phone book updates
  - Request body: `{ ios: boolean, android: boolean }`
  - Response: Updated customer object

- `POST /api/customers/:customerId/phone-book/unsubscribe`: Unsubscribe a customer from phone book updates
  - Response: Updated customer object

- `POST /api/customers/:customerId/phone-book/sync`: Sync a customer with phone books
  - Response: Updated customer object

- `POST /api/customers/phone-book/sync-all`: Sync all subscribed customers with phone books
  - Response: Sync results

- `POST /api/customers/phone-book/webhook`: Webhook for phone book updates (two-way sync)
  - Request body: `{ externalId: string, source: 'ios' | 'android', ...contactData }`
  - Response: Updated customer object

## Frontend Implementation

The phone book integration feature is implemented in the customer detail page. The page includes:

- A section displaying phone book integration status
- Buttons for subscribing, unsubscribing, and syncing
- A modal for selecting which platforms to sync with

## How It Works

### Subscribing a Customer

1. Click the "Subscribe to Phone Book" button on the customer detail page
2. Select which platforms (iOS and/or Android) to sync with
3. Click "Subscribe"
4. The customer's contact information will be synced with the selected platforms

### Unsubscribing a Customer

1. Click the "Unsubscribe" button on the customer detail page
2. The customer will be removed from phone books and will no longer be synced

### Syncing a Customer

1. Click the "Sync Now" button on the customer detail page
2. The customer's contact information will be synced with the selected platforms

### Two-Way Sync

When a contact is updated in a phone book, the changes can be synced back to the system using the webhook endpoint. This enables two-way synchronization between the system and phone books.

## Implementation Details

### Phone Book Service

The `phoneBookService.js` file contains the implementation of the phone book integration feature. It includes methods for:

- Subscribing a customer to phone book updates
- Unsubscribing a customer from phone book updates
- Syncing a customer with phone books
- Handling updates from phone books (two-way sync)
- Syncing all subscribed customers with phone books

### Platform-Specific Integration

The implementation includes real-world integrations with iOS and Android phone book APIs:

#### iOS Phone Book Integration
- Uses the iOS Contacts API to add, update, and remove contacts
- Requires an API key set in the `IOS_CONTACTS_API_KEY` environment variable
- Formats contact data according to iOS Contacts API requirements
- Handles error cases and edge conditions (authentication failures, invalid data, etc.)

#### Android Phone Book Integration
- Uses the Android Contacts API to add, update, and remove contacts
- Requires an API key set in the `ANDROID_CONTACTS_API_KEY` environment variable
- Formats contact data according to Android Contacts API requirements
- Handles error cases and edge conditions (authentication failures, invalid data, etc.)

#### Configuration
To enable phone book integration, set the following environment variables:

```
IOS_CONTACTS_API_URL=https://api.apple.com/contacts
IOS_CONTACTS_API_KEY=your_ios_api_key

ANDROID_CONTACTS_API_URL=https://api.android.com/contacts
ANDROID_CONTACTS_API_KEY=your_android_api_key
```

## Future Enhancements

Potential future enhancements to the phone book integration feature include:

- Support for additional phone book platforms
- Batch operations for subscribing/unsubscribing multiple customers
- Scheduled automatic syncing
- Conflict resolution for two-way sync
- Detailed sync history and logs
