import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';
import { checkPermission, PermissionType } from '../services/rolePermissionService';

/**
 * Custom hook to check tax management permissions
 */
const useTaxPermissions = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [permissions, setPermissions] = useState({
    canView: false,
    canCreate: false,
    canEdit: false,
    canDelete: false,
    isLoading: true,
    error: null as string | null
  });

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!user || !currentFarm) {
        setPermissions({
          canView: false,
          canCreate: false,
          canEdit: false,
          canDelete: false,
          isLoading: false,
          error: 'User or farm not available'
        });
        return;
      }

      try {
        // Check permissions for tax management
        const canView = await checkPermission(
          currentFarm.id,
          user.id,
          'tax_management',
          'view'
        );

        const canCreate = await checkPermission(
          currentFarm.id,
          user.id,
          'tax_management',
          'create'
        );

        const canEdit = await checkPermission(
          currentFarm.id,
          user.id,
          'tax_management',
          'edit'
        );

        const canDelete = await checkPermission(
          currentFarm.id,
          user.id,
          'tax_management',
          'delete'
        );

        setPermissions({
          canView,
          canCreate,
          canEdit,
          canDelete,
          isLoading: false,
          error: null
        });
      } catch (error) {
        setPermissions({
          canView: false,
          canCreate: false,
          canEdit: false,
          canDelete: false,
          isLoading: false,
          error: 'Failed to fetch permissions'
        });
      }
    };

    fetchPermissions();
  }, [user, currentFarm]);

  return permissions;
};

export default useTaxPermissions;