import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useAuth } from './AuthContext';
import axios from 'axios';

// Types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  message: string;
  title?: string;
  duration?: number; // Duration in milliseconds
  onClick?: () => void;
  createdAt: Date;
}

export interface NotificationPreferences {
  enableInApp: boolean;
  enableEmail: boolean;
  chatMessageNotifications: boolean;
  taskNotifications: boolean;
  documentNotifications: boolean;
  systemNotifications: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  showNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  dismissNotification: (id: string) => void;
  dismissAllNotifications: () => void;
  preferences: NotificationPreferences;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
}

// Create context with default values
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  showNotification: () => {},
  dismissNotification: () => {},
  dismissAllNotifications: () => {},
  preferences: {
    enableInApp: true,
    enableEmail: true,
    chatMessageNotifications: true,
    taskNotifications: true,
    documentNotifications: true,
    systemNotifications: true,
  },
  updatePreferences: async () => {},
});

// Provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    enableInApp: true,
    enableEmail: true,
    chatMessageNotifications: true,
    taskNotifications: true,
    documentNotifications: true,
    systemNotifications: true,
  });

  // Load notification preferences
  const loadPreferences = useCallback(async () => {
    if (!user) return;

    try {
      const response = await axios.get('/api/notifications/preferences');
      setPreferences(response.data);
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      // Use default preferences if there's an error
    }
  }, [user]);

  // Update notification preferences
  const updatePreferences = useCallback(async (newPreferences: Partial<NotificationPreferences>) => {
    if (!user) return;

    try {
      const updatedPreferences = { ...preferences, ...newPreferences };
      await axios.post('/api/notifications/preferences', updatedPreferences);
      setPreferences(updatedPreferences);
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }, [user, preferences]);

  // Show a notification
  const showNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt'>) => {
    // Only show in-app notifications if enabled in preferences
    if (!preferences.enableInApp) return;

    // Check if the notification type is enabled in preferences
    if (
      (notification.type === 'info' && !preferences.chatMessageNotifications) ||
      (notification.type === 'success' && !preferences.taskNotifications) ||
      (notification.type === 'warning' && !preferences.documentNotifications) ||
      (notification.type === 'error' && !preferences.systemNotifications)
    ) {
      return;
    }

    const id = Math.random().toString(36).substring(2, 11);
    const newNotification: Notification = {
      ...notification,
      id,
      createdAt: new Date(),
    };

    setNotifications((prev) => [...prev, newNotification]);

    // Auto-dismiss after duration (default: 5000ms)
    if (notification.duration !== 0) {
      setTimeout(() => {
        dismissNotification(id);
      }, notification.duration || 5000);
    }
  }, [preferences]);

  // Dismiss a notification
  const dismissNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  }, []);

  // Dismiss all notifications
  const dismissAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Load preferences on mount
  useEffect(() => {
    if (user) {
      loadPreferences();
    }
  }, [user, loadPreferences]);

  // Context value
  const value = {
    notifications,
    showNotification,
    dismissNotification,
    dismissAllNotifications,
    preferences,
    updatePreferences,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook to use the notification context
export const useNotification = () => useContext(NotificationContext);