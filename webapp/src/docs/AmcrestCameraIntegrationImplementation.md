# Amcrest Camera Integration Implementation

This document provides a summary of the changes made to implement Amcrest camera integration in the NxtAcre farm management platform.

## Overview

The integration allows users to add Amcrest cameras to the system using only the camera's serial number, username, and password, without requiring the camera to be on the same network as the application. The cameras can be viewed remotely through the Camera Dashboard using P2P (Peer-to-Peer) technology.

## Files Created

1. **Frontend Components**:
   - `webapp/src/components/Camera/CameraForm.tsx`: Form component for camera configuration
   - `webapp/src/components/Camera/CameraViewer.tsx`: Component for displaying camera feeds
   - `webapp/src/components/Camera/README.md`: Documentation for camera components

2. **Backend Utilities**:
   - `webapp/server/utils/encryption.js`: Utility for encrypting and decrypting camera credentials

3. **Pages**:
   - `webapp/src/pages/IoT/CameraDashboard.tsx`: Dashboard for viewing all cameras

4. **Documentation**:
   - `webapp/src/docs/AmcrestCameraIntegration.md`: Research and design document
   - `webapp/src/docs/AmcrestCameraIntegrationImplementation.md`: Implementation summary

5. **Testing**:
   - `webapp/server/scripts/testCameraEncryption.js`: Script to test encryption functionality

## Files Modified

1. **Frontend**:
   - `webapp/src/pages/IoT/IoTDeviceForm.tsx`: Added camera configuration form
   - `webapp/src/pages/IoT/IoTDeviceList.tsx`: Added link to camera dashboard
   - `webapp/src/pages/IoT/index.tsx`: Added route for camera dashboard

2. **Backend**:
   - `webapp/server/controllers/iotController.js`: Added encryption/decryption for camera credentials
   - `webapp/server/.env`: Added ENCRYPTION_KEY environment variable

## Implementation Details

### Camera Configuration

Camera-specific configuration is stored in the `configuration` JSON field of the IoTDevice model:

```json
{
  "cameraType": "amcrest",
  "serialNumber": "ABCD1234EFGH5678",
  "username": "admin",
  "password": "enc:encrypted_password",
  "p2pEnabled": true,
  "streamUrl": "p2p://ABCD1234EFGH5678"
}
```

### Security

Camera credentials are encrypted using AES-256-CBC encryption before being stored in the database. The encryption key is stored in the `.env` file as `ENCRYPTION_KEY`.

### P2P Connection

The P2P connection is established through the amcrestwebview service, which allows direct connection between the client and the camera through the Amcrest cloud service, bypassing the need for port forwarding.

### User Interface

1. **Camera Form**: When adding or editing an IoT device with type "camera", a specialized form is displayed for configuring camera-specific settings.

2. **Camera Dashboard**: A dedicated page for viewing all cameras associated with a farm. The dashboard supports different grid layouts (1x1, 2x2, 3x3) for viewing multiple cameras simultaneously.

## Testing

The implementation includes a test script (`testCameraEncryption.js`) to verify that the encryption functionality works correctly. To run the test:

```bash
cd webapp/server
node scripts/testCameraEncryption.js
```

## Future Enhancements

1. **Camera Recording**: Add functionality to record and store camera footage.
2. **Motion Detection**: Integrate with Amcrest's motion detection API to trigger alerts.
3. **Mobile App Integration**: Extend the integration to the mobile application.
4. **Multi-Camera Views**: Add support for custom camera view layouts.
5. **Camera Groups**: Allow users to organize cameras into groups.

## Conclusion

The Amcrest camera integration provides a seamless way for users to add and view cameras in the NxtAcre platform. By leveraging P2P technology, cameras can be accessed remotely without complex network configuration, enhancing the platform's monitoring capabilities.