import { useState, useEffect, useContext } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Task {
  id: string;
  title: string;
}

const NewTimeEntryForm = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [categories, setCategories] = useState<string[]>([
    'Field Work',
    'Equipment Operation',
    'Maintenance',
    'Administrative',
    'Training',
    'Other'
  ]);
  const [activityTypes, setActivityTypes] = useState<string[]>([
    'Planting',
    'Harvesting',
    'Spraying',
    'Fertilizing',
    'Irrigation',
    'Equipment Repair',
    'Data Entry',
    'Meeting',
    'Other'
  ]);

  // Form state
  const [formData, setFormData] = useState({
    employee_id: '',
    task_id: '',
    start_time: new Date().toISOString().slice(0, 16), // Format: YYYY-MM-DDThh:mm
    end_time: '',
    duration: '',
    break_duration: '0',
    activity_type: '',
    category: '',
    description: '',
    notes: ''
  });

  // Fetch employees
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!currentFarm) return;

      try {
        const response = await axios.get(`${API_URL}/employees?farm_id=${currentFarm.id}`);
        const employeeOptions = response.data.map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name} ${emp.last_name}`
        }));
        setEmployees(employeeOptions);

        // If the user is an employee, pre-select them
        if (user) {
          const userEmployee = response.data.find((emp: any) => emp.email === user.email);
          if (userEmployee) {
            setFormData(prev => ({ ...prev, employee_id: userEmployee.id }));
          }
        }
      } catch (err: any) {
        console.error('Error fetching employees:', err);
        setError('Failed to load employees. Please try again later.');
      }
    };

    fetchEmployees();
  }, [currentFarm, user]);

  // Fetch tasks
  useEffect(() => {
    const fetchTasks = async () => {
      if (!currentFarm) return;

      try {
        const response = await axios.get(`${API_URL}/tasks/farms/${currentFarm.id}?status=in_progress`);
        setTasks(response.data);
      } catch (err: any) {
        console.error('Error fetching tasks:', err);
        // Not setting error here as tasks are optional
      }
    };

    fetchTasks();
  }, [currentFarm]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // If end_time is set, calculate duration
    if (name === 'end_time' && value) {
      const startTime = new Date(formData.start_time);
      const endTime = new Date(value);
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);

      // Subtract break duration
      const breakDuration = parseFloat(formData.break_duration) || 0;
      const netDuration = Math.max(0, durationHours - breakDuration).toFixed(2);

      setFormData(prev => ({ ...prev, duration: netDuration }));
    }

    // If break_duration changes and end_time is set, recalculate duration
    if (name === 'break_duration' && formData.end_time) {
      const startTime = new Date(formData.start_time);
      const endTime = new Date(formData.end_time);
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);

      // Subtract break duration
      const breakDuration = parseFloat(value) || 0;
      const netDuration = Math.max(0, durationHours - breakDuration).toFixed(2);

      setFormData(prev => ({ ...prev, duration: netDuration }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.employee_id) {
      setError('Please select an employee.');
      return;
    }

    if (!formData.start_time) {
      setError('Start time is required.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare data for submission
      const timeEntryData = {
        ...formData,
        // Convert empty strings to null for optional fields
        task_id: formData.task_id || null,
        end_time: formData.end_time || null,
        duration: formData.duration ? parseFloat(formData.duration) : null,
        break_duration: formData.break_duration ? parseFloat(formData.break_duration) : 0,
        activity_type: formData.activity_type || null,
        category: formData.category || null,
        description: formData.description || null,
        notes: formData.notes || null
      };

      // Submit the time entry
      await axios.post(`${API_URL}/time-entries`, timeEntryData);

      // Redirect to time entries list
      navigate('/hr/time-entries');
    } catch (err: any) {
      console.error('Error creating time entry:', err);
      setError('Failed to create time entry. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">New Time Entry</h1>
        <Link
          to="/hr/time-entries"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Employee */}
            <div>
              <label htmlFor="employee_id" className="block text-sm font-medium text-gray-700 mb-1">
                Employee <span className="text-red-500">*</span>
              </label>
              <select
                id="employee_id"
                name="employee_id"
                value={formData.employee_id}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="">Select Employee</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>

            {/* Task (Optional) */}
            <div>
              <label htmlFor="task_id" className="block text-sm font-medium text-gray-700 mb-1">
                Related Task
              </label>
              <select
                id="task_id"
                name="task_id"
                value={formData.task_id}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">None</option>
                {tasks.map(task => (
                  <option key={task.id} value={task.id}>{task.title}</option>
                ))}
              </select>
            </div>

            {/* Start Time */}
            <div>
              <label htmlFor="start_time" className="block text-sm font-medium text-gray-700 mb-1">
                Start Time <span className="text-red-500">*</span>
              </label>
              <input
                type="datetime-local"
                id="start_time"
                name="start_time"
                value={formData.start_time}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* End Time */}
            <div>
              <label htmlFor="end_time" className="block text-sm font-medium text-gray-700 mb-1">
                End Time
              </label>
              <input
                type="datetime-local"
                id="end_time"
                name="end_time"
                value={formData.end_time}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">Leave blank if work is still in progress</p>
            </div>

            {/* Break Duration */}
            <div>
              <label htmlFor="break_duration" className="block text-sm font-medium text-gray-700 mb-1">
                Break Duration (hours)
              </label>
              <input
                type="number"
                id="break_duration"
                name="break_duration"
                value={formData.break_duration}
                onChange={handleChange}
                min="0"
                step="0.25"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Duration (calculated or manual) */}
            <div>
              <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                Duration (hours)
              </label>
              <input
                type="number"
                id="duration"
                name="duration"
                value={formData.duration}
                onChange={handleChange}
                min="0"
                step="0.25"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">Auto-calculated if end time is provided</p>
            </div>

            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Activity Type */}
            <div>
              <label htmlFor="activity_type" className="block text-sm font-medium text-gray-700 mb-1">
                Activity Type
              </label>
              <select
                id="activity_type"
                name="activity_type"
                value={formData.activity_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select Activity Type</option>
                {activityTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Description */}
            <div className="sm:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Describe the work performed"
              ></textarea>
            </div>

            {/* Notes */}
            <div className="sm:col-span-2">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={2}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Any additional notes"
              ></textarea>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <Link
              to="/hr/time-entries"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-3"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {loading ? 'Saving...' : 'Save Time Entry'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default NewTimeEntryForm;
