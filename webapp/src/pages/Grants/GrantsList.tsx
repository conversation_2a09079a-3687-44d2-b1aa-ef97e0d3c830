import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FarmContext } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { 
  fetchGrantsGovGrants, 
  fetchUSDAGrants, 
  fetchFarmersGovGrants,
  fetchFSAGrants,
  fetchAllGrants,
  searchGrants as searchGrantsApi,
  Grant, 
  USDAGrant,
  FarmersGovGrant,
  FSAGrant,
  AnyGrant
} from '../../services/grantsService';

const GrantsList: React.FC = () => {
  const [allGrants, setAllGrants] = useState<AnyGrant[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedSource, setSelectedSource] = useState<'all' | 'grants-gov' | 'usda' | 'farmers-gov' | 'fsa' | 'rural-development' | 'nrcs' | 'nifa' | 'rma' | 'ams' | 'data-gov'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Categories for filtering
  const categories = [
    'all',
    'agriculture',
    'conservation',
    'rural development',
    'research',
    'education',
    'small business',
    'energy',
    'environment',
    'organic',
    'beginning farmers',
    'disaster assistance',
    'farm loans',
    'price support'
  ];

  useEffect(() => {
    const fetchGrants = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch grants from all sources
        const grantsData = await fetchAllGrants('agriculture', 50);
        setAllGrants(grantsData);
      } catch (err) {
        console.error('Error fetching grants:', err);
        setError('Failed to fetch grants. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchGrants();
  }, []);

  // Filter grants based on search term, source, and category
  const filteredGrants = () => {
    let filtered = [...allGrants];

    // Filter by source if not 'all'
    if (selectedSource !== 'all') {
      filtered = filtered.filter(grant => {
        if (selectedSource === 'grants-gov' && 'opportunityNumber' in grant) {
          return true;
        } else if (selectedSource === 'usda' && 'details' in grant && !('agency' in grant && grant.agency === 'Farm Service Agency')) {
          return true;
        } else if (selectedSource === 'farmers-gov' && grant.id.startsWith('farmers-')) {
          return true;
        } else if (selectedSource === 'fsa' && 'agency' in grant && grant.agency === 'Farm Service Agency') {
          return true;
        } else if (selectedSource === 'rural-development' && 'agency' in grant && grant.agency === 'USDA Rural Development') {
          return true;
        } else if (selectedSource === 'nrcs' && 'agency' in grant && grant.agency === 'Natural Resources Conservation Service') {
          return true;
        } else if (selectedSource === 'nifa' && 'agency' in grant && grant.agency === 'National Institute of Food and Agriculture') {
          return true;
        } else if (selectedSource === 'rma' && 'agency' in grant && grant.agency === 'Risk Management Agency') {
          return true;
        } else if (selectedSource === 'ams' && 'agency' in grant && grant.agency === 'Agricultural Marketing Service') {
          return true;
        } else if (selectedSource === 'data-gov' && 'source' in grant && grant.source === 'data.gov') {
          return true;
        }
        return false;
      });
    }

    // Filter by category if not 'all'
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(grant => 
        grant.category?.toLowerCase().includes(selectedCategory.toLowerCase())
      );
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(grant => 
        grant.title.toLowerCase().includes(term) || 
        grant.description.toLowerCase().includes(term) ||
        ('agency' in grant && grant.agency.toLowerCase().includes(term)) ||
        (grant.eligibility && grant.eligibility.toLowerCase().includes(term))
      );
    }

    return filtered;
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSourceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedSource(e.target.value as 'all' | 'grants-gov' | 'usda' | 'farmers-gov' | 'fsa' | 'rural-development' | 'nrcs' | 'nifa' | 'rma' | 'ams' | 'data-gov');
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Agricultural Grants & Offers</h1>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                Search Grants
              </label>
              <input
                type="text"
                id="search"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Search by title or description"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>

            <div>
              <label htmlFor="source" className="block text-sm font-medium text-gray-700 mb-1">
                Source
              </label>
              <select
                id="source"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={selectedSource}
                onChange={handleSourceChange}
              >
                <option value="all">All Sources</option>
                <option value="grants-gov">Grants.gov</option>
                <option value="usda">USDA ARMS</option>
                <option value="farmers-gov">Farmers.gov</option>
                <option value="fsa">Farm Service Agency</option>
                <option value="rural-development">USDA Rural Development</option>
                <option value="nrcs">Natural Resources Conservation Service</option>
                <option value="nifa">National Institute of Food and Agriculture</option>
                <option value="rma">Risk Management Agency</option>
                <option value="ams">Agricultural Marketing Service</option>
                <option value="data-gov">Data.gov</option>
              </select>
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={selectedCategory}
                onChange={handleCategoryChange}
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Loading and Error States */}
        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
            <p className="mt-2 text-gray-600">Loading grants...</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Grants List */}
        {!loading && !error && (
          <>
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Available Grants and Offers
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  {filteredGrants().length} results found
                </p>
              </div>

              <ul className="divide-y divide-gray-200">
                {filteredGrants().length > 0 ? (
                  filteredGrants().map((grant) => (
                    <li key={`${grant.id}-${grant.title}`} className="px-4 py-4 sm:px-6 hover:bg-gray-50">
                      <Link 
                        to={`/grants/${grant.id}?source=${
                          'opportunityNumber' in grant ? 'grants-gov' : 
                          ('details' in grant && !('agency' in grant && grant.agency === 'Farm Service Agency')) ? 'usda' :
                          grant.id.startsWith('farmers-') ? 'farmers-gov' :
                          ('agency' in grant && grant.agency === 'Farm Service Agency') ? 'fsa' :
                          ('agency' in grant && grant.agency === 'USDA Rural Development') ? 'rural-development' :
                          ('agency' in grant && grant.agency === 'Natural Resources Conservation Service') ? 'nrcs' :
                          ('agency' in grant && grant.agency === 'National Institute of Food and Agriculture') ? 'nifa' :
                          ('agency' in grant && grant.agency === 'Risk Management Agency') ? 'rma' :
                          ('agency' in grant && grant.agency === 'Agricultural Marketing Service') ? 'ams' :
                          ('source' in grant && grant.source === 'data.gov') ? 'data-gov' : 'usda'
                        }`}
                        className="block"
                      >
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-primary-600 truncate">
                            {grant.title}
                          </p>
                          <div className="ml-2 flex-shrink-0 flex">
                            <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              {'opportunityNumber' in grant ? 'Grants.gov' : 
                              ('details' in grant && !('agency' in grant && grant.agency === 'Farm Service Agency')) ? 'USDA ARMS' :
                              grant.id.startsWith('farmers-') ? 'Farmers.gov' :
                              ('agency' in grant && grant.agency === 'Farm Service Agency') ? 'FSA' :
                              ('agency' in grant && grant.agency === 'USDA Rural Development') ? 'Rural Dev' :
                              ('agency' in grant && grant.agency === 'Natural Resources Conservation Service') ? 'NRCS' :
                              ('agency' in grant && grant.agency === 'National Institute of Food and Agriculture') ? 'NIFA' :
                              ('agency' in grant && grant.agency === 'Risk Management Agency') ? 'RMA' :
                              ('agency' in grant && grant.agency === 'Agricultural Marketing Service') ? 'AMS' :
                              ('source' in grant && grant.source === 'data.gov') ? 'Data.gov' : 'USDA'}
                            </p>
                          </div>
                        </div>
                        <div className="mt-2 sm:flex sm:justify-between">
                          <div className="sm:flex">
                            <p className="flex items-center text-sm text-gray-500">
                              {grant.category || 'Uncategorized'}
                            </p>
                          </div>
                          {'closeDate' in grant && (
                            <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                              <p>
                                Closes: {new Date(grant.closeDate).toLocaleDateString()}
                              </p>
                            </div>
                          )}
                        </div>
                        <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                          {grant.description}
                        </p>
                      </Link>
                    </li>
                  ))
                ) : (
                  <li className="px-4 py-8 sm:px-6 text-center text-gray-500">
                    No grants found matching your criteria. Try adjusting your filters.
                  </li>
                )}
              </ul>
            </div>

            {/* Information Section */}
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">About Agricultural Grants</h3>
              <p className="text-gray-600 mb-4">
                Agricultural grants and funding opportunities are available from various government agencies to support farmers, 
                ranchers, and agricultural businesses. These grants can help with conservation efforts, research, 
                equipment purchases, business development, and more.
              </p>
              <p className="text-gray-600 mb-4">
                The data shown here is sourced from multiple government APIs including Grants.gov, USDA's Agricultural Resource Management Survey (ARMS) Data API, Farmers.gov, Farm Service Agency, 
                USDA Rural Development, Natural Resources Conservation Service (NRCS), National Institute of Food and Agriculture (NIFA), Risk Management Agency (RMA), Agricultural Marketing Service (AMS),
                and Data.gov. For the most up-to-date information and to apply for these grants, please visit the official websites.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
                <a 
                  href="https://www.grants.gov/web/grants/search-grants.html" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit Grants.gov
                </a>
                <a 
                  href="https://www.ers.usda.gov/developer/data-apis/arms-data-api/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit USDA ARMS API
                </a>
                <a 
                  href="https://www.farmers.gov/fund" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit Farmers.gov
                </a>
                <a 
                  href="https://www.fsa.usda.gov/programs-and-services/index" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit Farm Service Agency
                </a>
                <a 
                  href="https://www.rd.usda.gov/programs-services" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit Rural Development
                </a>
                <a 
                  href="https://www.nrcs.usda.gov/programs-initiatives" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit NRCS
                </a>
                <a 
                  href="https://www.nifa.usda.gov/grants" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit NIFA
                </a>
                <a 
                  href="https://www.rma.usda.gov/en/Topics" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit RMA
                </a>
                <a 
                  href="https://www.ams.usda.gov/services/grants" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit AMS
                </a>
                <a 
                  href="https://www.data.gov/food/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Visit Data.gov
                </a>
              </div>
            </div>
          </>
        )}
      </div>
    </Layout>
  );
};

export default GrantsList;
