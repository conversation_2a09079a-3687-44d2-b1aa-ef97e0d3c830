import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation, Link } from 'react-router-dom';
import Layout from '../../components/Layout';
import { 
  getGrantDetails, 
  Grant, 
  USDAGrant, 
  FarmersGovGrant, 
  FSAGrant, 
  RuralDevelopmentGrant,
  NRCSGrant,
  NIFAGrant,
  RMAProgram,
  AMSGrant,
  AnyGrant 
} from '../../services/grantsService';

const GrantDetail: React.FC = () => {
  const { grantId } = useParams<{ grantId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [grant, setGrant] = useState<AnyGrant | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Get the source from the query parameters
  const searchParams = new URLSearchParams(location.search);
  const source = searchParams.get('source') as 'grants-gov' | 'usda' | 'farmers-gov' | 'fsa';

  useEffect(() => {
    const fetchGrantDetails = async () => {
      if (!grantId || !source) {
        setError('Invalid grant ID or source');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const grantData = await getGrantDetails(grantId, source);
        setGrant(grantData);
      } catch (err) {
        console.error('Error fetching grant details:', err);
        setError('Failed to fetch grant details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchGrantDetails();
  }, [grantId, source]);

  // Determine the type of grant
  const isGrantsGov = grant && 'opportunityNumber' in grant;
  const isUSDA = grant && 'details' in grant && !('agency' in grant && grant.agency === 'Farm Service Agency');
  const isFarmersGov = grant && grant.id.startsWith('farmers-');
  const isFSA = grant && 'agency' in grant && grant.agency === 'Farm Service Agency';
  const isRuralDevelopment = grant && 'agency' in grant && grant.agency === 'USDA Rural Development';
  const isNRCS = grant && 'agency' in grant && grant.agency === 'Natural Resources Conservation Service';
  const isNIFA = grant && 'agency' in grant && grant.agency === 'National Institute of Food and Agriculture';
  const isRMA = grant && 'agency' in grant && grant.agency === 'Risk Management Agency';
  const isAMS = grant && 'agency' in grant && grant.agency === 'Agricultural Marketing Service';

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Back button */}
        <div className="mb-6">
          <button
            onClick={() => navigate('/grants')}
            className="flex items-center text-primary-600 hover:text-primary-800"
          >
            <svg className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Grants List
          </button>
        </div>

        {/* Loading and Error States */}
        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
            <p className="mt-2 text-gray-600">Loading grant details...</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Grant Details */}
        {!loading && !error && grant && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h1 className="text-xl font-bold text-gray-900">{grant.title}</h1>
                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                  {isGrantsGov ? 'Grants.gov' : 
                   isUSDA ? 'USDA ARMS' : 
                   isFarmersGov ? 'Farmers.gov' : 
                   isFSA ? 'Farm Service Agency' : 
                   isRuralDevelopment ? 'USDA Rural Development' :
                   isNRCS ? 'Natural Resources Conservation Service' :
                   isNIFA ? 'National Institute of Food and Agriculture' :
                   isRMA ? 'Risk Management Agency' :
                   isAMS ? 'Agricultural Marketing Service' : 'USDA'}
                </span>
              </div>
            </div>

            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                {/* Left column */}
                <div>
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                    <p className="text-gray-600 whitespace-pre-line">{grant.description}</p>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Eligibility</h3>
                    <p className="text-gray-600">{grant.eligibility || 'Not specified'}</p>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Category</h3>
                    <p className="text-gray-600">{grant.category || 'Not specified'}</p>
                  </div>
                </div>

                {/* Right column */}
                <div>
                  {/* Grants.gov specific fields */}
                  {isGrantsGov && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as Grant).agency}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Opportunity Number</h3>
                        <p className="text-gray-600">{(grant as Grant).opportunityNumber}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Funding Amount</h3>
                        <p className="text-gray-600">{(grant as Grant).fundingAmount || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Close Date</h3>
                        <p className="text-gray-600">
                          {(grant as Grant).closeDate 
                            ? new Date((grant as Grant).closeDate).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>
                    </>
                  )}

                  {/* USDA ARMS specific fields */}
                  {isUSDA && (grant as USDAGrant).details && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Additional Details</h3>
                      <div className="text-gray-600">
                        {Object.entries((grant as USDAGrant).details).map(([key, value]) => (
                          <div key={key} className="mb-2">
                            <span className="font-medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Farmers.gov specific fields */}
                  {isFarmersGov && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as FarmersGovGrant).agency}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Funding Amount</h3>
                        <p className="text-gray-600">{(grant as FarmersGovGrant).fundingAmount || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Close Date</h3>
                        <p className="text-gray-600">
                          {(grant as FarmersGovGrant).closeDate 
                            ? new Date((grant as FarmersGovGrant).closeDate).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Created Date</h3>
                        <p className="text-gray-600">
                          {(grant as FarmersGovGrant).createdAt 
                            ? new Date((grant as FarmersGovGrant).createdAt).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>
                    </>
                  )}

                  {/* Farm Service Agency specific fields */}
                  {isFSA && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as FSAGrant).agency}</p>
                      </div>

                      {(grant as FSAGrant).details && (
                        <div className="mb-6">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Additional Details</h3>
                          <div className="text-gray-600">
                            {Object.entries((grant as FSAGrant).details).map(([key, value]) => (
                              <div key={key} className="mb-2">
                                <span className="font-medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Created Date</h3>
                        <p className="text-gray-600">
                          {(grant as FSAGrant).createdAt 
                            ? new Date((grant as FSAGrant).createdAt).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>
                    </>
                  )}

                  {/* Rural Development specific fields */}
                  {isRuralDevelopment && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as RuralDevelopmentGrant).agency}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Funding Amount</h3>
                        <p className="text-gray-600">{(grant as RuralDevelopmentGrant).fundingAmount || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Close Date</h3>
                        <p className="text-gray-600">
                          {(grant as RuralDevelopmentGrant).closeDate 
                            ? new Date((grant as RuralDevelopmentGrant).closeDate).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Program Type</h3>
                        <p className="text-gray-600">{(grant as RuralDevelopmentGrant).programType || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Rural Area Eligibility</h3>
                        <p className="text-gray-600">{(grant as RuralDevelopmentGrant).ruralAreaEligibility ? 'Yes' : 'No'}</p>
                      </div>
                    </>
                  )}

                  {/* NRCS specific fields */}
                  {isNRCS && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as NRCSGrant).agency}</p>
                      </div>

                      {(grant as NRCSGrant).details && (
                        <div className="mb-6">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Additional Details</h3>
                          <div className="text-gray-600">
                            {Object.entries((grant as NRCSGrant).details).map(([key, value]) => (
                              <div key={key} className="mb-2">
                                <span className="font-medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Conservation Focus</h3>
                        <div className="text-gray-600">
                          {(grant as NRCSGrant).conservationFocus && (grant as NRCSGrant).conservationFocus.map((focus, index) => (
                            <div key={index} className="mb-1">
                              • {focus}
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* NIFA specific fields */}
                  {isNIFA && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as NIFAGrant).agency}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Funding Amount</h3>
                        <p className="text-gray-600">{(grant as NIFAGrant).fundingAmount || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Close Date</h3>
                        <p className="text-gray-600">
                          {(grant as NIFAGrant).closeDate 
                            ? new Date((grant as NIFAGrant).closeDate).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Research Focus</h3>
                        <div className="text-gray-600">
                          {(grant as NIFAGrant).researchFocus && (grant as NIFAGrant).researchFocus.map((focus, index) => (
                            <div key={index} className="mb-1">
                              • {focus}
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* RMA specific fields */}
                  {isRMA && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as RMAProgram).agency}</p>
                      </div>

                      {(grant as RMAProgram).details && (
                        <div className="mb-6">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Additional Details</h3>
                          <div className="text-gray-600">
                            {Object.entries((grant as RMAProgram).details).map(([key, value]) => (
                              <div key={key} className="mb-2">
                                <span className="font-medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Risk Management Type</h3>
                        <p className="text-gray-600">{(grant as RMAProgram).riskManagementType || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Crops Covered</h3>
                        <div className="text-gray-600">
                          {(grant as RMAProgram).cropsCovered && (grant as RMAProgram).cropsCovered.map((crop, index) => (
                            <div key={index} className="mb-1">
                              • {crop}
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* AMS specific fields */}
                  {isAMS && (
                    <>
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Agency</h3>
                        <p className="text-gray-600">{(grant as AMSGrant).agency}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Funding Amount</h3>
                        <p className="text-gray-600">{(grant as AMSGrant).fundingAmount || 'Not specified'}</p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Close Date</h3>
                        <p className="text-gray-600">
                          {(grant as AMSGrant).closeDate 
                            ? new Date((grant as AMSGrant).closeDate).toLocaleDateString() 
                            : 'Not specified'}
                        </p>
                      </div>

                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Marketing Focus</h3>
                        <div className="text-gray-600">
                          {(grant as AMSGrant).marketingFocus && (grant as AMSGrant).marketingFocus.map((focus, index) => (
                            <div key={index} className="mb-1">
                              • {focus}
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Apply button */}
              <div className="mt-8 border-t border-gray-200 pt-8">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div className="mb-4 sm:mb-0">
                    <h3 className="text-lg font-medium text-gray-900 mb-1">Interested in this grant?</h3>
                    <p className="text-gray-600">Visit the official website for more information and to apply.</p>
                  </div>
                  <a
                    href={grant.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    View Official Listing
                    <svg className="ml-2 -mr-1 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default GrantDetail;
