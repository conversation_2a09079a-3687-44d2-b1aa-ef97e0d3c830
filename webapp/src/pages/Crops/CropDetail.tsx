import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Crop {
  id: string;
  farm_id: string;
  name: string;
  variety: string;
  season: string;
  year: number;
  status: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

interface CropActivity {
  id: string;
  crop_id: string;
  activity_type: string;
  date: string;
  notes: string;
  created_at: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  unit: string;
  created_at: string;
}

const CropDetail = () => {
  const { cropId } = useParams<{ cropId: string }>();
  const navigate = useNavigate();

  const [crop, setCrop] = useState<Crop | null>(null);
  const [activities, setActivities] = useState<CropActivity[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [farm, setFarm] = useState<{ id: string; name: string } | null>(null);

  // Fetch crop data
  useEffect(() => {
    const fetchCrop = async () => {
      if (!cropId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch crop details
        const cropResponse = await axios.get(`${API_URL}/crops/${cropId}`);
        setCrop(cropResponse.data);

        // Fetch farm details
        const farmResponse = await axios.get(`${API_URL}/farms/${cropResponse.data.farm_id}`);
        setFarm(farmResponse.data);

        // Fetch crop activities
        const activitiesResponse = await axios.get(`${API_URL}/crops/${cropId}/activities`);
        setActivities(Array.isArray(activitiesResponse.data) ? activitiesResponse.data : []);

        // Fetch products associated with this crop
        const productsResponse = await axios.get(`${API_URL}/products/farm/${cropResponse.data.farm_id}`);
        // Filter products that are associated with this crop
        const responseData = Array.isArray(productsResponse.data) ? productsResponse.data : [];
        const cropProducts = responseData.filter((product: any) => 
          product.type === 'crop' && product.source_id === cropId
        );
        setProducts(cropProducts);
      } catch (err: any) {
        console.error('Error fetching crop details:', err);
        setError('Failed to load crop details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCrop();
  }, [cropId]);

  // Handle crop deletion
  const handleDeleteCrop = async () => {
    if (!window.confirm('Are you sure you want to delete this crop?')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/crops/${cropId}`);
      navigate('/crops');
    } catch (err: any) {
      console.error('Error deleting crop:', err);
      setError('Failed to delete crop. Please try again later.');
    }
  };

  // Create a product from this crop
  const handleCreateProduct = async () => {
    try {
      const response = await axios.post(`${API_URL}/products/from-crop/${cropId}`, {
        price: 0, // Default price, can be updated later
        unit: 'unit' // Default unit, can be updated later
      });

      // Add the new product to the products list
      setProducts(Array.isArray(products) ? [...products, response.data] : [response.data]);

      alert('Product created successfully!');
    } catch (err: any) {
      console.error('Error creating product from crop:', err);
      setError('Failed to create product. Please try again later.');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading crop details...</p>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/crops"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Crops
          </Link>
        </div>
      </Layout>
    );
  }

  if (!crop) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Crop not found.</span>
        </div>
        <div className="flex justify-center mt-4">
          <Link
            to="/crops"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Crops
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{crop.name}</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleCreateProduct}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Product
          </button>
          <Link
            to={`/crops/${cropId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit
          </Link>
          <button
            onClick={handleDeleteCrop}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Delete
          </button>
          <Link
            to="/crops"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Crops
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Crop Details</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and information about the crop.</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Farm</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {farm ? farm.name : 'Unknown Farm'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Variety</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {crop.variety || 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Season</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {crop.season ? crop.season.charAt(0).toUpperCase() + crop.season.slice(1) : 'Not specified'}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Year</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {crop.year || 'Not specified'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  crop.status === 'active' ? 'bg-green-100 text-green-800' : 
                  crop.status === 'planned' ? 'bg-blue-100 text-blue-800' : 
                  crop.status === 'harvested' ? 'bg-yellow-100 text-yellow-800' : 
                  'bg-gray-100 text-gray-800'
                }`}>
                  {crop.status ? crop.status.charAt(0).toUpperCase() + crop.status.slice(1) : 'Not specified'}
                </span>
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Notes</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {crop.notes || 'No notes'}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(crop.created_at)}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(crop.updated_at)}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Products Section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Products</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Products created from this crop.</p>
          </div>
          <button
            onClick={handleCreateProduct}
            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Create Product
          </button>
        </div>
        <div className="border-t border-gray-200">
          {!Array.isArray(products) || products.length === 0 ? (
            <div className="px-4 py-5 sm:px-6 text-center">
              <p className="text-sm text-gray-500">No products have been created from this crop yet.</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {Array.isArray(products) && products.map(product => (
                <li key={product.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-primary-600">{product.name}</p>
                      <p className="text-sm text-gray-500">
                        Price: ${product.price.toFixed(2)} / {product.unit || 'unit'}
                      </p>
                      <p className="text-xs text-gray-400">
                        Created: {formatDate(product.created_at)}
                      </p>
                    </div>
                    <Link
                      to={`/products/${product.id}`}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      View Product
                    </Link>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      {/* Activities Section */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">Activities</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">Activities related to this crop.</p>
          </div>
          <Link
            to={`/crops/${cropId}/activities/new`}
            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add Activity
          </Link>
        </div>
        <div className="border-t border-gray-200">
          {!Array.isArray(activities) || activities.length === 0 ? (
            <div className="px-4 py-5 sm:px-6 text-center">
              <p className="text-sm text-gray-500">No activities recorded for this crop yet.</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {Array.isArray(activities) && activities.map(activity => (
                <li key={activity.id} className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-primary-600">
                        {activity.activity_type.charAt(0).toUpperCase() + activity.activity_type.slice(1)}
                      </p>
                      <p className="text-sm text-gray-500">
                        Date: {formatDate(activity.date)}
                      </p>
                      {activity.notes && (
                        <p className="text-sm text-gray-500 mt-1">{activity.notes}</p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/crops/${cropId}/activities/${activity.id}/edit`}
                        className="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => {
                          // Handle activity deletion
                          if (window.confirm('Are you sure you want to delete this activity?')) {
                            axios.delete(`${API_URL}/crops/activities/${activity.id}`)
                              .then(() => {
                                setActivities(Array.isArray(activities) ? activities.filter(a => a.id !== activity.id) : []);
                              })
                              .catch(err => {
                                console.error('Error deleting activity:', err);
                                setError('Failed to delete activity. Please try again later.');
                              });
                          }
                        }}
                        className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default CropDetail;
