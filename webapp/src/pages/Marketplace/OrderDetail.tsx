import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getOrderDetails } from '../../services/marketplaceCustomerService';
import { getDriverLocationTracking, getDeliveryRoute, formatDate, DeliveryTracking, DeliveryRoute } from '../../services/deliveryTrackingService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Separator } from '../../components/ui/Separator';
import { Textarea } from '../../components/ui/Textarea';
import { toast } from 'react-hot-toast';
import { ArrowLeft, MapPin, Truck, Package, Clock, Calendar, MessageCircle, Send, ShoppingCart } from 'lucide-react';

const OrderDetail: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deliveryRoute, setDeliveryRoute] = useState<DeliveryRoute | null>(null);
  const [trackingPoints, setTrackingPoints] = useState<DeliveryTracking[]>([]);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<number>(60); // seconds
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [reordering, setReordering] = useState(false);
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<google.maps.Marker | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load Google Maps API
  useEffect(() => {
    if (!window.google || !window.google.maps) {
      const googleMapsScript = document.createElement('script');
      googleMapsScript.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}&libraries=places`;
      googleMapsScript.async = true;
      googleMapsScript.defer = true;
      googleMapsScript.onload = () => setMapLoaded(true);
      document.head.appendChild(googleMapsScript);

      return () => {
        document.head.removeChild(googleMapsScript);
      };
    } else {
      setMapLoaded(true);
    }
  }, []);

  // Fetch order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId) return;

      try {
        setLoading(true);
        const orderData = await getOrderDetails(orderId);
        setOrder(orderData);

        // If order has a delivery_route_id, fetch the route and tracking data
        if (orderData.delivery_route_id) {
          fetchDeliveryRoute(orderData.delivery_route_id);
          fetchTrackingPoints(orderData.delivery_route_id);
        }

        // Fetch messages for this order
        fetchMessages(orderId);
      } catch (err: any) {
        console.error('Error fetching order details:', err);
        setError(err.response?.data?.error || 'Failed to load order details. Please try again.');

        // If unauthorized, redirect to login
        if (err.response?.status === 401) {
          navigate('/marketplace/login');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId, navigate]);

  // Fetch messages for the order
  const fetchMessages = async (orderId: string) => {
    try {
      setLoadingMessages(true);

      // In a real implementation, this would be an API call
      // For now, we'll use mock data
      setTimeout(() => {
        setMessages([
          {
            id: '1',
            sender: 'farm',
            sender_name: 'Green Valley Farm',
            content: 'Thank you for your order! We\'ve received your request and are processing it.',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
          },
          {
            id: '2',
            sender: 'farm',
            sender_name: 'Green Valley Farm',
            content: 'Your order has been approved and is being prepared for delivery.',
            created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() // 12 hours ago
          },
          {
            id: '3',
            sender: 'customer',
            sender_name: 'You',
            content: 'Great! Do you know approximately what time it will be delivered?',
            created_at: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString() // 10 hours ago
          },
          {
            id: '4',
            sender: 'farm',
            sender_name: 'Green Valley Farm',
            content: 'We plan to deliver between 2-4pm tomorrow. We\'ll send you a notification when the driver is on the way.',
            created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString() // 8 hours ago
          }
        ]);
        setLoadingMessages(false);
      }, 500);
    } catch (err) {
      console.error('Error fetching messages:', err);
      toast.error('Failed to load messages.');
      setLoadingMessages(false);
    }
  };

  // Send a new message
  const sendMessage = async () => {
    if (!newMessage.trim() || !orderId) return;

    try {
      setSendingMessage(true);

      // In a real implementation, this would be an API call
      // For now, we'll simulate sending a message
      setTimeout(() => {
        const newMsg = {
          id: `msg_${Date.now()}`,
          sender: 'customer',
          sender_name: 'You',
          content: newMessage.trim(),
          created_at: new Date().toISOString()
        };

        setMessages(prev => [...prev, newMsg]);
        setNewMessage('');
        setSendingMessage(false);

        // Scroll to bottom of messages
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 500);
    } catch (err) {
      console.error('Error sending message:', err);
      toast.error('Failed to send message. Please try again.');
      setSendingMessage(false);
    }
  };

  // Scroll to bottom of messages when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Reorder the same items
  const handleReorder = async () => {
    if (!order || !order.items || order.items.length === 0) return;

    try {
      setReordering(true);

      // In a real implementation, this would add the items to the cart via API
      // For now, we'll simulate the process
      setTimeout(() => {
        toast.success('Items added to your cart!');
        setReordering(false);
        navigate('/marketplace/cart');
      }, 1000);
    } catch (err) {
      console.error('Error reordering items:', err);
      toast.error('Failed to add items to cart. Please try again.');
      setReordering(false);
    }
  };

  // Fetch delivery route
  const fetchDeliveryRoute = async (routeId: string) => {
    try {
      const routeData = await getDeliveryRoute(routeId);
      setDeliveryRoute(routeData);
    } catch (err) {
      console.error('Error fetching delivery route:', err);
      toast.error('Failed to load delivery route information.');
    }
  };

  // Fetch tracking points
  const fetchTrackingPoints = async (routeId: string) => {
    try {
      const trackingData = await getDriverLocationTracking(routeId, 100);
      setTrackingPoints(trackingData);
      setLastRefreshed(new Date());

      // Initialize or update map if tracking points are available
      if (trackingData.length > 0 && mapLoaded) {
        initializeOrUpdateMap(trackingData);
      }
    } catch (err) {
      console.error('Error fetching tracking points:', err);
      toast.error('Failed to load driver location tracking.');
    }
  };

  // Set up refresh interval for tracking points
  useEffect(() => {
    if (!order?.delivery_route_id || order?.status !== 'in_progress') return;

    const intervalId = setInterval(() => {
      fetchTrackingPoints(order.delivery_route_id);
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [refreshInterval, order]);

  // Initialize or update map when tracking points change
  const initializeOrUpdateMap = (points: DeliveryTracking[]) => {
    if (!mapRef.current || points.length === 0) return;

    const latestPoint = points[0]; // Assuming points are sorted by timestamp desc
    const position = { lat: latestPoint.latitude, lng: latestPoint.longitude };

    if (!googleMapRef.current) {
      // Initialize map
      const map = new window.google.maps.Map(mapRef.current, {
        zoom: 15,
        center: position,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
      });
      googleMapRef.current = map;

      // Add marker for driver location
      const marker = new window.google.maps.Marker({
        position,
        map,
        title: 'Driver Location',
        icon: {
          url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
          scaledSize: new window.google.maps.Size(30, 30),
        },
      });
      markerRef.current = marker;

      // Add delivery address marker if available
      if (order?.delivery_address) {
        const geocoder = new window.google.maps.Geocoder();
        const address = `${order.delivery_address.address}, ${order.delivery_address.city}, ${order.delivery_address.state} ${order.delivery_address.zip_code}`;

        geocoder.geocode({ address }, (results, status) => {
          if (status === 'OK' && results && results[0]) {
            new window.google.maps.Marker({
              position: results[0].geometry.location,
              map,
              title: 'Delivery Address',
              icon: {
                url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
                scaledSize: new window.google.maps.Size(30, 30),
              },
            });

            // Fit bounds to include both markers
            const bounds = new window.google.maps.LatLngBounds();
            bounds.extend(position);
            bounds.extend(results[0].geometry.location);
            map.fitBounds(bounds);
          }
        });
      }
    } else {
      // Update existing marker position
      if (markerRef.current) {
        markerRef.current.setPosition(position);
      }

      // Center map on new position
      googleMapRef.current.panTo(position);
    }
  };

  // Handle refresh interval change
  const handleRefreshIntervalChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRefreshInterval(parseInt(e.target.value));
  };

  // Manual refresh
  const handleManualRefresh = () => {
    if (order?.delivery_route_id) {
      fetchTrackingPoints(order.delivery_route_id);
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'shipped':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <div className="text-red-500 text-xl mb-4">{error}</div>
        <Button onClick={() => navigate('/marketplace/orders')}>Return to Orders</Button>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <div className="text-gray-500 text-xl mb-4">Order not found</div>
        <Button onClick={() => navigate('/marketplace/orders')}>Return to Orders</Button>
      </div>
    );
  }

  const isDelivery = order.fulfillment_method === 'delivery';
  const isInProgress = order.status === 'in_progress' || 
                       (deliveryRoute && deliveryRoute.status === 'in_progress');
  const showTracking = isDelivery && isInProgress && trackingPoints.length > 0;

  return (
    <div>
      <div className="mb-6">
        <Button 
          variant="outline" 
          className="flex items-center gap-1"
          onClick={() => navigate('/marketplace/orders')}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Orders
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Order Details</CardTitle>
              <CardDescription>
                Order #{order.id.substring(0, 8)}...
              </CardDescription>
            </div>
            <Badge className={getStatusBadgeColor(order.status)}>
              {order.status.toUpperCase()}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Order Information</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-500">Date:</span>
                  <span>{formatDate(order.created_at)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-500">Items:</span>
                  <span>{order.items?.length || 0}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Truck className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-500">Fulfillment:</span>
                  <span className="capitalize">{order.fulfillment_method}</span>
                </div>
                {order.pickup_date && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-500">Pickup Date:</span>
                    <span>{formatDate(order.pickup_date)}</span>
                  </div>
                )}
              </div>

              {order.notes && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-1">Notes</h4>
                  <p className="text-sm text-gray-600">{order.notes}</p>
                </div>
              )}
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Farm Information</h3>
              <div className="space-y-2">
                <p className="text-sm">
                  <span className="font-medium">{order.farm?.name || 'Unknown Farm'}</span>
                </p>
                {order.farm?.address && (
                  <p className="text-sm text-gray-600">
                    {order.farm.address}, {order.farm.city}, {order.farm.state} {order.farm.zip_code}
                  </p>
                )}
                {order.farm?.phone && (
                  <p className="text-sm text-gray-600">
                    {order.farm.phone}
                  </p>
                )}
              </div>

              {isDelivery && order.delivery_address && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-1">Delivery Address</h4>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm">{order.delivery_address.address}</p>
                      <p className="text-sm">{order.delivery_address.city}, {order.delivery_address.state} {order.delivery_address.zip_code}</p>
                      {order.delivery_address.delivery_instructions && (
                        <p className="text-sm text-gray-600 mt-1">
                          <span className="font-medium">Instructions:</span> {order.delivery_address.delivery_instructions}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator className="my-6" />

          <h3 className="text-lg font-medium mb-4">Order Items</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {order.items?.map((item: any) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {item.product?.image_url && (
                          <div className="flex-shrink-0 h-10 w-10 mr-4">
                            <img className="h-10 w-10 rounded-full object-cover" src={item.product.image_url} alt={item.product.name} />
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {item.product?.name || 'Unknown Product'}
                          </div>
                          {item.product?.sku && (
                            <div className="text-sm text-gray-500">
                              SKU: {item.product.sku}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${item.price?.toFixed(2) || '0.00'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${((item.price || 0) * (item.quantity || 0)).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50">
                <tr>
                  <td colSpan={3} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                    Subtotal
                  </td>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    ${order.subtotal?.toFixed(2) || '0.00'}
                  </td>
                </tr>
                {order.delivery_fee > 0 && (
                  <tr>
                    <td colSpan={3} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                      Delivery Fee
                    </td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">
                      ${order.delivery_fee?.toFixed(2) || '0.00'}
                    </td>
                  </tr>
                )}
                <tr>
                  <td colSpan={3} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                    Total
                  </td>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    ${order.total?.toFixed(2) || '0.00'}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          {/* Reorder Button */}
          <div className="mt-6">
            <Button 
              onClick={handleReorder}
              disabled={reordering}
              className="flex items-center gap-2"
            >
              {reordering ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Adding to Cart...
                </>
              ) : (
                <>
                  <ShoppingCart className="h-4 w-4" />
                  Reorder These Items
                </>
              )}
            </Button>
          </div>

          {/* Messages Section */}
          <Separator className="my-6" />

          <div>
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <MessageCircle className="h-5 w-5 mr-2 text-gray-500" />
              Messages
            </h3>

            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              {loadingMessages ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : messages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No messages yet.</p>
                </div>
              ) : (
                <div className="space-y-4 max-h-[400px] overflow-y-auto mb-4">
                  {messages.map(message => (
                    <div 
                      key={message.id} 
                      className={`flex ${message.sender === 'customer' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div 
                        className={`max-w-[80%] rounded-lg p-3 ${
                          message.sender === 'customer' 
                            ? 'bg-primary-100 text-primary-900' 
                            : 'bg-white border border-gray-200'
                        }`}
                      >
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium text-sm">{message.sender_name}</span>
                          <span className="text-xs text-gray-500">
                            {new Date(message.created_at).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-sm">{message.content}</p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}

              <div className="flex gap-2">
                <Textarea
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  className="flex-1"
                  disabled={sendingMessage}
                />
                <Button 
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || sendingMessage}
                  className="self-end"
                >
                  {sendingMessage ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {showTracking && (
            <>
              <Separator className="my-6" />

              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Delivery Tracking</h3>
                  <div className="flex items-center space-x-4">
                    <div>
                      <label htmlFor="refresh-interval" className="block text-sm font-medium text-gray-700 mr-2">
                        Refresh every:
                      </label>
                      <select
                        id="refresh-interval"
                        value={refreshInterval}
                        onChange={handleRefreshIntervalChange}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                      >
                        <option value="10">10 seconds</option>
                        <option value="30">30 seconds</option>
                        <option value="60">1 minute</option>
                        <option value="300">5 minutes</option>
                      </select>
                    </div>
                    <Button
                      onClick={handleManualRefresh}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Refresh Now
                    </Button>
                  </div>
                </div>

                <div className="bg-gray-50 p-2 rounded-md mb-4">
                  <p className="text-sm text-gray-500">
                    Last updated: {lastRefreshed.toLocaleTimeString()}
                  </p>
                </div>

                <div className="h-[400px] w-full rounded-md overflow-hidden">
                  <div ref={mapRef} className="h-full w-full">
                    {!mapLoaded && (
                      <div className="flex justify-center items-center h-full">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                        <p className="ml-3 text-gray-500">Loading map...</p>
                      </div>
                    )}
                  </div>
                </div>

                {deliveryRoute && deliveryRoute.driver && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-md">
                    <h4 className="text-sm font-medium mb-2">Driver Information</h4>
                    <p className="text-sm">
                      <span className="font-medium">Name:</span> {deliveryRoute.driver.first_name} {deliveryRoute.driver.last_name}
                    </p>
                    {deliveryRoute.start_time && (
                      <p className="text-sm">
                        <span className="font-medium">Started:</span> {formatDate(deliveryRoute.start_time)}
                      </p>
                    )}
                    {deliveryRoute.description && (
                      <p className="text-sm">
                        <span className="font-medium">Route:</span> {deliveryRoute.description}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderDetail;
