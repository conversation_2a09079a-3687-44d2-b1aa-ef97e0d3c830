import React from 'react';
import { Routes, Route } from 'react-router-dom';
import GlobalMarketplace from './GlobalMarketplace';
import FarmMarketplace from './FarmMarketplace';
import ProductDetail from './ProductDetail';
import CategoryBrowsing from './CategoryBrowsing';
import SearchResults from './SearchResults';
import Checkout from './Checkout';
import CheckoutSuccess from './CheckoutSuccess';
import CustomerProfile from './CustomerProfile';
import OrderHistory from './OrderHistory';
import OrderDetail from './OrderDetail';
import SavedCarts from './SavedCarts';
import Cart from './Cart';
import Login from './Login';
import Register from './Register';

const MarketplaceRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<GlobalMarketplace />} />
      <Route path="farm/:farmId" element={<FarmMarketplace />} />
      <Route path="product/:productId" element={<ProductDetail />} />
      <Route path="categories/:category" element={<CategoryBrowsing />} />
      <Route path="checkout" element={<Checkout />} />
      <Route path="checkout-success" element={<CheckoutSuccess />} />
      <Route path="profile" element={<CustomerProfile />} />
      <Route path="orders" element={<OrderHistory />} />
      <Route path="orders/:orderId" element={<OrderDetail />} />
      <Route path="saved-carts" element={<SavedCarts />} />
      <Route path="cart" element={<Cart />} />
      <Route path="login" element={<Login />} />
      <Route path="register" element={<Register />} />
      {/* Add more marketplace routes here as they are implemented */}
      <Route path="search" element={<SearchResults />} />
    </Routes>
  );
};

export default MarketplaceRoutes;
