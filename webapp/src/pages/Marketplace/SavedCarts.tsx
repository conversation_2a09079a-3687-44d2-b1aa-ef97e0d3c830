import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../../context/CartContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Separator } from '../../components/ui/Separator';
import { formatCurrency } from '../../utils/formatters';
import { toast } from 'react-hot-toast';
import { ShoppingBag, ShoppingCart, Trash2, Clock, Calendar } from 'lucide-react';

const SavedCarts: React.FC = () => {
  const navigate = useNavigate();
  const { carts, fetchAllCarts, loading, error } = useCart();
  const [savedCarts, setSavedCarts] = useState<any[]>([]);
  const [loadingSavedCarts, setLoadingSavedCarts] = useState(true);
  const [processingCartAction, setProcessingCartAction] = useState<string | null>(null);

  useEffect(() => {
    const fetchSavedCarts = async () => {
      try {
        setLoadingSavedCarts(true);
        
        // In a real implementation, this would be an API call to get saved carts
        // For now, we'll use mock data
        setTimeout(() => {
          setSavedCarts([
            {
              id: 'cart_1',
              farm: {
                id: 'farm_1',
                name: 'Green Valley Farm',
                logo_url: null
              },
              created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
              items: [
                {
                  id: 'item_1',
                  product: {
                    id: 'prod_1',
                    name: 'Organic Apples',
                    price: 4.99,
                    images: []
                  },
                  quantity: 2
                },
                {
                  id: 'item_2',
                  product: {
                    id: 'prod_2',
                    name: 'Fresh Eggs (Dozen)',
                    price: 6.99,
                    images: []
                  },
                  quantity: 1
                }
              ],
              total_price: 16.97
            },
            {
              id: 'cart_2',
              farm: {
                id: 'farm_2',
                name: 'Sunny Meadows',
                logo_url: null
              },
              created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
              items: [
                {
                  id: 'item_3',
                  product: {
                    id: 'prod_3',
                    name: 'Grass-Fed Ground Beef',
                    price: 8.99,
                    images: []
                  },
                  quantity: 2
                },
                {
                  id: 'item_4',
                  product: {
                    id: 'prod_4',
                    name: 'Organic Carrots',
                    price: 3.49,
                    images: []
                  },
                  quantity: 1
                }
              ],
              total_price: 21.47
            }
          ]);
          setLoadingSavedCarts(false);
        }, 1000);
      } catch (err) {
        console.error('Error fetching saved carts:', err);
        toast.error('Failed to load saved carts. Please try again.');
        setLoadingSavedCarts(false);
      }
    };

    fetchSavedCarts();
  }, []);

  const handleAddToCart = async (cartId: string) => {
    setProcessingCartAction(cartId);
    
    try {
      // In a real implementation, this would be an API call to add the saved cart to the current cart
      // For now, we'll simulate the process
      setTimeout(() => {
        toast.success('Items added to your cart!');
        setProcessingCartAction(null);
        navigate('/marketplace/cart');
      }, 1000);
    } catch (err) {
      console.error('Error adding saved cart to current cart:', err);
      toast.error('Failed to add items to cart. Please try again.');
      setProcessingCartAction(null);
    }
  };

  const handleDeleteSavedCart = async (cartId: string) => {
    setProcessingCartAction(cartId);
    
    try {
      // In a real implementation, this would be an API call to delete the saved cart
      // For now, we'll simulate the process
      setTimeout(() => {
        setSavedCarts(prev => prev.filter(cart => cart.id !== cartId));
        toast.success('Saved cart deleted!');
        setProcessingCartAction(null);
      }, 1000);
    } catch (err) {
      console.error('Error deleting saved cart:', err);
      toast.error('Failed to delete saved cart. Please try again.');
      setProcessingCartAction(null);
    }
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (loadingSavedCarts) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Saved Carts</CardTitle>
          <CardDescription>
            View and manage your saved shopping carts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {savedCarts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ShoppingBag className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>You don't have any saved carts yet.</p>
              <Button 
                className="mt-4" 
                onClick={() => navigate('/marketplace')}
              >
                Start Shopping
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {savedCarts.map(cart => (
                <Card key={cart.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="flex items-center">
                        {cart.farm.logo_url ? (
                          <img 
                            src={cart.farm.logo_url} 
                            alt={cart.farm.name} 
                            className="w-6 h-6 rounded-full mr-2"
                          />
                        ) : (
                          <ShoppingBag className="w-5 h-5 text-gray-500 mr-2" />
                        )}
                        {cart.farm.name}
                      </CardTitle>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>Saved on {formatDate(cart.created_at)}</span>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        {cart.items.map((item: any) => (
                          <div key={item.id} className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-gray-100 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                {item.product.images && item.product.images.length > 0 ? (
                                  <img 
                                    src={item.product.images[0].file_path} 
                                    alt={item.product.name} 
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                    <span className="text-gray-400 text-xs">No image</span>
                                  </div>
                                )}
                              </div>
                              <div>
                                <p className="font-medium">{item.product.name}</p>
                                <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                              </div>
                            </div>
                            <p className="font-medium">{formatCurrency(item.product.price * item.quantity)}</p>
                          </div>
                        ))}
                      </div>
                      
                      <Separator />
                      
                      <div className="flex justify-between items-center">
                        <div className="font-medium">
                          Total: {formatCurrency(cart.total_price)}
                        </div>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="flex items-center gap-1"
                            onClick={() => handleDeleteSavedCart(cart.id)}
                            disabled={processingCartAction === cart.id}
                          >
                            {processingCartAction === cart.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                            Delete
                          </Button>
                          <Button 
                            size="sm" 
                            className="flex items-center gap-1"
                            onClick={() => handleAddToCart(cart.id)}
                            disabled={processingCartAction === cart.id}
                          >
                            {processingCartAction === cart.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <ShoppingCart className="h-4 w-4" />
                            )}
                            Add to Cart
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SavedCarts;