import React, { useState, useEffect } from 'react';
import { 
  addCustomerAddress, 
  updateCustomerAddress, 
  deleteCustomerAddress,
  CustomerAddress,
  MarketplaceCustomer,
  AddressRequest
} from '../../services/marketplaceCustomerService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Label } from '../../components/ui/Label';
import { Textarea } from '../../components/ui/Textarea';
import { Checkbox } from '../../components/ui/Checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../../components/ui/Card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../../components/ui/Dialog';
import { toast } from 'react-hot-toast';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';

interface AddressManagementProps {
  customer: MarketplaceCustomer;
}

const AddressManagement: React.FC<AddressManagementProps> = ({ customer }) => {
  const [addresses, setAddresses] = useState<CustomerAddress[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<CustomerAddress | null>(null);
  const [formData, setFormData] = useState<AddressRequest>({
    farmId: '',
    farmAlias: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    deliveryInstructions: '',
    accessCode: '',
    contactName: '',
    contactPhone: '',
    isDefault: false
  });

  useEffect(() => {
    if (customer && customer.addresses) {
      setAddresses(customer.addresses);
    }
  }, [customer]);

  const resetForm = () => {
    setFormData({
      farmId: customer.farm_id,
      farmAlias: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA',
      deliveryInstructions: '',
      accessCode: '',
      contactName: '',
      contactPhone: '',
      isDefault: false
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, isDefault: e.target.checked }));
  };

  const handleAddAddress = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await addCustomerAddress(formData);
      setAddresses(prev => [...prev, response.address]);
      setIsAddDialogOpen(false);
      resetForm();
      toast.success('Address added successfully');
    } catch (err: any) {
      console.error('Error adding address:', err);
      toast.error(err.response?.data?.error || 'Failed to add address. Please try again.');
    }
  };

  const handleEditAddress = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentAddress) return;

    try {
      const response = await updateCustomerAddress(currentAddress.id, formData);
      setAddresses(prev => prev.map(addr => 
        addr.id === currentAddress.id ? response.address : addr
      ));
      setIsEditDialogOpen(false);
      setCurrentAddress(null);
      toast.success('Address updated successfully');
    } catch (err: any) {
      console.error('Error updating address:', err);
      toast.error(err.response?.data?.error || 'Failed to update address. Please try again.');
    }
  };

  const handleDeleteAddress = async () => {
    if (!currentAddress) return;

    try {
      await deleteCustomerAddress(currentAddress.id);
      setAddresses(prev => prev.filter(addr => addr.id !== currentAddress.id));
      setIsDeleteDialogOpen(false);
      setCurrentAddress(null);
      toast.success('Address deleted successfully');
    } catch (err: any) {
      console.error('Error deleting address:', err);
      toast.error(err.response?.data?.error || 'Failed to delete address. Please try again.');
    }
  };

  const openEditDialog = (address: CustomerAddress) => {
    setCurrentAddress(address);
    setFormData({
      farmId: address.farm_id,
      farmAlias: address.farm_alias || '',
      address: address.address,
      city: address.city,
      state: address.state,
      zipCode: address.zip_code,
      country: address.country,
      deliveryInstructions: address.delivery_instructions || '',
      accessCode: address.access_code || '',
      contactName: address.contact_name || '',
      contactPhone: address.contact_phone || '',
      isDefault: address.is_default
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (address: CustomerAddress) => {
    setCurrentAddress(address);
    setIsDeleteDialogOpen(true);
  };

  const openAddDialog = () => {
    resetForm();
    setIsAddDialogOpen(true);
  };

  const renderAddressForm = (isEdit: boolean, onSubmit: (e: React.FormEvent) => Promise<void>) => (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="farmAlias">Farm Alias (Optional)</Label>
          <Input
            id="farmAlias"
            name="farmAlias"
            value={formData.farmAlias}
            onChange={handleInputChange}
            placeholder="e.g., Home Farm, Main Location"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="contactName">Contact Name (Optional)</Label>
          <Input
            id="contactName"
            name="contactName"
            value={formData.contactName}
            onChange={handleInputChange}
            placeholder="e.g., John Doe"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="address">Street Address*</Label>
        <Input
          id="address"
          name="address"
          value={formData.address}
          onChange={handleInputChange}
          required
          placeholder="123 Main St"
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">City*</Label>
          <Input
            id="city"
            name="city"
            value={formData.city}
            onChange={handleInputChange}
            required
            placeholder="Anytown"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="state">State*</Label>
          <Input
            id="state"
            name="state"
            value={formData.state}
            onChange={handleInputChange}
            required
            placeholder="CA"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="zipCode">ZIP Code*</Label>
          <Input
            id="zipCode"
            name="zipCode"
            value={formData.zipCode}
            onChange={handleInputChange}
            required
            placeholder="12345"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="country">Country</Label>
          <Input
            id="country"
            name="country"
            value={formData.country}
            onChange={handleInputChange}
            placeholder="USA"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="contactPhone">Contact Phone (Optional)</Label>
          <Input
            id="contactPhone"
            name="contactPhone"
            value={formData.contactPhone}
            onChange={handleInputChange}
            placeholder="(*************"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="deliveryInstructions">Delivery Instructions (Optional)</Label>
        <Textarea
          id="deliveryInstructions"
          name="deliveryInstructions"
          value={formData.deliveryInstructions}
          onChange={handleInputChange}
          placeholder="Special instructions for delivery"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="accessCode">Access Code (Optional)</Label>
        <Input
          id="accessCode"
          name="accessCode"
          value={formData.accessCode}
          onChange={handleInputChange}
          placeholder="Gate code, lockbox code, etc."
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox 
          id="isDefault" 
          checked={formData.isDefault} 
          onChange={handleCheckboxChange} 
        />
        <Label htmlFor="isDefault">Set as default address</Label>
      </div>

      <DialogFooter>
        <Button type="submit">{isEdit ? 'Update Address' : 'Add Address'}</Button>
      </DialogFooter>
    </form>
  );

  return (
    <div>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>My Addresses</CardTitle>
              <CardDescription>
                Manage your delivery and pickup addresses
              </CardDescription>
            </div>
            <Button onClick={openAddDialog} className="flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              Add New Address
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {addresses.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>You don't have any saved addresses yet.</p>
              <p>Add an address to make checkout faster.</p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2">
              {addresses.map(address => (
                <Card key={address.id} className={`relative ${address.is_default ? 'border-primary' : ''}`}>
                  {address.is_default && (
                    <div className="absolute top-2 right-2 bg-primary text-white text-xs px-2 py-1 rounded-full">
                      Default
                    </div>
                  )}
                  <CardContent className="pt-6">
                    <div className="space-y-2">
                      {address.farm_alias && (
                        <div className="font-semibold">{address.farm_alias}</div>
                      )}
                      {address.contact_name && (
                        <div>{address.contact_name}</div>
                      )}
                      <div>{address.address}</div>
                      <div>{`${address.city}, ${address.state} ${address.zip_code}`}</div>
                      <div>{address.country}</div>
                      {address.contact_phone && (
                        <div>{address.contact_phone}</div>
                      )}
                      {address.delivery_instructions && (
                        <div className="text-sm text-gray-600 mt-2">
                          <span className="font-medium">Delivery Instructions:</span> {address.delivery_instructions}
                        </div>
                      )}
                      {address.access_code && (
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Access Code:</span> {address.access_code}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => openEditDialog(address)}
                      className="flex items-center gap-1"
                    >
                      <Pencil className="h-4 w-4" />
                      Edit
                    </Button>
                    <Button 
                      variant="destructive" 
                      size="sm" 
                      onClick={() => openDeleteDialog(address)}
                      className="flex items-center gap-1"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Address Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Address</DialogTitle>
            <DialogDescription>
              Add a new delivery or pickup address to your account.
            </DialogDescription>
          </DialogHeader>
          {renderAddressForm(false, handleAddAddress)}
        </DialogContent>
      </Dialog>

      {/* Edit Address Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Address</DialogTitle>
            <DialogDescription>
              Update your address information.
            </DialogDescription>
          </DialogHeader>
          {renderAddressForm(true, handleEditAddress)}
        </DialogContent>
      </Dialog>

      {/* Delete Address Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Address</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this address? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteAddress}>
              Delete Address
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AddressManagement;
