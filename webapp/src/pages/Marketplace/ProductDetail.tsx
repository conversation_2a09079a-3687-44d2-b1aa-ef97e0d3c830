import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Layout from '../../components/Layout';
import { 
  getMarketplaceProduct,
  MarketplaceProduct
} from '../../services/productMarketplaceService';
import { getFarmFulfillmentOptions } from '../../services/fulfillmentOptionsService';
import { Button } from '../../components/ui/Button';
import { Card, CardContent } from '../../components/ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/Tabs';
import { CartProvider } from '../../context/CartContext';
import CartButton from '../../components/cart/CartButton';
import AddToCartButton from '../../components/cart/AddToCartButton';

const ProductDetailContent: React.FC = () => {
  // Get product ID from URL params
  const { productId } = useParams<{ productId: string }>();

  // State for product and loading
  const [product, setProduct] = useState<MarketplaceProduct | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [fulfillmentOptions, setFulfillmentOptions] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);

  // Fetch product details
  useEffect(() => {
    if (!productId) return;

    const fetchProductDetails = async () => {
      setLoading(true);
      setError(null);

      try {
        const productData = await getMarketplaceProduct(productId);
        setProduct(productData);

        // Fetch fulfillment options for the farm
        if (productData && productData.farm_id) {
          const fulfillmentData = await getFarmFulfillmentOptions(productData.farm_id);
          setFulfillmentOptions(fulfillmentData);
        }
      } catch (err) {
        console.error('Error fetching product details:', err);
        setError('Failed to load product details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [productId]);

  // Handle quantity change
  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  // Handle add to cart
  const handleAddToCart = () => {
    // This would be implemented to add the product to the cart
    alert(`Added ${quantity} of ${product?.name} to cart`);
  };

  // Determine if product offers delivery and/or pickup
  const getProductFulfillmentOptions = () => {
    if (!product || !fulfillmentOptions) return { delivery: true, pickup: true };

    const delivery = product.override_farm_fulfillment 
      ? product.offers_delivery 
      : fulfillmentOptions.offers_delivery;

    const pickup = product.override_farm_fulfillment 
      ? product.offers_pickup 
      : fulfillmentOptions.offers_pickup;

    return { delivery, pickup };
  };

  const { delivery, pickup } = getProductFulfillmentOptions();

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          </div>
        ) : product ? (
          <div>
            {/* Header with Breadcrumb and Cart Button */}
            <div className="mb-6 flex justify-between items-center">
              <nav className="flex" aria-label="Breadcrumb">
                <ol className="inline-flex items-center space-x-1 md:space-x-3">
                  <li className="inline-flex items-center">
                    <Link to="/store" className="text-gray-600 hover:text-primary-600">
                      Marketplace
                    </Link>
                  </li>
                  <li>
                    <div className="flex items-center">
                      <span className="mx-2 text-gray-400">/</span>
                      <Link to={`/store/farm/${product.farm_id}`} className="text-gray-600 hover:text-primary-600">
                        {product.farm_name}
                      </Link>
                    </div>
                  </li>
                  <li aria-current="page">
                    <div className="flex items-center">
                      <span className="mx-2 text-gray-400">/</span>
                      <span className="text-gray-500">{product.name}</span>
                    </div>
                  </li>
                </ol>
              </nav>
              <CartButton farmId={product.farm_id} />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Product Images */}
              <div>
                <div className="mb-4 aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200">
                  {product.images && product.images.length > 0 ? (
                    <img
                      src={product.images[selectedImageIndex].file_path}
                      alt={product.name}
                      className="h-96 w-full object-cover object-center"
                    />
                  ) : (
                    <div className="h-96 w-full flex items-center justify-center bg-gray-100">
                      <span className="text-gray-400">No image</span>
                    </div>
                  )}
                </div>

                {/* Thumbnail Gallery */}
                {product.images && product.images.length > 1 && (
                  <div className="grid grid-cols-5 gap-2">
                    {product.images.map((image, index) => (
                      <button
                        key={image.id}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-md ${
                          selectedImageIndex === index ? 'ring-2 ring-primary-500' : ''
                        }`}
                      >
                        <img
                          src={image.file_path}
                          alt={`${product.name} thumbnail ${index + 1}`}
                          className="h-full w-full object-cover object-center"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Details */}
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
                <div className="mt-2">
                  <Link to={`/store/farm/${product.farm_id}`} className="text-primary-600 hover:underline">
                    {product.farm_name}
                  </Link>
                </div>

                <div className="mt-4">
                  <p className="text-3xl font-bold text-gray-900">${product.price.toFixed(2)}</p>
                  {product.unit && (
                    <p className="text-sm text-gray-500">Price per {product.unit}</p>
                  )}
                </div>

                <div className="mt-6">
                  <Tabs defaultValue="description">
                    <TabsList>
                      <TabsTrigger value="description">Description</TabsTrigger>
                      <TabsTrigger value="details">Details</TabsTrigger>
                      <TabsTrigger value="fulfillment">Fulfillment</TabsTrigger>
                    </TabsList>
                    <TabsContent value="description" className="mt-4">
                      <div className="prose max-w-none">
                        <p>{product.marketplace_description || product.description}</p>
                      </div>
                    </TabsContent>
                    <TabsContent value="details" className="mt-4">
                      <div className="space-y-4">
                        {product.category && (
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">Category</h3>
                            <p className="mt-1 text-sm text-gray-500">{product.marketplace_category || product.category}</p>
                          </div>
                        )}
                        {product.sku && (
                          <div>
                            <h3 className="text-sm font-medium text-gray-900">SKU</h3>
                            <p className="mt-1 text-sm text-gray-500">{product.sku}</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                    <TabsContent value="fulfillment" className="mt-4">
                      <div className="space-y-4">
                        <h3 className="text-sm font-medium text-gray-900">Available Fulfillment Options</h3>
                        <div className="flex flex-col space-y-2">
                          {delivery && (
                            <div className="flex items-center">
                              <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                              <span className="ml-2">Delivery Available</span>
                            </div>
                          )}
                          {!delivery && (
                            <div className="flex items-center">
                              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                              </svg>
                              <span className="ml-2">Delivery Not Available</span>
                            </div>
                          )}
                          {pickup && (
                            <div className="flex items-center">
                              <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                              <span className="ml-2">Pickup Available</span>
                            </div>
                          )}
                          {!pickup && (
                            <div className="flex items-center">
                              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                              </svg>
                              <span className="ml-2">Pickup Not Available</span>
                            </div>
                          )}
                        </div>

                        {fulfillmentOptions && delivery && (
                          <div className="mt-4">
                            <h3 className="text-sm font-medium text-gray-900">Delivery Information</h3>
                            <div className="mt-2 space-y-2 text-sm text-gray-500">
                              {fulfillmentOptions.delivery_fee > 0 && (
                                <p>Delivery Fee: ${fulfillmentOptions.delivery_fee.toFixed(2)}</p>
                              )}
                              {fulfillmentOptions.min_order_for_free_delivery && (
                                <p>Free delivery on orders over ${fulfillmentOptions.min_order_for_free_delivery.toFixed(2)}</p>
                              )}
                              {fulfillmentOptions.delivery_radius_miles && (
                                <p>Delivery available within {fulfillmentOptions.delivery_radius_miles} miles</p>
                              )}
                              {fulfillmentOptions.delivery_instructions && (
                                <p>Delivery Instructions: {fulfillmentOptions.delivery_instructions}</p>
                              )}
                            </div>
                          </div>
                        )}

                        {fulfillmentOptions && pickup && fulfillmentOptions.pickup_instructions && (
                          <div className="mt-4">
                            <h3 className="text-sm font-medium text-gray-900">Pickup Information</h3>
                            <p className="mt-2 text-sm text-gray-500">{fulfillmentOptions.pickup_instructions}</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>

                {/* Add to Cart Section */}
                <div className="mt-8">
                  <AddToCartButton 
                    productId={product.id}
                    farmId={product.farm_id}
                    className="w-full"
                    showQuantity={true}
                  />
                </div>

                {/* Farm Information Card */}
                <Card className="mt-8">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500 text-lg font-bold">
                            {product.farm_name?.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">{product.farm_name}</h3>
                        <Link to={`/store/farm/${product.farm_id}`} className="text-primary-600 hover:underline text-sm">
                          View all products from this farm
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">Product not found</h2>
            <p className="text-gray-600">
              The product you're looking for doesn't exist or has been removed.
            </p>
            <Link to="/store" className="mt-4 inline-block text-primary-600 hover:underline">
              Return to marketplace
            </Link>
          </div>
        )}
      </div>
    </Layout>
  );
};

const ProductDetail: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();

  return (
    <CartProvider>
      <ProductDetailContent />
    </CartProvider>
  );
};

export default ProductDetail;
