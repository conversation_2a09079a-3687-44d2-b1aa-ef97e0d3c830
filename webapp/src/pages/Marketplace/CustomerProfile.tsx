import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { 
  getCustomerProfile, 
  updateCustomerProfile, 
  MarketplaceCustomer, 
  UpdateProfileRequest 
} from '../../services/marketplaceCustomerService';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/Tabs';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Label } from '../../components/ui/Label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../../components/ui/Card';
import { toast } from 'react-hot-toast';
import AddressManagement from './AddressManagement';
import OrderHistory from './OrderHistory';

const CustomerProfile: React.FC = () => {
  const navigate = useNavigate();
  const [customer, setCustomer] = useState<MarketplaceCustomer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<UpdateProfileRequest>({
    name: '',
    phone: ''
  });

  useEffect(() => {
    const fetchCustomerProfile = async () => {
      try {
        setLoading(true);
        const response = await getCustomerProfile();
        setCustomer(response.customer);
        setFormData({
          name: response.customer.name,
          phone: response.customer.phone || ''
        });
      } catch (err: any) {
        console.error('Error fetching customer profile:', err);
        setError(err.response?.data?.error || 'Failed to load profile. Please try again.');
        
        // If unauthorized, redirect to login
        if (err.response?.status === 401) {
          navigate('/marketplace/login');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCustomerProfile();
  }, [navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await updateCustomerProfile(formData);
      setCustomer(prev => prev ? { ...prev, ...response.customer } : null);
      setEditMode(false);
      toast.success('Profile updated successfully');
    } catch (err: any) {
      console.error('Error updating profile:', err);
      toast.error(err.response?.data?.error || 'Failed to update profile. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <div className="text-red-500 text-xl mb-4">{error}</div>
        <Button onClick={() => navigate('/marketplace')}>Return to Marketplace</Button>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <div className="text-xl mb-4">Please log in to view your profile</div>
        <Button onClick={() => navigate('/marketplace/login')}>Log In</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">My Account</h1>
        <Button variant="outline" onClick={() => navigate('/marketplace')}>
          Back to Marketplace
        </Button>
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="addresses">Addresses</TabsTrigger>
          <TabsTrigger value="orders">Order History</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Manage your personal information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent>
              {editMode ? (
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        value={customer.email}
                        disabled
                        className="bg-gray-100"
                      />
                      <p className="text-sm text-gray-500">Email cannot be changed</p>
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="(*************"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end mt-6 space-x-2">
                    <Button type="button" variant="outline" onClick={() => setEditMode(false)}>
                      Cancel
                    </Button>
                    <Button type="submit">Save Changes</Button>
                  </div>
                </form>
              ) : (
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="font-medium">Name</div>
                    <div>{customer.name}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="font-medium">Email</div>
                    <div>{customer.email}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="font-medium">Phone</div>
                    <div>{customer.phone || 'Not provided'}</div>
                  </div>
                  <div className="flex justify-end mt-6">
                    <Button onClick={() => setEditMode(true)}>Edit Profile</Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addresses">
          <AddressManagement customer={customer} />
        </TabsContent>

        <TabsContent value="orders">
          <OrderHistory />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomerProfile;