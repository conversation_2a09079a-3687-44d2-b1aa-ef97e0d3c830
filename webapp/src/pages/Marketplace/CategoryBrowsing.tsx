import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, Link } from 'react-router-dom';
import Layout from '../../components/Layout';
import { 
  getMarketplaceProducts, 
  getMarketplaceCategories,
  MarketplaceProduct
} from '../../services/productMarketplaceService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '../../components/ui/Card';
import { Pagination } from '../../components/ui/Pagination';
import { Slider } from '../../components/ui/Slider';

const CategoryBrowsing: React.FC = () => {
  // Get category from URL params
  const { category } = useParams<{ category: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // State for products and pagination
  const [products, setProducts] = useState<MarketplaceProduct[]>([]);
  const [totalProducts, setTotalProducts] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for filters
  const [categories, setCategories] = useState<string[]>([]);
  const [subcategories, setSubcategories] = useState<string[]>([]);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  
  // Get query parameters from URL
  useEffect(() => {
    const subcategory = searchParams.get('subcategory') || '';
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const minPrice = parseFloat(searchParams.get('minPrice') || '0');
    const maxPrice = parseFloat(searchParams.get('maxPrice') || '1000');
    
    setSelectedSubcategory(subcategory);
    setSearchQuery(search);
    setCurrentPage(page);
    setPriceRange([minPrice, maxPrice]);
  }, [searchParams]);
  
  // Fetch categories and subcategories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const fetchedCategories = await getMarketplaceCategories();
        setCategories(fetchedCategories);
        
        // For now, we'll simulate subcategories
        // In a real implementation, you would fetch subcategories from the API
        if (category) {
          // Simulate subcategories based on the main category
          const simulatedSubcategories = [
            `${category} - Type A`,
            `${category} - Type B`,
            `${category} - Type C`,
            `${category} - Special`
          ];
          setSubcategories(simulatedSubcategories);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again later.');
      }
    };
    
    fetchCategories();
  }, [category]);
  
  // Fetch products when filters or pagination changes
  useEffect(() => {
    if (!category) return;
    
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Use the category from URL params and subcategory from state
        const result = await getMarketplaceProducts({
          category: category,
          subcategory: selectedSubcategory,
          search: searchQuery,
          minPrice: priceRange[0],
          maxPrice: priceRange[1],
          page: currentPage,
          limit: itemsPerPage
        });
        
        setProducts(result.products);
        setTotalProducts(result.total);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, [category, selectedSubcategory, searchQuery, priceRange, currentPage, itemsPerPage]);
  
  // Update URL when filters change
  const updateFilters = () => {
    const params = new URLSearchParams();
    
    if (selectedSubcategory) params.append('subcategory', selectedSubcategory);
    if (searchQuery) params.append('search', searchQuery);
    if (currentPage > 1) params.append('page', currentPage.toString());
    if (priceRange[0] > 0) params.append('minPrice', priceRange[0].toString());
    if (priceRange[1] < 1000) params.append('maxPrice', priceRange[1].toString());
    
    setSearchParams(params);
  };
  
  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    updateFilters();
  };
  
  // Handle subcategory selection
  const handleSubcategoryChange = (subcategory: string) => {
    setSelectedSubcategory(subcategory);
    setCurrentPage(1); // Reset to first page on subcategory change
    updateFilters();
  };
  
  // Handle price range change
  const handlePriceChange = (values: number[]) => {
    setPriceRange([values[0], values[1]]);
  };
  
  // Handle price range apply
  const handlePriceApply = () => {
    updateFilters();
  };
  
  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateFilters();
  };
  
  // Calculate total pages
  const totalPages = Math.ceil(totalProducts / itemsPerPage);
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link to="/store" className="text-gray-600 hover:text-primary-600">
                  Marketplace
                </Link>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <span className="text-gray-500">{category}</span>
                </div>
              </li>
              {selectedSubcategory && (
                <li aria-current="page">
                  <div className="flex items-center">
                    <span className="mx-2 text-gray-400">/</span>
                    <span className="text-gray-500">{selectedSubcategory}</span>
                  </div>
                </li>
              )}
            </ol>
          </nav>
        </div>
        
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">{category}</h1>
          <div className="flex space-x-2">
            <form onSubmit={handleSearch} className="flex">
              <Input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 mr-2"
              />
              <Button type="submit">Search</Button>
            </form>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="w-full md:w-1/4 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Subcategories Filter */}
                {subcategories.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-2">Subcategories</h3>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <Button
                          variant={selectedSubcategory === '' ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleSubcategoryChange('')}
                          className="w-full justify-start"
                        >
                          All {category}
                        </Button>
                      </div>
                      {subcategories.map((subcategory) => (
                        <div key={subcategory} className="flex items-center">
                          <Button
                            variant={selectedSubcategory === subcategory ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleSubcategoryChange(subcategory)}
                            className="w-full justify-start"
                          >
                            {subcategory}
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Price Range Filter */}
                <div>
                  <h3 className="font-medium mb-2">Price Range</h3>
                  <div className="space-y-4">
                    <Slider
                      defaultValue={[priceRange[0], priceRange[1]]}
                      min={0}
                      max={1000}
                      step={10}
                      onValueChange={handlePriceChange}
                      className="mt-6"
                    />
                    <div className="flex justify-between">
                      <span>${priceRange[0]}</span>
                      <span>${priceRange[1]}</span>
                    </div>
                    <Button onClick={handlePriceApply} size="sm" className="w-full">
                      Apply Price Filter
                    </Button>
                  </div>
                </div>
                
                {/* Other Categories */}
                <div>
                  <h3 className="font-medium mb-2">Browse Other Categories</h3>
                  <div className="space-y-2">
                    {categories
                      .filter(cat => cat !== category)
                      .map((cat) => (
                        <div key={cat} className="flex items-center">
                          <Link 
                            to={`/store/categories/${cat}`}
                            className="text-primary-600 hover:underline block w-full py-1"
                          >
                            {cat}
                          </Link>
                        </div>
                      ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Products Grid */}
          <div className="w-full md:w-3/4">
            {error ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span className="block sm:inline">{error}</span>
              </div>
            ) : loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-12">
                <h2 className="text-xl font-semibold mb-2">No products found</h2>
                <p className="text-gray-600">
                  Try adjusting your filters or search query to find what you're looking for.
                </p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {products.map((product) => (
                    <Card key={product.id} className="h-full flex flex-col">
                      <CardHeader className="pb-2">
                        <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4">
                          {product.images && product.images.length > 0 ? (
                            <img
                              src={product.images[0].file_path}
                              alt={product.name}
                              className="h-48 w-full object-cover object-center"
                            />
                          ) : (
                            <div className="h-48 w-full flex items-center justify-center bg-gray-100">
                              <span className="text-gray-400">No image</span>
                            </div>
                          )}
                        </div>
                        <CardTitle className="text-lg">{product.name}</CardTitle>
                        <p className="text-sm text-gray-500">{product.farm_name}</p>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <p className="text-xl font-bold text-primary-600">${product.price.toFixed(2)}</p>
                        <p className="text-sm text-gray-700 mt-2 line-clamp-3">
                          {product.marketplace_description || product.description}
                        </p>
                      </CardContent>
                      <CardFooter className="pt-2">
                        <Button 
                          className="w-full"
                          onClick={() => window.location.href = `/store/product/${product.id}`}
                        >
                          View Product
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
                
                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CategoryBrowsing;