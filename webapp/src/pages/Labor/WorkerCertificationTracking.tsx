import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { toast } from 'react-hot-toast';
import { 
  createWorkerCertification, 
  getWorkerCertifications, 
  deleteWorkerCertification,
  getSeasonalWorkers,
  uploadCertificationDocument
} from '../../services/laborService';
import Layout from '../../components/Layout';
import { format } from 'date-fns';
import { API_URL } from '../../config';

const WorkerCertificationTracking: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentFarm } = useFarm();
  const [isLoading, setIsLoading] = useState(false);
  const [certifications, setCertifications] = useState<any[]>([]);
  const [workers, setWorkers] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadingFile, setUploadingFile] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get farmId from URL query parameters or currentFarm
  const getFarmId = () => {
    const searchParams = new URLSearchParams(location.search);
    const farmIdParam = searchParams.get('farmId');
    return farmIdParam ? Number(farmIdParam) : currentFarm?.id ? Number(currentFarm.id) : undefined;
  };
  const [formData, setFormData] = useState({
    workerId: '',
    certificationType: '',
    issueDate: format(new Date(), 'yyyy-MM-dd'),
    expirationDate: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd'),
    status: 'active' as 'active' | 'expired' | 'pending',
    documentUrl: '',
    notes: ''
  });

  useEffect(() => {
    const farmId = getFarmId();
    if (farmId) {
      fetchCertifications();
      fetchWorkers();
    }
  }, [currentFarm, location.search]);

  const fetchCertifications = async () => {
    try {
      const farmId = getFarmId();
      if (!farmId) {
        toast.error('Farm ID is required');
        return;
      }

      setIsLoading(true);
      const data = await getWorkerCertifications(farmId);
      setCertifications(data);
    } catch (error) {
      console.error('Error fetching worker certifications:', error);
      toast.error('Failed to fetch worker certifications');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchWorkers = async () => {
    try {
      const farmId = getFarmId();
      if (!farmId) {
        toast.error('Farm ID is required');
        return;
      }

      const data = await getSeasonalWorkers(farmId);
      setWorkers(data);
    } catch (error) {
      console.error('Error fetching workers:', error);
      toast.error('Failed to fetch workers');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'status') {
      // Ensure status is one of the allowed values
      const statusValue = value as 'active' | 'expired' | 'pending';
      setFormData(prev => ({ ...prev, [name]: statusValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleFileUpload = async () => {
    const farmId = getFarmId();
    if (!selectedFile || !farmId) {
      toast.error('Farm ID is required');
      return;
    }

    try {
      setUploadingFile(true);
      const document = await uploadCertificationDocument(farmId, selectedFile);

      // Set the document URL in the form data
      setFormData(prev => ({ ...prev, documentUrl: document.file_path }));

      toast.success('Document uploaded successfully');
      setSelectedFile(null);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      toast.error('Failed to upload document');
    } finally {
      setUploadingFile(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const farmId = getFarmId();
    if (!farmId) {
      toast.error('Please select a farm first');
      return;
    }

    try {
      setIsLoading(true);
      await createWorkerCertification({
        farmId: farmId,
        ...formData,
        workerId: parseInt(formData.workerId)
      });

      toast.success('Worker certification added successfully');
      setFormData({
        workerId: '',
        certificationType: '',
        issueDate: format(new Date(), 'yyyy-MM-dd'),
        expirationDate: format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd'),
        status: 'active' as 'active' | 'expired' | 'pending',
        documentUrl: '',
        notes: ''
      });
      fetchCertifications();
    } catch (error) {
      console.error('Error creating worker certification:', error);
      toast.error('Failed to add worker certification');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this worker certification?')) {
      try {
        setIsLoading(true);
        await deleteWorkerCertification(id);
        toast.success('Worker certification deleted successfully');
        fetchCertifications();
      } catch (error) {
        console.error('Error deleting worker certification:', error);
        toast.error('Failed to delete worker certification');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusLabel = (status: string) => {
    const statuses: Record<string, string> = {
      'active': 'Active',
      'expired': 'Expired',
      'pending': 'Pending'
    };
    return statuses[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'active': 'bg-green-100 text-green-800',
      'expired': 'bg-red-100 text-red-800',
      'pending': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const isExpired = (expirationDate: string) => {
    return new Date(expirationDate) < new Date();
  };

  const getCertificationTypeOptions = () => {
    return [
      { value: 'pesticide-applicator', label: 'Pesticide Applicator License' },
      { value: 'cdl', label: 'Commercial Driver\'s License (CDL)' },
      { value: 'equipment-operator', label: 'Equipment Operator Certification' },
      { value: 'food-safety', label: 'Food Safety Training' },
      { value: 'first-aid', label: 'First Aid & CPR' },
      { value: 'osha', label: 'OSHA Safety Training' },
      { value: 'forklift', label: 'Forklift Operator' },
      { value: 'organic-handler', label: 'Organic Handler Certification' },
      { value: 'other', label: 'Other' }
    ];
  };

  const getCertificationTypeLabel = (value: string) => {
    const option = getCertificationTypeOptions().find(opt => opt.value === value);
    return option ? option.label : value;
  };

  const getWorkerName = (workerId: number) => {
    const worker = workers.find(w => w.id === workerId);
    return worker ? `${worker.first_name} ${worker.last_name}` : `Worker #${workerId}`;
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Worker Certification Tracking</h1>
        <button
          onClick={() => navigate('/labor')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Add Worker Certification</h2>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="workerId" className="block text-sm font-medium text-gray-700 mb-1">
                Worker
              </label>
              <select
                id="workerId"
                name="workerId"
                value={formData.workerId}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">Select Worker</option>
                {workers.map(worker => (
                  <option key={worker.id} value={worker.id}>
                    {worker.first_name} {worker.last_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="certificationType" className="block text-sm font-medium text-gray-700 mb-1">
                Certification Type
              </label>
              <select
                id="certificationType"
                name="certificationType"
                value={formData.certificationType}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">Select Certification Type</option>
                {getCertificationTypeOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Issue Date
                </label>
                <input
                  type="date"
                  id="issueDate"
                  name="issueDate"
                  value={formData.issueDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Expiration Date
                </label>
                <input
                  type="date"
                  id="expirationDate"
                  name="expirationDate"
                  value={formData.expirationDate}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
            </div>

            <div className="mb-4">
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="active">Active</option>
                <option value="expired">Expired</option>
                <option value="pending">Pending</option>
              </select>
            </div>

            <div className="mb-4">
              <label htmlFor="documentUpload" className="block text-sm font-medium text-gray-700 mb-1">
                Document Upload (Optional)
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  id="documentUpload"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                />
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  Choose File
                </button>
                <button
                  type="button"
                  onClick={handleFileUpload}
                  disabled={!selectedFile || uploadingFile}
                  className={`px-3 py-2 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    !selectedFile || uploadingFile
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-primary-600 text-white hover:bg-primary-700'
                  }`}
                >
                  {uploadingFile ? 'Uploading...' : 'Upload'}
                </button>
                <span className="text-sm text-gray-500">
                  {selectedFile ? selectedFile.name : 'No file selected'}
                </span>
              </div>
              {formData.documentUrl && (
                <div className="mt-2 flex items-center">
                  <span className="text-sm text-green-600 mr-2">✓ Document uploaded</span>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, documentUrl: '' }))}
                    className="text-xs text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>

            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-400"
            >
              {isLoading ? 'Adding...' : 'Add Worker Certification'}
            </button>
          </form>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Worker Certifications</h2>
          {isLoading && <p className="text-gray-500">Loading...</p>}

          {!isLoading && certifications.length === 0 && (
            <p className="text-gray-500">No worker certifications found.</p>
          )}

          {!isLoading && certifications.length > 0 && (
            <div className="space-y-4">
              {certifications.map((certification) => (
                <div key={certification.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {getCertificationTypeLabel(certification.certification_type)}
                      </h3>
                      <p className="text-sm text-gray-500 mb-1">
                        Worker: {getWorkerName(certification.worker_id)}
                      </p>
                      <p className="text-sm text-gray-500 mb-2">
                        Issued: {new Date(certification.issue_date).toLocaleDateString()} • 
                        Expires: {new Date(certification.expiration_date).toLocaleDateString()}
                      </p>
                      <span 
                        className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                          isExpired(certification.expiration_date) 
                            ? 'bg-red-100 text-red-800' 
                            : getStatusColor(certification.status)
                        }`}
                      >
                        {isExpired(certification.expiration_date) ? 'Expired' : getStatusLabel(certification.status)}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      {certification.document_url && (
                        <a
                          href={`${API_URL}/documents/farm/${getFarmId()}/documents/${certification.document_url.split('/').pop()}/download`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-blue-600 hover:text-blue-900"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                          </svg>
                          View Document
                        </a>
                      )}
                      <button
                        onClick={() => handleDelete(certification.id)}
                        className="inline-flex items-center text-red-600 hover:text-red-900"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                      </button>
                    </div>
                  </div>
                  {certification.notes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700">Notes:</p>
                      <p className="text-sm text-gray-600">{certification.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default WorkerCertificationTracking;
