import { useState, useEffect, useContext, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL, MAIN_DOMAIN, GOOGLE_MAPS_API_KEY } from '../../config/index';
import { loadGoogleMapsApi } from '../../utils/googleMapsLoader';
import LocationAutocomplete from '../../components/LocationAutocomplete';
import '../../types/google-maps';

interface FarmFormData {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  taxId: string;
  taxRate: string;
  subdomain: string;
  custom_login_text: string;
  custom_login_logo: string;
  location_data?: {
    latitude: number;
    longitude: number;
  };
}

const FarmForm = () => {
  const { farmId } = useParams<{ farmId: string }>();
  const isEditMode = !!farmId;

  const [formData, setFormData] = useState<FarmFormData>({
    name: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    taxId: '',
    taxRate: '',
    subdomain: '',
    custom_login_text: '',
    custom_login_logo: '',
    location_data: undefined,
  });

  // State for subdomain availability checking
  const [subdomainStatus, setSubdomainStatus] = useState<{
    checking: boolean;
    available: boolean | null;
    message: string;
  }>({
    checking: false,
    available: null,
    message: '',
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoadingMapsApi, setIsLoadingMapsApi] = useState(true);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Load Google Maps API with Places library
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['places']);
        setIsLoadingMapsApi(false);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setIsLoadingMapsApi(false);
      }
    };

    loadMapsApi();
  }, []);

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    // If in edit mode, fetch farm data
    if (isEditMode) {
      const fetchFarm = async () => {
        try {
          setLoading(true);
          setError(null);

          const response = await axios.get(`${API_URL}/farms/${farmId}`);
          const farm = response.data.farm;

          setFormData({
            name: farm.name || '',
            address: farm.address || '',
            city: farm.city || '',
            state: farm.state || '',
            zipCode: farm.zip_code || '',
            country: farm.country || 'USA',
            taxId: farm.tax_id || '',
            taxRate: farm.tax_rate ? farm.tax_rate.toString() : '',
            subdomain: farm.subdomain || '',
            custom_login_text: farm.custom_login_text || '',
            custom_login_logo: farm.custom_login_logo || '',
            location_data: farm.location_data || undefined,
          });

          // Set logo preview if custom_login_logo exists
          if (farm.custom_login_logo) {
            setLogoPreview(farm.custom_login_logo);
          }
        } catch (err: any) {
          console.error('Error fetching farm:', err);
          setError(err.response?.data?.error || 'Failed to load farm details');
        } finally {
          setLoading(false);
        }
      };

      fetchFarm();
    }
  }, [user, navigate, farmId, isEditMode]);

  // Update logo preview when custom_login_logo changes
  useEffect(() => {
    if (formData.custom_login_logo && !logoPreview) {
      setLogoPreview(formData.custom_login_logo);
    }
  }, [formData.custom_login_logo, logoPreview]);

  // Function to generate a subdomain from farm name
  const generateSubdomainFromName = (name: string): string => {
    if (!name) return '';

    // Convert to lowercase, replace spaces with hyphens, and remove special characters
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')
      .replace(/-+/g, '-') // Replace multiple hyphens with a single one
      .replace(/^-|-$/g, ''); // Remove leading and trailing hyphens
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Generate subdomain when farm name changes and subdomain is empty
    if (name === 'name' && value && !formData.subdomain && !isEditMode) {
      const generatedSubdomain = generateSubdomainFromName(value);
      setFormData(prev => ({ ...prev, subdomain: generatedSubdomain }));
      checkSubdomainAvailability(generatedSubdomain, (available) => {
        console.log(`Generated subdomain "${generatedSubdomain}" availability:`, available);
      });
    }

    // Check subdomain availability when the subdomain field changes
    if (name === 'subdomain' && value) {
      checkSubdomainAvailability(value, (available) => {
        console.log(`User entered subdomain "${value}" availability:`, available);
      });
    }
  };

  // Handle address selection from Google Places Autocomplete
  const handleAddressSelect = (address: string, coordinates?: { lat: number; lng: number }, addressComponents?: any) => {
    // If we have address components directly from the autocomplete
    if (addressComponents) {
      const { streetAddress, city, state, zipCode, country } = addressComponents;

      // Use streetAddress if available, otherwise use the full address
      const addressToUse = streetAddress || address;

      // Update form data with extracted components and coordinates
      setFormData(prev => ({
        ...prev,
        address: addressToUse,
        city: city || prev.city,
        state: state || prev.state,
        zipCode: zipCode || prev.zipCode,
        country: country || prev.country,
        location_data: coordinates ? {
          latitude: coordinates.lat,
          longitude: coordinates.lng
        } : prev.location_data
      }));
    }
    // If we have coordinates but no address components, use geocoder as fallback
    else if (coordinates && window.google && window.google.maps) {
      // The geocoder can help us get address components from the coordinates
      const geocoder = new google.maps.Geocoder();

      geocoder.geocode({ location: coordinates }, (results: google.maps.GeocoderResult[], status: google.maps.GeocoderStatus) => {
        if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
          const place = results[0];

          // Extract address components
          let streetNumber = '';
          let street = '';
          let streetAddress = '';
          let city = '';
          let state = '';
          let zipCode = '';
          let country = 'US';

          if (place.address_components) {
            // First pass: extract primary components
            for (const component of place.address_components) {
              const componentType = component.types[0];

              switch (componentType) {
                case 'street_number':
                  streetNumber = component.long_name;
                  break;
                case 'route':
                  street = component.long_name;
                  break;
                case 'locality':
                  city = component.long_name;
                  break;
                case 'administrative_area_level_1':
                  state = component.short_name;
                  break;
                case 'postal_code':
                  zipCode = component.long_name;
                  break;
                case 'country':
                  country = component.short_name;
                  break;
              }
            }

            // Construct street address from street number and street
            if (streetNumber && street) {
              streetAddress = `${streetNumber} ${street}`;
            }

            // Second pass: check for alternative component types if primary ones are missing
            if (!city) {
              const sublocality = place.address_components.find(comp =>
                comp.types.includes('sublocality') || comp.types.includes('sublocality_level_1'));
              if (sublocality) {
                city = sublocality.long_name;
              }
            }

            if (!city) {
              const neighborhood = place.address_components.find(comp => comp.types.includes('neighborhood'));
              if (neighborhood) {
                city = neighborhood.long_name;
              }
            }

            if (!state) {
              const adminArea2 = place.address_components.find(comp =>
                comp.types.includes('administrative_area_level_2'));
              if (adminArea2) {
                state = adminArea2.short_name;
              }
            }
          }

          // Use streetAddress if available, otherwise use the full address
          const addressToUse = streetAddress || address;

          // Update form data with extracted components and coordinates
          setFormData(prev => ({
            ...prev,
            address: addressToUse,
            city: city || prev.city,
            state: state || prev.state,
            zipCode: zipCode || prev.zipCode,
            country: country || prev.country,
            location_data: coordinates ? {
              latitude: coordinates.lat,
              longitude: coordinates.lng
            } : prev.location_data
          }));
        }
      });
    }
  };

  // Function to check subdomain availability
  const checkSubdomainAvailability = async (subdomain: string, callback?: (available: boolean) => void) => {
    // Don't check if subdomain is empty or unchanged in edit mode
    if (!subdomain || (isEditMode && subdomain === formData.subdomain)) {
      setSubdomainStatus({
        checking: false,
        available: null,
        message: '',
      });
      if (callback) callback(false);
      return;
    }

    try {
      setSubdomainStatus({
        checking: true,
        available: null,
        message: 'Checking availability...',
      });

      const response = await axios.get(`${API_URL}/farms/subdomain/${subdomain}/check`);

      setSubdomainStatus({
        checking: false,
        available: response.data.available,
        message: response.data.message,
      });

      if (callback) callback(response.data.available);
    } catch (err: any) {
      console.error('Error checking subdomain availability:', err);
      setSubdomainStatus({
        checking: false,
        available: false,
        message: err.response?.data?.message || 'Error checking subdomain availability',
      });

      if (callback) callback(false);
    }
  };

  // Handle logo file selection
  const handleLogoFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        setError('Please select an image file for the logo');
        return;
      }

      setLogoFile(file);

      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setLogoPreview(previewUrl);
    }
  };

  // Handle logo upload
  const handleLogoUpload = async () => {
    if (!logoFile) {
      setError('Please select a logo file to upload');
      return;
    }

    setUploadingLogo(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('logo', logoFile);

      const response = await axios.post(
        `${API_URL}/farms/${farmId || 'new'}/logo`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      // Update the form data with the new logo URL
      setFormData(prev => ({
        ...prev,
        custom_login_logo: response.data.logoUrl
      }));

      setSuccess('Logo uploaded successfully');
    } catch (err: any) {
      console.error('Error uploading logo:', err);
      setError(err.response?.data?.error || 'Failed to upload logo');
    } finally {
      setUploadingLogo(false);
    }
  };

  // Handle logo URL input change
  const handleLogoUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setFormData(prev => ({ ...prev, custom_login_logo: value }));

    // Update preview if URL is not empty
    if (value) {
      setLogoPreview(value);
    } else {
      setLogoPreview(null);
    }
  };

  // Handle browse button click
  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (isEditMode) {
        // Update existing farm
        await axios.put(`${API_URL}/farms/${farmId}`, {
          ...formData,
          tax_rate: formData.taxRate ? parseFloat(formData.taxRate) : null
        });
        setSuccess('Farm updated successfully');
      } else {
        // Create new farm
        await axios.post(`${API_URL}/farms`, {
          ...formData,
          userId: user?.id,
          tax_rate: formData.taxRate ? parseFloat(formData.taxRate) : null
        });
        setSuccess('Farm created successfully');

        // Reset form after successful creation
        setFormData({
          name: '',
          address: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'US',
          taxId: '',
          taxRate: '',
          subdomain: '',
          custom_login_text: '',
          custom_login_logo: '',
          location_data: undefined,
        });

        // Reset subdomain status
        setSubdomainStatus({
          checking: false,
          available: null,
          message: '',
        });

        // Reset logo preview
        setLogoPreview(null);
        setLogoFile(null);
      }

      // Redirect after a short delay
      setTimeout(() => {
        if (isEditMode) {
          // When editing, redirect back to the farm detail page
          navigate(`/farms/${farmId}`);
        } else {
          // When creating a new farm, redirect to the farms list
          navigate('/farms');
        }
      }, 2000);
    } catch (err: any) {
      console.error('Error saving farm:', err);
      setError(err.response?.data?.error || 'Failed to save farm');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Farm' : 'Add New Farm'}
        </h1>
        <button
          type="button"
          onClick={() => navigate('/farms')}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div className="col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Farm Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="name"
                id="name"
                required
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.name}
                onChange={handleChange}
              />
            </div>

            <div className="col-span-2">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              {!isLoadingMapsApi ? (
                <LocationAutocomplete
                  value={formData.address}
                  onChange={handleAddressSelect}
                  placeholder="Enter farm address"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              ) : (
                <input
                  type="text"
                  name="address"
                  id="address"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.address}
                  onChange={handleChange}
                  placeholder="Loading address autocomplete..."
                />
              )}
              <p className="mt-1 text-xs text-gray-500">
                Start typing to see address suggestions
              </p>
            </div>

            <div>
              <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                City
              </label>
              <input
                type="text"
                name="city"
                id="city"
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.city}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                State
              </label>
              <input
                type="text"
                name="state"
                id="state"
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.state}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">
                ZIP Code
              </label>
              <input
                type="text"
                name="zipCode"
                id="zipCode"
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.zipCode}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                Country
              </label>
              <input
                type="text"
                name="country"
                id="country"
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.country}
                onChange={handleChange}
              />
            </div>

            <div className="col-span-2">
              <label htmlFor="subdomain" className="block text-sm font-medium text-gray-700 mb-1">
                Subdomain {!isEditMode && <span className="text-red-500">*</span>}
              </label>
              <div className="flex items-center">
                <input
                  type="text"
                  name="subdomain"
                  id="subdomain"
                  required={!isEditMode}
                  className={`appearance-none block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${
                    subdomainStatus.available === false ? 'border-red-300 text-red-900' :
                    subdomainStatus.available === true ? 'border-green-300 text-green-900' :
                    'border-gray-300 text-gray-900'
                  }`}
                  value={formData.subdomain}
                  onChange={handleChange}
                  placeholder="your-farm-name"
                  pattern="[a-zA-Z0-9-]+"
                  title="Subdomain can only contain letters, numbers, and hyphens"
                />
                <span className="ml-2 text-sm">.{MAIN_DOMAIN}</span>
              </div>
              {subdomainStatus.checking && (
                <p className="mt-1 text-xs text-gray-500">
                  Checking availability...
                </p>
              )}
              {!subdomainStatus.checking && subdomainStatus.message && (
                <p className={`mt-1 text-xs ${
                  subdomainStatus.available ? 'text-green-600' : 'text-red-600'
                }`}>
                  {subdomainStatus.message}
                </p>
              )}
              {!subdomainStatus.checking && !subdomainStatus.message && (
                <p className="mt-1 text-xs text-gray-500">
                  Choose a subdomain for your farm. This will be used to access your farm's dashboard.
                </p>
              )}
            </div>

            <div className="col-span-2">
              <label htmlFor="taxId" className="block text-sm font-medium text-gray-700 mb-1">
                Tax ID
              </label>
              <input
                type="text"
                name="taxId"
                id="taxId"
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.taxId}
                onChange={handleChange}
                placeholder="e.g. EIN or SSN"
              />
              <p className="mt-1 text-xs text-gray-500">
                For tax reporting purposes. This information is kept secure.
              </p>
            </div>

            <div className="col-span-2">
              <label htmlFor="taxRate" className="block text-sm font-medium text-gray-700 mb-1">
                Tax Rate (%)
              </label>
              <input
                type="number"
                name="taxRate"
                id="taxRate"
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.taxRate}
                onChange={handleChange}
                placeholder="e.g. 7.25"
                step="0.01"
                min="0"
                max="100"
              />
              <p className="mt-1 text-xs text-gray-500">
                Default tax rate for this farm's state. This will be applied to sales unless a product or customer is tax exempt.
              </p>
            </div>

            <div className="col-span-2 border-t pt-4 mt-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Farm Subdomain Login Customization</h3>
              <p className="text-sm text-gray-500 mb-4">
                Customize how your farm's login page appears when users access it via your farm's subdomain.
              </p>
            </div>

            <div className="col-span-2">
              <label htmlFor="custom_login_text" className="block text-sm font-medium text-gray-700 mb-1">
                Custom Login Page Text
              </label>
              <textarea
                name="custom_login_text"
                id="custom_login_text"
                rows={3}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={formData.custom_login_text}
                onChange={(e) => setFormData(prev => ({ ...prev, custom_login_text: e.target.value }))}
                placeholder="Welcome to our farm's portal. Please sign in to access your account."
              />
              <p className="mt-1 text-xs text-gray-500">
                This text will be displayed on the login page when users access your farm via your subdomain.
              </p>
            </div>

            <div className="col-span-2">
              <label htmlFor="custom_login_logo" className="block text-sm font-medium text-gray-700 mb-1">
                Custom Login Page Logo
              </label>

              {/* Logo Preview */}
              {logoPreview && (
                <div className="mb-4 border rounded-md p-4 bg-gray-50">
                  <p className="text-sm font-medium text-gray-700 mb-2">Logo Preview:</p>
                  <img
                    src={logoPreview}
                    alt="Farm Logo Preview"
                    className="max-h-24 max-w-full object-contain border border-gray-200 rounded-md bg-white p-2"
                  />
                </div>
              )}

              {/* URL Input */}
              <div className="mb-4">
                <label htmlFor="custom_login_logo" className="block text-sm font-medium text-gray-700 mb-1">
                  Logo URL
                </label>
                <input
                  type="url"
                  name="custom_login_logo"
                  id="custom_login_logo"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  value={formData.custom_login_logo}
                  onChange={handleLogoUrlChange}
                  placeholder="https://example.com/your-farm-logo.png"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Enter a URL to your farm's logo. This will be displayed on the login page. Recommended size: 200x60 pixels.
                </p>
              </div>

              {/* File Upload */}
              <div className="mb-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Or Upload a Logo Image
                </label>
                <div className="flex items-center">
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handleLogoFileChange}
                  />
                  <button
                    type="button"
                    onClick={handleBrowseClick}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Browse...
                  </button>
                  {logoFile && (
                    <span className="ml-2 text-sm text-gray-600">
                      {logoFile.name}
                    </span>
                  )}
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Upload an image file for your farm's logo. Recommended size: 200x60 pixels.
                </p>
              </div>

              {/* Upload Button */}
              {logoFile && (
                <button
                  type="button"
                  onClick={handleLogoUpload}
                  disabled={uploadingLogo}
                  className={`mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${uploadingLogo ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {uploadingLogo ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Uploading...
                    </>
                  ) : (
                    'Upload Logo'
                  )}
                </button>
              )}
            </div>
          </div>

          <div className="mt-6">
            <button
              type="submit"
              disabled={loading}
              className={`w-full sm:w-auto px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditMode ? 'Update Farm' : 'Create Farm'
              )}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default FarmForm;
