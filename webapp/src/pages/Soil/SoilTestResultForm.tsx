import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link, useLocation } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface SoilTestResult {
  id?: string;
  soil_sample_id: string;
  ph: number | null;
  organic_matter: number | null;
  nitrogen: number | null;
  phosphorus: number | null;
  potassium: number | null;
  calcium: number | null;
  magnesium: number | null;
  sulfur: number | null;
  zinc: number | null;
  manganese: number | null;
  copper: number | null;
  iron: number | null;
  boron: number | null;
  cec: number | null;
  base_saturation: number | null;
  other_results: any;
}

interface SoilSample {
  id: string;
  farm_name: string;
  field_name: string | null;
  sample_date: string;
}

const SoilTestResultForm = () => {
  const { resultId } = useParams<{ resultId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const sampleId = queryParams.get('sampleId');

  const isEditMode = !!resultId;

  const [testResult, setTestResult] = useState<SoilTestResult>({
    soil_sample_id: sampleId || '',
    ph: null,
    organic_matter: null,
    nitrogen: null,
    phosphorus: null,
    potassium: null,
    calcium: null,
    magnesium: null,
    sulfur: null,
    zinc: null,
    manganese: null,
    copper: null,
    iron: null,
    boron: null,
    cec: null,
    base_saturation: null,
    other_results: {}
  });

  const [soilSample, setSoilSample] = useState<SoilSample | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch soil sample data
  useEffect(() => {
    const fetchSoilSample = async () => {
      if (!sampleId && !isEditMode) return;

      try {
        if (isEditMode) {
          // If editing, first get the test result to get the soil_sample_id
          const resultResponse = await axios.get(`${API_URL}/soil/test-results/${resultId}`);
          const sampleId = resultResponse.data.soilTestResult.soil_sample_id;
          setTestResult(resultResponse.data.soilTestResult);

          // Then fetch the soil sample
          const sampleResponse = await axios.get(`${API_URL}/soil/samples/${sampleId}`);
          setSoilSample(sampleResponse.data.soilSample);
        } else if (sampleId) {
          // If creating new test result, fetch the soil sample directly
          const response = await axios.get(`${API_URL}/soil/samples/${sampleId}`);
          setSoilSample(response.data.soilSample);
        }
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      }
    };

    fetchSoilSample();
  }, [sampleId, resultId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTestResult(prev => ({ 
      ...prev, 
      [name]: value === '' ? null : parseFloat(value) 
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate soil_sample_id
      if (!testResult.soil_sample_id) {
        setError('Soil sample ID is required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing test result
        await axios.put(`${API_URL}/soil/test-results/${resultId}`, testResult);
      } else {
        // Create new test result
        await axios.post(`${API_URL}/soil/test-results`, testResult);
      }

      // Redirect to soil sample detail page
      navigate(`/soil/samples/${testResult.soil_sample_id}`);
    } catch (err: any) {
      console.error('Error saving soil test result:', err);
      setError('Failed to save soil test result. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (!sampleId && !isEditMode) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">No soil sample specified. Please select a soil sample first.</span>
        </div>
        <Link
          to="/soil"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Samples
        </Link>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Soil Test Results' : 'Add Soil Test Results'}
        </h1>
        <Link
          to={soilSample ? `/soil/samples/${soilSample.id}` : '/soil'}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Soil Sample
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {soilSample && (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
          <h2 className="text-lg font-medium text-gray-900">Soil Sample Information</h2>
          <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2">
            <div>
              <span className="text-sm font-medium text-gray-500">Farm:</span>
              <span className="ml-2 text-sm text-gray-900">{soilSample.farm_name}</span>
            </div>
            {soilSample.field_name && (
              <div>
                <span className="text-sm font-medium text-gray-500">Field:</span>
                <span className="ml-2 text-sm text-gray-900">{soilSample.field_name}</span>
              </div>
            )}
            <div>
              <span className="text-sm font-medium text-gray-500">Sample Date:</span>
              <span className="ml-2 text-sm text-gray-900">
                {new Date(soilSample.sample_date).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* pH */}
            <div>
              <label htmlFor="ph" className="block text-sm font-medium text-gray-700 mb-1">
                pH
              </label>
              <input
                type="number"
                id="ph"
                name="ph"
                value={testResult.ph === null ? '' : testResult.ph}
                onChange={handleChange}
                step="0.1"
                min="0"
                max="14"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 6.5"
              />
            </div>

            {/* Organic Matter */}
            <div>
              <label htmlFor="organic_matter" className="block text-sm font-medium text-gray-700 mb-1">
                Organic Matter (%)
              </label>
              <input
                type="number"
                id="organic_matter"
                name="organic_matter"
                value={testResult.organic_matter === null ? '' : testResult.organic_matter}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 3.5"
              />
            </div>

            {/* Nitrogen */}
            <div>
              <label htmlFor="nitrogen" className="block text-sm font-medium text-gray-700 mb-1">
                Nitrogen (ppm)
              </label>
              <input
                type="number"
                id="nitrogen"
                name="nitrogen"
                value={testResult.nitrogen === null ? '' : testResult.nitrogen}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 15"
              />
            </div>

            {/* Phosphorus */}
            <div>
              <label htmlFor="phosphorus" className="block text-sm font-medium text-gray-700 mb-1">
                Phosphorus (ppm)
              </label>
              <input
                type="number"
                id="phosphorus"
                name="phosphorus"
                value={testResult.phosphorus === null ? '' : testResult.phosphorus}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 25"
              />
            </div>

            {/* Potassium */}
            <div>
              <label htmlFor="potassium" className="block text-sm font-medium text-gray-700 mb-1">
                Potassium (ppm)
              </label>
              <input
                type="number"
                id="potassium"
                name="potassium"
                value={testResult.potassium === null ? '' : testResult.potassium}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 150"
              />
            </div>

            {/* Calcium */}
            <div>
              <label htmlFor="calcium" className="block text-sm font-medium text-gray-700 mb-1">
                Calcium (ppm)
              </label>
              <input
                type="number"
                id="calcium"
                name="calcium"
                value={testResult.calcium === null ? '' : testResult.calcium}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 1500"
              />
            </div>

            {/* Magnesium */}
            <div>
              <label htmlFor="magnesium" className="block text-sm font-medium text-gray-700 mb-1">
                Magnesium (ppm)
              </label>
              <input
                type="number"
                id="magnesium"
                name="magnesium"
                value={testResult.magnesium === null ? '' : testResult.magnesium}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 200"
              />
            </div>

            {/* Sulfur */}
            <div>
              <label htmlFor="sulfur" className="block text-sm font-medium text-gray-700 mb-1">
                Sulfur (ppm)
              </label>
              <input
                type="number"
                id="sulfur"
                name="sulfur"
                value={testResult.sulfur === null ? '' : testResult.sulfur}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 15"
              />
            </div>

            {/* Zinc */}
            <div>
              <label htmlFor="zinc" className="block text-sm font-medium text-gray-700 mb-1">
                Zinc (ppm)
              </label>
              <input
                type="number"
                id="zinc"
                name="zinc"
                value={testResult.zinc === null ? '' : testResult.zinc}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 2.5"
              />
            </div>

            {/* Manganese */}
            <div>
              <label htmlFor="manganese" className="block text-sm font-medium text-gray-700 mb-1">
                Manganese (ppm)
              </label>
              <input
                type="number"
                id="manganese"
                name="manganese"
                value={testResult.manganese === null ? '' : testResult.manganese}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 10"
              />
            </div>

            {/* Copper */}
            <div>
              <label htmlFor="copper" className="block text-sm font-medium text-gray-700 mb-1">
                Copper (ppm)
              </label>
              <input
                type="number"
                id="copper"
                name="copper"
                value={testResult.copper === null ? '' : testResult.copper}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 1.2"
              />
            </div>

            {/* Iron */}
            <div>
              <label htmlFor="iron" className="block text-sm font-medium text-gray-700 mb-1">
                Iron (ppm)
              </label>
              <input
                type="number"
                id="iron"
                name="iron"
                value={testResult.iron === null ? '' : testResult.iron}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 25"
              />
            </div>

            {/* Boron */}
            <div>
              <label htmlFor="boron" className="block text-sm font-medium text-gray-700 mb-1">
                Boron (ppm)
              </label>
              <input
                type="number"
                id="boron"
                name="boron"
                value={testResult.boron === null ? '' : testResult.boron}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 0.8"
              />
            </div>

            {/* CEC */}
            <div>
              <label htmlFor="cec" className="block text-sm font-medium text-gray-700 mb-1">
                CEC (meq/100g)
              </label>
              <input
                type="number"
                id="cec"
                name="cec"
                value={testResult.cec === null ? '' : testResult.cec}
                onChange={handleChange}
                step="0.1"
                min="0"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 15"
              />
            </div>

            {/* Base Saturation */}
            <div>
              <label htmlFor="base_saturation" className="block text-sm font-medium text-gray-700 mb-1">
                Base Saturation (%)
              </label>
              <input
                type="number"
                id="base_saturation"
                name="base_saturation"
                value={testResult.base_saturation === null ? '' : testResult.base_saturation}
                onChange={handleChange}
                step="0.1"
                min="0"
                max="100"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="e.g., 80"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to={soilSample ? `/soil/samples/${soilSample.id}` : '/soil'}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Test Results'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default SoilTestResultForm;
