import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useFarm } from '../../context/FarmContext';
import { Card, CardContent, CardHeader } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/Tabs';
import { Heading } from '../../components/ui/Heading';
import { Text } from '../../components/ui/Text';
import { Spinner } from '../../components/ui/Spinner';
import { Alert, AlertDescription, AlertTitle } from '../../components/ui/Alert';
import { ChartLineUp, Scales, TrendUp, ShoppingCart } from 'phosphor-react';

const MarketDashboard: React.FC = () => {
  const { currentFarm } = useFarm();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Simulated data - in a real implementation, this would be fetched from the API
  const [marketData, setMarketData] = useState({
    contracts: {
      total: 0,
      active: 0,
      pending: 0,
      completed: 0
    },
    priceComparisons: {
      total: 0,
      recentSavings: 0
    },
    marketTrends: {
      total: 0,
      uptrends: 0,
      downtrends: 0
    },
    marketplace: {
      activeListings: 0,
      views: 0,
      favorites: 0
    }
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        // In a real implementation, these would be actual API calls
        // For now, we'll just simulate some data
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Set simulated data
        setMarketData({
          contracts: {
            total: 12,
            active: 5,
            pending: 3,
            completed: 4
          },
          priceComparisons: {
            total: 8,
            recentSavings: 1250
          },
          marketTrends: {
            total: 15,
            uptrends: 8,
            downtrends: 7
          },
          marketplace: {
            activeListings: 7,
            views: 124,
            favorites: 18
          }
        });
      } catch (err) {
        console.error('Error fetching market data:', err);
        setError('Failed to load market data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentFarm]);

  if (!currentFarm) {
    return (
      <Alert variant="warning">
        <AlertTitle>No Farm Selected</AlertTitle>
        <AlertDescription>
          Please select a farm to view market data.
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Heading level={1}>Market Integration</Heading>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="price-comparison">Price Comparison</TabsTrigger>
          <TabsTrigger value="market-trends">Market Trends</TabsTrigger>
          <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Contracts Card */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <Heading level={3}>Contracts</Heading>
                  <Scales size={24} className="text-primary-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text>Total Contracts</Text>
                    <Text className="font-semibold">{marketData.contracts.total}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Active</Text>
                    <Text className="font-semibold">{marketData.contracts.active}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Pending</Text>
                    <Text className="font-semibold">{marketData.contracts.pending}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Completed</Text>
                    <Text className="font-semibold">{marketData.contracts.completed}</Text>
                  </div>
                </div>
                <div className="mt-4">
                  <Link to="/market/contracts">
                    <Button variant="outline" className="w-full">View Contracts</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Price Comparison Card */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <Heading level={3}>Price Comparison</Heading>
                  <ChartLineUp size={24} className="text-primary-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text>Total Comparisons</Text>
                    <Text className="font-semibold">{marketData.priceComparisons.total}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Recent Savings</Text>
                    <Text className="font-semibold">${marketData.priceComparisons.recentSavings}</Text>
                  </div>
                </div>
                <div className="mt-4">
                  <Link to="/market/price-comparison">
                    <Button variant="outline" className="w-full">Compare Prices</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Market Trends Card */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <Heading level={3}>Market Trends</Heading>
                  <TrendUp size={24} className="text-primary-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text>Total Trends</Text>
                    <Text className="font-semibold">{marketData.marketTrends.total}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Uptrends</Text>
                    <Text className="font-semibold text-green-600">{marketData.marketTrends.uptrends}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Downtrends</Text>
                    <Text className="font-semibold text-red-600">{marketData.marketTrends.downtrends}</Text>
                  </div>
                </div>
                <div className="mt-4">
                  <Link to="/market/trends">
                    <Button variant="outline" className="w-full">View Trends</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Marketplace Card */}
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <Heading level={3}>Marketplace</Heading>
                  <ShoppingCart size={24} className="text-primary-500" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text>Active Listings</Text>
                    <Text className="font-semibold">{marketData.marketplace.activeListings}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Total Views</Text>
                    <Text className="font-semibold">{marketData.marketplace.views}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text>Favorites</Text>
                    <Text className="font-semibold">{marketData.marketplace.favorites}</Text>
                  </div>
                </div>
                <div className="mt-4">
                  <Link to="/market/marketplace">
                    <Button variant="outline" className="w-full">Visit Marketplace</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <Heading level={2}>Market Integration Overview</Heading>
            </CardHeader>
            <CardContent>
              <Text>
                The Market Integration module provides tools to help you manage contracts, compare prices, 
                track market trends, and participate in the agricultural marketplace. Use the tabs above 
                to navigate to specific features.
              </Text>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div>
                  <Heading level={3}>Features</Heading>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Contract Management</li>
                    <li>Price Comparison Tools</li>
                    <li>Market Trend Analysis</li>
                    <li>Marketplace for Buying and Selling</li>
                  </ul>
                </div>
                <div>
                  <Heading level={3}>Benefits</Heading>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Better price visibility</li>
                    <li>Improved contract tracking</li>
                    <li>Data-driven market decisions</li>
                    <li>Expanded market reach</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contracts" className="mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Contracts</Heading>
            </CardHeader>
            <CardContent>
              <Text>
                Manage your sales and purchase contracts in one place. Track contract status, terms, and deadlines.
              </Text>
              <div className="mt-4">
                <Link to="/market/contracts">
                  <Button>Go to Contracts</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="price-comparison" className="mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Price Comparison</Heading>
            </CardHeader>
            <CardContent>
              <Text>
                Compare prices from different suppliers to find the best deals for your farm inputs.
              </Text>
              <div className="mt-4">
                <Link to="/market/price-comparison">
                  <Button>Go to Price Comparison</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="market-trends" className="mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Market Trends</Heading>
            </CardHeader>
            <CardContent>
              <Text>
                Track price trends for agricultural products and commodities to make informed decisions.
              </Text>
              <div className="mt-4">
                <Link to="/market/trends">
                  <Button>Go to Market Trends</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="marketplace" className="mt-6">
          <Card>
            <CardHeader>
              <Heading level={2}>Marketplace</Heading>
            </CardHeader>
            <CardContent>
              <Text>
                Buy and sell agricultural products, equipment, and services in our online marketplace.
              </Text>
              <div className="mt-4">
                <Link to="/market/marketplace">
                  <Button>Go to Marketplace</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MarketDashboard;