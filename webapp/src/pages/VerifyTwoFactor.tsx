import { useState, useContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import { API_URL } from '../config';
import { redirectToFarmSubdomain } from '../utils/redirectUtils';
import { getStorageJSON } from '../utils/storageUtils';

interface TwoFactorMethod {
  method: string;
  configured: boolean;
  enabled: boolean;
  label: string;
}

const VerifyTwoFactor = () => {
  const [token, setToken] = useState('');
  const [userId, setUserId] = useState<string | null>(null);
  const [farmSubdomain, setFarmSubdomain] = useState<string | null>(null);
  const [farmId, setFarmId] = useState<string | null>(null);
  const [availableMethods, setAvailableMethods] = useState<TwoFactorMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [isLoading2FAMethods, setIsLoading2FAMethods] = useState<boolean>(false);
  const [methodsError, setMethodsError] = useState<string | null>(null);
  const [rememberDevice, setRememberDevice] = useState<boolean>(false);
  const { verifyTwoFactor, loading, error, clearError } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Get userId, farmSubdomain, and farmId from location state
    const state = location.state as { 
      userId?: string;
      farmSubdomain?: string;
      farmId?: string;
    } | null;

    if (state && state.userId) {
      setUserId(state.userId);

      // Set farm information if available
      if (state.farmSubdomain) {
        setFarmSubdomain(state.farmSubdomain);
      }

      if (state.farmId) {
        setFarmId(state.farmId);
      }

      // Fetch available 2FA methods
      fetchAvailable2FAMethods(state.userId);
    } else {
      // Redirect to login if no userId is provided
      navigate('/login');
    }
  }, [location, navigate]);

  // Function to fetch available 2FA methods
  const fetchAvailable2FAMethods = async (userId: string) => {
    setIsLoading2FAMethods(true);
    setMethodsError(null);

    try {
      const response = await axios.get(`${API_URL}/auth/available-2fa-methods/${userId}`);
      setAvailableMethods(response.data.methods);

      // Set the selected method to the first configured method
      const configuredMethods = response.data.methods.filter((method: TwoFactorMethod) => method.configured);
      if (configuredMethods.length > 0) {
        setSelectedMethod(configuredMethods[0].method);
      }
    } catch (error) {
      console.error('Error fetching 2FA methods:', error);
      setMethodsError('Failed to load authentication methods. Please try again.');
    } finally {
      setIsLoading2FAMethods(false);
    }
  };

  // Function to send SMS or email 2FA code
  const sendVerificationCode = async (method: string) => {
    if (!userId) return;

    try {
      if (method === 'sms') {
        await axios.post(`${API_URL}/auth/send-sms-2fa-code`, { userId });
      } else if (method === 'email') {
        await axios.post(`${API_URL}/auth/send-email-2fa-code`, { userId });
      }
    } catch (error) {
      console.error(`Error sending ${method} verification code:`, error);
      setMethodsError(`Failed to send verification code. Please try again.`);
    }
  };

  // When the selected method changes, send a verification code if needed
  useEffect(() => {
    if (selectedMethod === 'sms' || selectedMethod === 'email') {
      sendVerificationCode(selectedMethod);
    }
  }, [selectedMethod]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!userId || !selectedMethod) {
      return;
    }

    try {
      // Pass farmSubdomain, farmId, selected method, and rememberDevice to verifyTwoFactor
      const result = await verifyTwoFactor(userId, token, farmSubdomain, farmId, selectedMethod, rememberDevice);

      // Check if we need to redirect to a farm subdomain
      if (result?.farmSubdomain) {
        // Get user from localStorage for redirect logic
        const user = getStorageJSON('user');

        // Use the redirectUtils function to handle the redirect logic
        const redirected = redirectToFarmSubdomain(result.farmSubdomain, '/dashboard', user);

        // If redirect was performed, return early
        if (redirected) {
          return;
        }
      }

      // If no redirection needed, just navigate to dashboard
      navigate('/dashboard');
    } catch (err) {
      // Error is handled by the context
      console.error('2FA verification error:', err);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Two-Factor Authentication
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Verify your identity to continue
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {methodsError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{methodsError}</span>
          </div>
        )}

        {isLoading2FAMethods ? (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {/* 2FA Method Selection */}
            {availableMethods.length > 0 && (
              <div className="space-y-4">
                <label className="block text-sm font-medium text-gray-700">
                  Choose verification method:
                </label>
                <div className="grid grid-cols-1 gap-3">
                  {availableMethods.filter(m => m.configured).map((method) => (
                    <div 
                      key={method.method}
                      className={`border rounded-md p-3 cursor-pointer ${selectedMethod === method.method ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'}`}
                      onClick={() => setSelectedMethod(method.method)}
                    >
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="2fa-method"
                          checked={selectedMethod === method.method}
                          onChange={() => setSelectedMethod(method.method)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <label className="ml-3 block text-sm font-medium text-gray-700">
                          {method.label}
                        </label>
                      </div>
                      <p className="mt-1 text-xs text-gray-500">
                        {method.method === 'app' && 'Use the code from your authenticator app'}
                        {method.method === 'sms' && 'We\'ll send a code to your verified phone number'}
                        {method.method === 'email' && 'We\'ll send a code to your verified email address'}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Verification Code Input */}
            <div className="rounded-md shadow-sm">
              <div>
                <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-1">
                  {selectedMethod === 'app' ? 'Authentication Code' : 'Verification Code'}
                </label>
                <input
                  id="token"
                  name="token"
                  type="text"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder={selectedMethod === 'app' ? 'Enter 6-digit code' : 'Enter verification code'}
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  autoComplete="off"
                  autoFocus
                  maxLength={6}
                  pattern="[0-9]*"
                  inputMode="numeric"
                />
              </div>
            </div>

            {/* Remember Device Checkbox */}
            <div className="flex items-center mt-4">
              <input
                id="remember-device"
                name="remember-device"
                type="checkbox"
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                checked={rememberDevice}
                onChange={(e) => setRememberDevice(e.target.checked)}
              />
              <label htmlFor="remember-device" className="ml-2 block text-sm text-gray-900">
                Remember this device for future logins
              </label>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading || !token || !selectedMethod}
                className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(loading || !token || !selectedMethod) ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {loading ? 'Verifying...' : 'Verify'}
              </button>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600">
                {selectedMethod === 'app' && 'Open your authenticator app to view your authentication code'}
                {selectedMethod === 'sms' && 'Check your phone for the verification code'}
                {selectedMethod === 'email' && 'Check your email for the verification code'}
              </p>
              {(selectedMethod === 'sms' || selectedMethod === 'email') && (
                <button
                  type="button"
                  onClick={() => sendVerificationCode(selectedMethod)}
                  className="mt-2 text-sm text-indigo-600 hover:text-indigo-500"
                >
                  Resend code
                </button>
              )}
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => navigate('/login')}
                  className="text-sm text-gray-600 hover:text-gray-900"
                >
                  ← Go back to login
                </button>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default VerifyTwoFactor;
