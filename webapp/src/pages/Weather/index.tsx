import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import HarvestSchedule from '../../components/HarvestSchedule';
import { format, parseISO, subMonths } from 'date-fns';
import { 
  getFieldWeatherAlerts, 
  getFarmWeatherAlerts, 
  WeatherAlert,
  getFieldHistoricalWeather,
  getFarmHistoricalWeather,
  HistoricalWeatherRecord,
  HistoricalWeatherAnalysis,
  getFieldAllWeatherData,
  getFarmAllWeatherData,
  AllWeatherData
} from '../../services/weatherService';

interface Field {
  id: string;
  name: string;
  location: {
    latitude: number;
    longitude: number;
  };
  size: number;
  size_unit: string;
}

interface WeatherData {
  location: string;
  temperature: number;
  condition: string;
  wind_speed: number;
  wind_direction: string;
  precipitation: number;
  humidity: number;
  timestamp: string;
}

interface ForecastDay {
  date: string;
  temperature_high: number;
  temperature_low: number;
  condition: string;
  wind_speed: number;
  wind_direction: string;
  precipitation_chance: number;
  humidity: number;
}

interface Forecast {
  location: string;
  days: ForecastDay[];
}

interface HourlyForecast {
  time: string;
  temperature: number;
  condition: string;
  wind_speed: number;
  wind_direction: string;
  precipitation_chance: number;
  humidity: number;
}

interface HarvestRecommendation {
  field_id: string;
  field_name: string;
  crop_type: string;
  optimal_days: string[];
  warning_days: string[];
  reason: string;
}

const WeatherPage: React.FC = () => {
  const { selectedFarm } = useFarm();
  const [fields, setFields] = useState<Field[]>([]);
  const [selectedField, setSelectedField] = useState<string>('');
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [forecast, setForecast] = useState<Forecast | null>(null);
  const [hourlyForecast, setHourlyForecast] = useState<HourlyForecast[]>([]);
  const [harvestRecommendations, setHarvestRecommendations] = useState<HarvestRecommendation[]>([]);
  const [weatherAlerts, setWeatherAlerts] = useState<WeatherAlert[]>([]);
  const [alertsLoading, setAlertsLoading] = useState<boolean>(false);
  const [alertsError, setAlertsError] = useState<string | null>(null);
  const [historicalData, setHistoricalData] = useState<HistoricalWeatherRecord[]>([]);
  const [historicalAnalysis, setHistoricalAnalysis] = useState<HistoricalWeatherAnalysis | null>(null);
  const [historicalLoading, setHistoricalLoading] = useState<boolean>(false);
  const [historicalError, setHistoricalError] = useState<string | null>(null);
  const [historicalInterval, setHistoricalInterval] = useState<'daily' | 'weekly' | 'monthly'>('weekly');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch fields when farm changes
  useEffect(() => {
    const fetchFields = async () => {
      if (!selectedFarm) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`/api/fields/farm/${selectedFarm.id}`);
        setFields(response.data);

        // Set the first field as selected by default
        if (response.data.length > 0 && !selectedField) {
          setSelectedField(response.data[0].id);
        }
      } catch (err) {
        console.error('Error fetching fields:', err);
        setError('Failed to load fields. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFields();
  }, [selectedFarm, selectedField]);

  // Fetch all weather data when selected field changes or interval changes
  useEffect(() => {
    const fetchAllWeatherData = async () => {
      if (!selectedField) return;

      setLoading(true);
      setError(null);
      setAlertsLoading(true);
      setAlertsError(null);
      setHistoricalLoading(true);
      setHistoricalError(null);

      try {
        // Calculate date range (last 6 months)
        const endDate = new Date();
        const startDate = subMonths(endDate, 6);

        // Format dates as ISO strings (YYYY-MM-DD)
        const startDateStr = startDate.toISOString().split('T')[0];
        const endDateStr = endDate.toISOString().split('T')[0];

        // Fetch all weather data for the field in a single request
        const allWeatherData = await getFieldAllWeatherData(selectedField, {
          days: 7,
          hours: 48,
          startDate: startDateStr,
          endDate: endDateStr,
          interval: historicalInterval
        });

        // Set current weather data
        setWeatherData({
          location: 'Field Location',
          temperature: allWeatherData.current.temp,
          condition: allWeatherData.current.condition,
          wind_speed: 0, // Default value since it's not in CurrentWeather
          wind_direction: '', // Default value since it's not in CurrentWeather
          precipitation: 0, // Default value since it's not in CurrentWeather
          humidity: 0, // Default value since it's not in CurrentWeather
          timestamp: new Date().toISOString() // Default value since it's not in CurrentWeather
        });

        // Set forecast data
        setForecast({
          location: 'Field Forecast',
          days: allWeatherData.forecast.map(day => ({
            date: day.day, // Using day property instead of date
            temperature_high: day.high,
            temperature_low: day.low,
            condition: day.condition,
            wind_speed: 0, // Default value since it's not in ForecastDay
            wind_direction: '', // Default value since it's not in ForecastDay
            precipitation_chance: 0, // Default value since it's not in ForecastDay
            humidity: 0 // Default value since it's not in ForecastDay
          }))
        });

        // Set hourly forecast data
        if (allWeatherData.hourly && allWeatherData.hourly.length > 0) {
          setHourlyForecast(allWeatherData.hourly.map(hour => ({
            time: hour.time,
            temperature: hour.temp,
            condition: hour.condition,
            wind_speed: typeof hour.wind_speed === 'string' ? parseFloat(hour.wind_speed) : hour.wind_speed,
            wind_direction: hour.wind_direction,
            precipitation_chance: hour.precipitation_chance,
            humidity: hour.humidity
          })));
        } else {
          // Fallback to mock data if hourly data is not available
          generateMockHourlyForecast();
        }

        // Set weather alerts
        setWeatherAlerts(allWeatherData.alerts || []);

        // Set historical weather data
        setHistoricalData(allWeatherData.historical.data || []);
        setHistoricalAnalysis(allWeatherData.historical.analysis || null);
      } catch (err) {
        console.error('Error fetching all weather data:', err);
        setError('Failed to load weather data. Please try again later.');
        setAlertsError('Failed to load weather alerts. Please try again later.');
        setHistoricalError('Failed to load historical weather data. Please try again later.');

        // Try to fetch farm-level data as fallback
        if (selectedFarm) {
          try {
            // Calculate date range (last 6 months)
            const endDate = new Date();
            const startDate = subMonths(endDate, 6);

            // Format dates as ISO strings (YYYY-MM-DD)
            const startDateStr = startDate.toISOString().split('T')[0];
            const endDateStr = endDate.toISOString().split('T')[0];

            const allFarmWeatherData = await getFarmAllWeatherData(selectedFarm.id, {
              days: 7,
              hours: 48,
              startDate: startDateStr,
              endDate: endDateStr,
              interval: historicalInterval
            });

            // Set current weather data
            setWeatherData({
              location: 'Farm Location',
              temperature: allFarmWeatherData.current.temp,
              condition: allFarmWeatherData.current.condition,
              wind_speed: 0, // Default value since it's not in CurrentWeather
              wind_direction: '', // Default value since it's not in CurrentWeather
              precipitation: 0, // Default value since it's not in CurrentWeather
              humidity: 0, // Default value since it's not in CurrentWeather
              timestamp: new Date().toISOString() // Default value since it's not in CurrentWeather
            });

            // Set forecast data
            setForecast({
              location: 'Farm Forecast',
              days: allFarmWeatherData.forecast.map(day => ({
                date: day.day, // Using day property instead of date
                temperature_high: day.high,
                temperature_low: day.low,
                condition: day.condition,
                wind_speed: 0, // Default value since it's not in ForecastDay
                wind_direction: '', // Default value since it's not in ForecastDay
                precipitation_chance: 0, // Default value since it's not in ForecastDay
                humidity: 0 // Default value since it's not in ForecastDay
              }))
            });

            // Set hourly forecast data
            if (allFarmWeatherData.hourly && allFarmWeatherData.hourly.length > 0) {
              setHourlyForecast(allFarmWeatherData.hourly.map(hour => ({
                time: hour.time,
                temperature: hour.temp,
                condition: hour.condition,
                wind_speed: typeof hour.wind_speed === 'string' ? parseFloat(hour.wind_speed) : hour.wind_speed,
                wind_direction: hour.wind_direction,
                precipitation_chance: hour.precipitation_chance,
                humidity: hour.humidity
              })));
            } else {
              generateMockHourlyForecast();
            }

            // Set weather alerts
            setWeatherAlerts(allFarmWeatherData.alerts || []);

            // Set historical weather data
            setHistoricalData(allFarmWeatherData.historical.data || []);
            setHistoricalAnalysis(allFarmWeatherData.historical.analysis || null);
          } catch (fallbackErr) {
            console.error('Error fetching farm weather data:', fallbackErr);
            generateMockHourlyForecast();
          }
        } else {
          generateMockHourlyForecast();
        }
      } finally {
        setLoading(false);
        setAlertsLoading(false);
        setHistoricalLoading(false);
      }
    };

    fetchAllWeatherData();
  }, [selectedField, selectedFarm, historicalInterval]);

  const handleFieldChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedField(e.target.value);
  };

  const handleIntervalChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setHistoricalInterval(e.target.value as 'daily' | 'weekly' | 'monthly');
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return (
          <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        );
      case 'decreasing':
        return (
          <svg className="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        );
      case 'stable':
        return (
          <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' });
  };

  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return '☀️';
      case 'partly cloudy':
        return '⛅';
      case 'cloudy':
        return '☁️';
      case 'rainy':
        return '🌧️';
      case 'stormy':
        return '⛈️';
      default:
        return '🌤️';
    }
  };

  const getConditionColorClass = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return 'text-yellow-500';
      case 'partly cloudy':
        return 'text-blue-300';
      case 'cloudy':
        return 'text-gray-500';
      case 'rainy':
        return 'text-blue-500';
      case 'stormy':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const generateMockHourlyForecast = () => {
    const hourlyData: HourlyForecast[] = [];
    const now = new Date();

    for (let i = 0; i < 24; i++) {
      const forecastTime = new Date(now);
      forecastTime.setHours(now.getHours() + i);

      const conditions = ['Sunny', 'Partly Cloudy', 'Cloudy', 'Rainy', 'Stormy'];
      const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];

      hourlyData.push({
        time: forecastTime.toISOString(),
        temperature: Math.round(15 + Math.random() * 15), // Random temp between 15-30
        condition: randomCondition,
        wind_speed: Math.round(5 + Math.random() * 20), // Random wind speed between 5-25
        wind_direction: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
        precipitation_chance: Math.round(Math.random() * 100), // Random chance between 0-100
        humidity: Math.round(30 + Math.random() * 70) // Random humidity between 30-100
      });
    }

    setHourlyForecast(hourlyData);
  };

  const generateMockHarvestRecommendations = () => {
    const recommendations: HarvestRecommendation[] = [];
    const now = new Date();

    fields.forEach(field => {
      const cropTypes = ['Corn', 'Wheat', 'Soybeans', 'Barley', 'Oats'];
      const randomCrop = cropTypes[Math.floor(Math.random() * cropTypes.length)];

      const optimalDays = [];
      const warningDays = [];

      // Generate some optimal days in the next 10 days
      for (let i = 2; i < 10; i++) {
        if (Math.random() > 0.7) {
          const optimalDate = new Date(now);
          optimalDate.setDate(now.getDate() + i);
          optimalDays.push(optimalDate.toISOString().split('T')[0]);
        }
      }

      // Generate some warning days
      for (let i = 2; i < 10; i++) {
        if (!optimalDays.includes(new Date(now.getTime() + i * 86400000).toISOString().split('T')[0]) && Math.random() > 0.7) {
          const warningDate = new Date(now);
          warningDate.setDate(now.getDate() + i);
          warningDays.push(warningDate.toISOString().split('T')[0]);
        }
      }

      const reasons = [
        'Expected precipitation may affect harvest quality',
        'High winds could make harvesting difficult',
        'Optimal soil moisture and weather conditions',
        'Low humidity will help with crop drying',
        'Forecasted temperature is ideal for harvesting'
      ];

      recommendations.push({
        field_id: field.id,
        field_name: field.name,
        crop_type: randomCrop,
        optimal_days: optimalDays,
        warning_days: warningDays,
        reason: reasons[Math.floor(Math.random() * reasons.length)]
      });
    });

    setHarvestRecommendations(recommendations);
  };

  if (loading && !fields.length) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error && !fields.length) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      </Layout>
    );
  }

  if (!fields.length) {
    return (
      <Layout>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">No fields available. Please add fields to your farm first.</span>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Weather Forecast</h1>

        <div className="mb-6">
          <label htmlFor="field" className="block text-sm font-medium text-gray-700 mb-2">
            Select Field
          </label>
          <select
            id="field"
            value={selectedField}
            onChange={handleFieldChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            {fields.map(field => (
              <option key={field.id} value={field.id}>
                {field.name} ({field.size} {field.size_unit})
              </option>
            ))}
          </select>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : (
          <>
            {weatherData && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                  <div>
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Current Weather
                    </h3>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                      {weatherData.location} - Field-specific weather data
                    </p>
                    <p className="mt-1 max-w-2xl text-xs text-gray-400">
                      Last updated: {new Date(weatherData.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-4xl font-bold">
                      {weatherData.temperature}°F
                    </div>
                    <div className={`text-xl ${getConditionColorClass(weatherData.condition)}`}>
                      {getWeatherIcon(weatherData.condition)} {weatherData.condition}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Feels like: {weatherData.temperature}°F
                    </div>
                  </div>
                </div>
                <div className="border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                    <div className="p-4 border-b md:border-r">
                      <div className="flex items-center">
                        <svg className="h-6 w-6 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905a3.61 3.61 0 01-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                        </svg>
                        <div>
                          <div className="text-sm font-medium text-gray-900">Field Work</div>
                          <div className="text-sm text-gray-500">
                            {weatherData.precipitation > 30 ? (
                              <span className="text-red-600">Not Recommended</span>
                            ) : weatherData.precipitation > 10 ? (
                              <span className="text-yellow-600">Use Caution</span>
                            ) : (
                              <span className="text-green-600">Good Conditions</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border-b md:border-r">
                      <div className="flex items-center">
                        <svg className="h-6 w-6 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                        </svg>
                        <div>
                          <div className="text-sm font-medium text-gray-900">Precipitation</div>
                          <div className="text-sm text-gray-500">
                            {weatherData.precipitation}% chance
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border-b lg:border-r">
                      <div className="flex items-center">
                        <svg className="h-6 w-6 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <div className="text-sm font-medium text-gray-900">Humidity</div>
                          <div className="text-sm text-gray-500">
                            {weatherData.humidity}%
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border-b">
                      <div className="flex items-center">
                        <svg className="h-6 w-6 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <div>
                          <div className="text-sm font-medium text-gray-900">Wind</div>
                          <div className="text-sm text-gray-500">
                            {weatherData.wind_speed} {weatherData.wind_direction}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Weather Alerts Section */}
            {weatherAlerts.length > 0 && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Weather Alerts
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Active weather alerts for this location
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="divide-y divide-gray-200">
                    {weatherAlerts.map((alert, index) => (
                      <div key={index} className={`px-4 py-4 sm:px-6 ${
                        alert.severity === 'extreme' ? 'bg-red-50' :
                        alert.severity === 'severe' ? 'bg-orange-50' :
                        alert.severity === 'moderate' ? 'bg-yellow-50' :
                        'bg-blue-50'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center ${
                              alert.severity === 'extreme' ? 'bg-red-100 text-red-600' :
                              alert.severity === 'severe' ? 'bg-orange-100 text-orange-600' :
                              alert.severity === 'moderate' ? 'bg-yellow-100 text-yellow-600' :
                              'bg-blue-100 text-blue-600'
                            }`}>
                              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                              </svg>
                            </div>
                            <div className="ml-4">
                              <h4 className="text-lg font-medium text-gray-900">{alert.event}</h4>
                              <p className="text-sm text-gray-500">
                                {alert.headline}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-500">
                              {alert.onset ? `From: ${new Date(alert.onset).toLocaleString()}` : ''}
                            </div>
                            <div className="text-sm text-gray-500">
                              {alert.expires ? `Until: ${new Date(alert.expires).toLocaleString()}` : ''}
                            </div>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="text-sm text-gray-700 whitespace-pre-line">
                            {alert.description}
                          </div>
                          {alert.instruction && (
                            <div className="mt-2 p-2 bg-white rounded border border-gray-200">
                              <h5 className="text-sm font-medium text-gray-900">Instructions:</h5>
                              <p className="text-sm text-gray-700 whitespace-pre-line">{alert.instruction}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {alertsLoading && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8 p-4">
                <div className="flex justify-center items-center h-16">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                  <span className="ml-2 text-gray-600">Loading weather alerts...</span>
                </div>
              </div>
            )}

            {hourlyForecast.length > 0 && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Hourly Forecast
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Next 48 hours - Scroll horizontally to see more
                  </p>
                </div>
                <div className="border-t border-gray-200 overflow-x-auto">
                  <div className="flex p-4 space-x-6" style={{ minWidth: 'max-content' }}>
                    {hourlyForecast.map((hour, index) => (
                      <div key={index} className="flex flex-col items-center w-24">
                        <div className="text-sm font-medium text-gray-500">
                          {formatTime(hour.time)}
                        </div>
                        <div className="text-xs text-gray-400">
                          {hour.time ? format(parseISO(hour.time), 'EEE') : ''}
                        </div>
                        <div className={`text-2xl my-2 ${getConditionColorClass(hour.condition)}`}>
                          {getWeatherIcon(hour.condition)}
                        </div>
                        <div className="text-sm font-bold">
                          {hour.temperature}°F
                        </div>
                        <div className="text-xs text-gray-500">
                          Precip: {hour.precipitation_chance}%
                        </div>
                        <div className="text-xs text-gray-500">
                          Wind: {hour.wind_speed} {hour.wind_direction}
                        </div>
                        <div className="text-xs text-gray-500">
                          Humidity: {hour.humidity}%
                        </div>
                        <div className="mt-1 text-xs">
                          {hour.precipitation_chance > 30 ? (
                            <span className="px-2 py-1 rounded-full bg-red-100 text-red-800">Avoid</span>
                          ) : hour.precipitation_chance > 10 ? (
                            <span className="px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">Caution</span>
                          ) : (
                            <span className="px-2 py-1 rounded-full bg-green-100 text-green-800">Good</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {forecast && forecast.days.length > 0 && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    7-Day Forecast
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Extended forecast for planning field operations
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="divide-y divide-gray-200">
                    {forecast.days.map((day, index) => (
                      <div key={index} className="px-4 py-4 sm:px-6">
                        <div className="flex justify-between items-center">
                          <div className="w-1/5">
                            <div className="text-sm font-medium text-gray-900">
                              {day.date ? formatDate(day.date) : 'Unknown Date'}
                            </div>
                            <div className="text-xs text-gray-500">
                              {day.date ? format(parseISO(day.date), 'EEEE') : ''}
                            </div>
                          </div>
                          <div className="w-1/5 flex items-center justify-center">
                            <span className={`text-2xl ${getConditionColorClass(day.condition)}`}>
                              {getWeatherIcon(day.condition)}
                            </span>
                            <span className="ml-2 text-sm text-gray-500">
                              {day.condition}
                            </span>
                          </div>
                          <div className="w-1/5 text-center">
                            <div className="text-sm font-medium text-gray-900">
                              {day.temperature_high}°F / {day.temperature_low}°F
                            </div>
                            <div className="text-xs text-gray-500">
                              Feels like: {Math.round((day.temperature_high + day.temperature_low) / 2)}°F
                            </div>
                          </div>
                          <div className="w-1/5 text-center">
                            <div className="text-sm text-gray-500">
                              Precip: {day.precipitation_chance}%
                            </div>
                            <div className="text-xs text-gray-500">
                              Humidity: {day.humidity || 0}%
                            </div>
                          </div>
                          <div className="w-1/5 text-right">
                            <div className="text-sm text-gray-500">
                              Wind: {day.wind_speed} km/h {day.wind_direction}
                            </div>
                            <div className="text-xs text-gray-500">
                              {day.precipitation_chance > 30 ? 'Not ideal for field work' : 
                               day.precipitation_chance > 10 ? 'Caution for field work' : 
                               'Good for field work'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Historical Weather Analysis Section */}
            <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Historical Weather Analysis
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Weather patterns over the past 6 months for better crop planning
                  </p>
                </div>
                <div>
                  <select
                    id="interval"
                    value={historicalInterval}
                    onChange={handleIntervalChange}
                    className="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>

              {historicalLoading ? (
                <div className="border-t border-gray-200 p-4">
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                  </div>
                </div>
              ) : historicalError ? (
                <div className="border-t border-gray-200 p-4">
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{historicalError}</span>
                  </div>
                </div>
              ) : (
                <>
                  {/* Weather Trends Summary */}
                  {historicalAnalysis && (
                    <div className="border-t border-gray-200">
                      <div className="px-4 py-5 sm:p-6">
                        <h4 className="text-base font-medium text-gray-900 mb-4">Weather Trends</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="px-4 py-5 sm:p-6">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                                  <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                  </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                  <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Temperature</dt>
                                    <dd className="flex items-center">
                                      <div className="text-lg font-medium text-gray-900">
                                        {historicalAnalysis.temperature_avg !== null ? `${historicalAnalysis.temperature_avg.toFixed(1)}°F` : 'N/A'}
                                      </div>
                                      <div className="ml-2">
                                        {getTrendIcon(historicalAnalysis.temperature_trend)}
                                      </div>
                                    </dd>
                                    <dd className="mt-1 text-sm text-gray-500">
                                      Trend: {historicalAnalysis.temperature_trend.charAt(0).toUpperCase() + historicalAnalysis.temperature_trend.slice(1)}
                                    </dd>
                                  </dl>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="px-4 py-5 sm:p-6">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                                  <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                                  </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                  <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Precipitation</dt>
                                    <dd className="flex items-center">
                                      <div className="text-lg font-medium text-gray-900">
                                        {historicalAnalysis.precipitation_avg !== null ? `${historicalAnalysis.precipitation_avg.toFixed(1)}%` : 'N/A'}
                                      </div>
                                      <div className="ml-2">
                                        {getTrendIcon(historicalAnalysis.precipitation_trend)}
                                      </div>
                                    </dd>
                                    <dd className="mt-1 text-sm text-gray-500">
                                      Trend: {historicalAnalysis.precipitation_trend.charAt(0).toUpperCase() + historicalAnalysis.precipitation_trend.slice(1)}
                                    </dd>
                                  </dl>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="px-4 py-5 sm:p-6">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                                  <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                  <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Humidity</dt>
                                    <dd className="flex items-center">
                                      <div className="text-lg font-medium text-gray-900">
                                        {historicalAnalysis.humidity_avg !== null ? `${historicalAnalysis.humidity_avg.toFixed(1)}%` : 'N/A'}
                                      </div>
                                      <div className="ml-2">
                                        {getTrendIcon(historicalAnalysis.humidity_trend)}
                                      </div>
                                    </dd>
                                    <dd className="mt-1 text-sm text-gray-500">
                                      Trend: {historicalAnalysis.humidity_trend.charAt(0).toUpperCase() + historicalAnalysis.humidity_trend.slice(1)}
                                    </dd>
                                  </dl>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="px-4 py-5 sm:p-6">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                                  <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                  </svg>
                                </div>
                                <div className="ml-5 w-0 flex-1">
                                  <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Wind Speed</dt>
                                    <dd className="flex items-center">
                                      <div className="text-lg font-medium text-gray-900">
                                        {historicalAnalysis.wind_speed_avg !== null ? `${historicalAnalysis.wind_speed_avg.toFixed(1)} km/h` : 'N/A'}
                                      </div>
                                      <div className="ml-2">
                                        {getTrendIcon(historicalAnalysis.wind_speed_trend)}
                                      </div>
                                    </dd>
                                    <dd className="mt-1 text-sm text-gray-500">
                                      Trend: {historicalAnalysis.wind_speed_trend.charAt(0).toUpperCase() + historicalAnalysis.wind_speed_trend.slice(1)}
                                    </dd>
                                  </dl>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Historical Data Chart */}
                  {historicalData.length > 0 && (
                    <div className="border-t border-gray-200">
                      <div className="px-4 py-5 sm:p-6">
                        <h4 className="text-base font-medium text-gray-900 mb-4">Historical Data</h4>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Date
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Temperature
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Precipitation
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Humidity
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Wind Speed
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Condition
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {historicalData.map((record, index) => (
                                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {record.interval_start}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.temperature_avg !== null ? `${record.temperature_avg.toFixed(1)}°F` : 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.precipitation_avg !== null ? `${record.precipitation_avg.toFixed(1)}%` : 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.humidity_avg !== null ? `${record.humidity_avg.toFixed(1)}%` : 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.wind_speed_avg !== null ? `${record.wind_speed_avg.toFixed(1)} km/h` : 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.most_common_condition || 'N/A'}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      <div className="px-4 py-4 sm:px-6 bg-gray-50">
                        <div className="text-sm">
                          <h5 className="font-medium text-gray-900">Crop Planning Insights</h5>
                          <p className="mt-1 text-gray-500">
                            Based on historical weather patterns, this location has shown 
                            {historicalAnalysis?.temperature_trend === 'increasing' ? ' increasing temperatures' : 
                             historicalAnalysis?.temperature_trend === 'decreasing' ? ' decreasing temperatures' : 
                             ' stable temperatures'} and
                            {historicalAnalysis?.precipitation_trend === 'increasing' ? ' increasing precipitation' : 
                             historicalAnalysis?.precipitation_trend === 'decreasing' ? ' decreasing precipitation' : 
                             ' stable precipitation'} over the past 6 months.
                          </p>
                          <p className="mt-2 text-gray-500">
                            Consider these patterns when planning crop rotations, irrigation schedules, and harvest timing.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            {harvestRecommendations.length > 0 && (
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Harvest Recommendations
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    Weather-based recommendations for optimal harvest timing
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="divide-y divide-gray-200">
                    {harvestRecommendations.map((rec, index) => (
                      <div key={index} className="px-4 py-5 sm:px-6">
                        <h4 className="text-md font-medium text-gray-900">
                          {rec.field_name} - {rec.crop_type}
                        </h4>
                        <p className="mt-1 text-sm text-gray-500">
                          {rec.reason}
                        </p>

                        <div className="mt-3">
                          <h5 className="text-sm font-medium text-gray-700">Optimal Harvest Days:</h5>
                          <div className="flex flex-wrap mt-1">
                            {rec.optimal_days.length > 0 ? (
                              rec.optimal_days.map((day, i) => (
                                <span key={i} className="mr-2 mb-2 px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                                  {formatDate(day)}
                                </span>
                              ))
                            ) : (
                              <span className="text-sm text-gray-500">No optimal days in the forecast period</span>
                            )}
                          </div>
                        </div>

                        {rec.warning_days.length > 0 && (
                          <div className="mt-3">
                            <h5 className="text-sm font-medium text-gray-700">Days to Avoid:</h5>
                            <div className="flex flex-wrap mt-1">
                              {rec.warning_days.map((day, i) => (
                                <span key={i} className="mr-2 mb-2 px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                                  {formatDate(day)}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* Harvest Schedule Section */}
        {selectedFarm && (
          <div className="mt-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Harvest Planning</h2>
            <p className="text-sm text-gray-500 mb-4">
              Plan and track your harvests with weather-based recommendations. The system will analyze weather forecasts to suggest optimal harvest days.
            </p>
            <HarvestSchedule farmId={selectedFarm.id} />
          </div>
        )}
      </div>
    </Layout>
  );
};

export default WeatherPage;
