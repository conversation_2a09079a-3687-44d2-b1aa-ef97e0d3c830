import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../config';
import { Disclosure } from '@headlessui/react';
import { ChevronUpIcon } from '@heroicons/react/24/outline';
import Layout from '../../components/Layout';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  name: string;
  faqs: FAQ[];
}

const FAQPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');

  useEffect(() => {
    const fetchFAQs = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch FAQs
        const response = await axios.get(`${API_URL}/faqs`);
        const faqs: FAQ[] = response.data.faqs || [];

        // Group FAQs by category
        const categoriesMap: { [key: string]: FAQ[] } = {};

        faqs.forEach(faq => {
          const category = faq.category || 'General';
          if (!categoriesMap[category]) {
            categoriesMap[category] = [];
          }
          categoriesMap[category].push(faq);
        });

        // Convert map to array of categories
        const categoriesArray: Category[] = Object.keys(categoriesMap).map(name => ({
          name,
          faqs: categoriesMap[name].sort((a, b) => a.order - b.order)
        }));

        setCategories(categoriesArray);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching FAQs:', err);
        setError(err.response?.data?.error || 'Failed to load FAQs');
        setLoading(false);
      }
    };

    fetchFAQs();
  }, []);

  // Filter FAQs based on search term
  const filteredCategories = searchTerm
    ? categories.map(category => ({
        name: category.name,
        faqs: category.faqs.filter(faq => 
          faq.question.toLowerCase().includes(searchTerm.toLowerCase()) || 
          faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })).filter(category => category.faqs.length > 0)
    : categories;

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/support" className="text-primary-600 hover:text-primary-800 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Support
          </Link>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Frequently Asked Questions</h1>

        {/* Search input */}
        <div className="mb-8">
          <div className="relative">
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
        ) : filteredCategories.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No FAQs found. Please check back later.</p>
          </div>
        ) : (
          <div className="space-y-8">
            {filteredCategories.map((category) => (
              <div key={category.name} className="bg-white shadow overflow-hidden rounded-lg">
                <div className="px-4 py-5 sm:px-6 bg-gray-50">
                  <h2 className="text-xl font-semibold text-gray-900">{category.name}</h2>
                </div>
                <div className="border-t border-gray-200 divide-y divide-gray-200">
                  {category.faqs.map((faq) => (
                    <Disclosure key={faq.id}>
                      {({ open }) => (
                        <>
                          <Disclosure.Button className="w-full px-4 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none">
                            <span className="text-gray-900 font-medium">{faq.question}</span>
                            <ChevronUpIcon
                              className={`${
                                open ? 'transform rotate-180' : ''
                              } w-5 h-5 text-gray-500`}
                            />
                          </Disclosure.Button>
                          <Disclosure.Panel className="px-4 py-4 bg-gray-50">
                            <div 
                              className="prose max-w-none text-gray-700"
                              dangerouslySetInnerHTML={{ __html: faq.answer }}
                            />
                          </Disclosure.Panel>
                        </>
                      )}
                    </Disclosure>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default FAQPage;
