import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface SchemaIssue {
  tableName: string;
  columnName?: string;
  expectedType?: string;
  actualType?: string;
  foreignKey?: string;
  targetTable?: string;
  modelName?: string;
  attributes?: any[];
  associationType?: string;
  associationName?: string;
  modelAttribute?: any;
  modelFilePath?: string;
}

interface SchemaComparisonResults {
  missingTables: SchemaIssue[];
  missingColumns: SchemaIssue[];
  typeMismatches: SchemaIssue[];
  missingForeignKeys: SchemaIssue[];
  totalIssues: number;
}

interface MigrationFile {
  name: string;
  path: string;
  content: string;
}

interface FixResult {
  success: boolean;
  message: string;
  sql?: string;
  fixedIssues?: any[];
  failedIssues?: any[];
  skippedIssues?: any[];
}

const DatabaseHealth: React.FC = () => {
  const [results, setResults] = useState<SchemaComparisonResults | null>(null);
  const [migrationFiles, setMigrationFiles] = useState<MigrationFile[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [scanning, setScanning] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'issues' | 'migrations'>('issues');
  const [fixingIssue, setFixingIssue] = useState<string | null>(null);
  const [fixingAll, setFixingAll] = useState<boolean>(false);
  const [fixResult, setFixResult] = useState<FixResult | null>(null);
  const [showFixResultModal, setShowFixResultModal] = useState<boolean>(false);

  useEffect(() => {
    checkDatabaseHealth();
    fetchMigrationFiles();
  }, []);

  const checkDatabaseHealth = async () => {
    try {
      setScanning(true);
      setError(null);

      const response = await axios.get(`${API_URL}/db-health/check`);
      setResults(response.data.results);
      setLoading(false);
      setScanning(false);
    } catch (err: any) {
      console.error('Error checking database health:', err);
      setError(err.response?.data?.error || 'Failed to check database health');
      setLoading(false);
      setScanning(false);
    }
  };

  const fetchMigrationFiles = async () => {
    try {
      const response = await axios.get(`${API_URL}/db-health/migrations`);
      setMigrationFiles(response.data.files || []);
    } catch (err: any) {
      console.error('Error fetching migration files:', err);
    }
  };

  const handleFixIssue = async (issueType: string, issue: SchemaIssue) => {
    try {
      setFixingIssue(`${issueType}-${issue.tableName}-${issue.columnName || issue.foreignKey || ''}`);
      setError(null);
      setFixResult(null);

      const response = await axios.post(`${API_URL}/db-health/fix-issue`, {
        issueType,
        issue
      });

      setFixResult(response.data);
      setShowFixResultModal(true);

      // Refresh the database health check to see the updated status
      await checkDatabaseHealth();

      setFixingIssue(null);
    } catch (err: any) {
      console.error('Error fixing schema issue:', err);
      setError(err.response?.data?.error || 'Failed to fix schema issue');
      setFixingIssue(null);
    }
  };

  const handleFixAll = async () => {
    try {
      setFixingAll(true);
      setError(null);
      setFixResult(null);

      const response = await axios.post(`${API_URL}/db-health/fix-all`);

      setFixResult(response.data);
      setShowFixResultModal(true);

      // Refresh the database health check to see the updated status
      await checkDatabaseHealth();

      setFixingAll(false);
    } catch (err: any) {
      console.error('Error fixing all schema issues:', err);
      setError(err.response?.data?.error || 'Failed to fix all schema issues');
      setFixingAll(false);
    }
  };

  const getStatusBadgeClass = (issueType: string) => {
    switch (issueType) {
      case 'missingTable':
        return 'bg-red-100 text-red-800';
      case 'missingColumn':
        return 'bg-orange-100 text-orange-800';
      case 'typeMismatch':
        return 'bg-yellow-100 text-yellow-800';
      case 'missingForeignKey':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold">Database Health</h2>
          {results && (
            <p className="text-sm text-gray-600 mt-1">
              Found {results.totalIssues} issues: {results.missingTables.length} missing tables, {results.missingColumns.length} missing columns, {results.typeMismatches.length} type mismatches, {results.missingForeignKeys.length} missing foreign keys
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          {results && results.totalIssues > 0 && (
            <button 
              onClick={handleFixAll}
              disabled={fixingAll || scanning}
              className={`${
                fixingAll || scanning
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-green-600 hover:bg-green-700'
              } text-white font-bold py-2 px-4 rounded`}
            >
              {fixingAll ? 'Fixing All...' : 'Fix All Issues'}
            </button>
          )}
          <button 
            onClick={checkDatabaseHealth}
            disabled={scanning || fixingAll}
            className={`${
              scanning || fixingAll
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-primary-600 hover:bg-primary-700'
            } text-white font-bold py-2 px-4 rounded`}
          >
            {scanning ? 'Scanning...' : 'Scan Database Schema'}
          </button>
        </div>
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab('issues')}
              className={`py-2 px-4 border-b-2 font-medium text-sm ${
                activeTab === 'issues'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Schema Issues
            </button>
            <button
              onClick={() => setActiveTab('migrations')}
              className={`ml-8 py-2 px-4 border-b-2 font-medium text-sm ${
                activeTab === 'migrations'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Migration Files
            </button>
          </nav>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      {activeTab === 'issues' ? (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          {results && results.totalIssues === 0 ? (
            <div className="p-6 text-center">
              <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 className="text-lg font-medium text-gray-900">Database schema is healthy!</h3>
              <p className="mt-2 text-sm text-gray-500">
                No issues were found between the database schema and the expected schema.
              </p>
            </div>
          ) : (
            <>
              {/* Missing Tables */}
              {results && results.missingTables.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 px-6 py-4 bg-gray-50">Missing Tables</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Model Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Table Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Model File Path
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {results.missingTables.map((issue, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {issue.modelName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.tableName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.modelFilePath}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass('missingTable')}`}>
                                Missing Table
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleFixIssue('missingTable', issue)}
                                disabled={fixingIssue === `missingTable-${issue.tableName}-` || fixingAll}
                                className={`${
                                  fixingIssue === `missingTable-${issue.tableName}-` || fixingAll
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'text-green-600 hover:text-green-900'
                                }`}
                              >
                                {fixingIssue === `missingTable-${issue.tableName}-` ? 'Fixing...' : 'Fix'}
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Missing Columns */}
              {results && results.missingColumns.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 px-6 py-4 bg-gray-50">Missing Columns</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Table Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Column Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Expected Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Model File Path
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {results.missingColumns.map((issue, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {issue.tableName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.columnName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.expectedType}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.modelFilePath}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass('missingColumn')}`}>
                                Missing Column
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleFixIssue('missingColumn', issue)}
                                disabled={fixingIssue === `missingColumn-${issue.tableName}-${issue.columnName}` || fixingAll}
                                className={`${
                                  fixingIssue === `missingColumn-${issue.tableName}-${issue.columnName}` || fixingAll
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'text-green-600 hover:text-green-900'
                                }`}
                              >
                                {fixingIssue === `missingColumn-${issue.tableName}-${issue.columnName}` ? 'Fixing...' : 'Fix'}
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Type Mismatches */}
              {results && results.typeMismatches.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 px-6 py-4 bg-gray-50">Type Mismatches</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Table Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Column Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Expected Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actual Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Model File Path
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {results.typeMismatches.map((issue, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {issue.tableName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.columnName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.expectedType}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.actualType}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.modelFilePath}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass('typeMismatch')}`}>
                                Type Mismatch
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleFixIssue('typeMismatch', issue)}
                                disabled={fixingIssue === `typeMismatch-${issue.tableName}-${issue.columnName}` || fixingAll}
                                className={`${
                                  fixingIssue === `typeMismatch-${issue.tableName}-${issue.columnName}` || fixingAll
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'text-green-600 hover:text-green-900'
                                }`}
                              >
                                {fixingIssue === `typeMismatch-${issue.tableName}-${issue.columnName}` ? 'Fixing...' : 'Fix'}
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Missing Foreign Keys */}
              {results && results.missingForeignKeys.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 px-6 py-4 bg-gray-50">Missing Foreign Keys</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Table Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Foreign Key
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Target Table
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Association Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Model File Path
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {results.missingForeignKeys.map((issue, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {issue.tableName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.foreignKey}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.targetTable}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.associationType}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {issue.modelFilePath}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass('missingForeignKey')}`}>
                                Missing Foreign Key
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleFixIssue('missingForeignKey', issue)}
                                disabled={fixingIssue === `missingForeignKey-${issue.tableName}-${issue.foreignKey}` || fixingAll}
                                className={`${
                                  fixingIssue === `missingForeignKey-${issue.tableName}-${issue.foreignKey}` || fixingAll
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'text-green-600 hover:text-green-900'
                                }`}
                              >
                                {fixingIssue === `missingForeignKey-${issue.tableName}-${issue.foreignKey}` ? 'Fixing...' : 'Fix'}
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <h3 className="text-lg font-medium text-gray-900 px-6 py-4 bg-gray-50">Migration Files</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Path
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Preview
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {migrationFiles.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                      No migration files found.
                    </td>
                  </tr>
                ) : (
                  migrationFiles.map((file, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {file.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {file.path}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        <div className="max-h-20 overflow-y-auto">
                          <pre className="text-xs">{file.content}...</pre>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Fix Result Modal */}
      {showFixResultModal && fixResult && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">
                {fixResult.success ? 'Fix Successful' : 'Fix Failed'}
              </h3>
              <button 
                onClick={() => setShowFixResultModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="mb-4">
                {fixResult.message}
              </p>
              {fixResult.sql && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">SQL Executed:</p>
                  <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">{fixResult.sql}</pre>
                </div>
              )}
              {fixResult.fixedIssues && fixResult.fixedIssues.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">Fixed Issues:</p>
                  <ul className="list-disc pl-5 text-sm">
                    {fixResult.fixedIssues.map((issue, index) => (
                      <li key={index} className="text-green-600">
                        {issue.type === 'missingColumn' && `Added column ${issue.columnName} to table ${issue.tableName}`}
                        {issue.type === 'typeMismatch' && `Fixed type for column ${issue.columnName} in table ${issue.tableName}`}
                        {issue.type === 'missingForeignKey' && `Added foreign key ${issue.foreignKey} to table ${issue.tableName}`}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {fixResult.failedIssues && fixResult.failedIssues.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">Failed Issues:</p>
                  <ul className="list-disc pl-5 text-sm">
                    {fixResult.failedIssues.map((issue, index) => (
                      <li key={index} className="text-red-600">
                        {issue.type}: {issue.error}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {fixResult.skippedIssues && fixResult.skippedIssues.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">Skipped Issues:</p>
                  <ul className="list-disc pl-5 text-sm">
                    {fixResult.skippedIssues.map((issue, index) => (
                      <li key={index} className="text-yellow-600">
                        {issue.type} ({issue.count}): {issue.reason}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowFixResultModal(false)}
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatabaseHealth;
