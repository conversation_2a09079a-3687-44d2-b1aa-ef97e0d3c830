import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface Farm {
  id: string;
  name: string;
  subscription_status: string;
  subscription_plan_id: string | null;
  subscription_start_date: string | null;
  subscription_end_date: string | null;
  billing_email: string | null;
  created_at: string;
}

interface SubscriptionPlan {
  id: string;
  name: string;
}

const FarmManagement: React.FC = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [currentFarm, setCurrentFarm] = useState<Farm | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    billing_email: '',
    subscription_plan_id: '',
    subscription_status: 'active'
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch farms
        const farmsResponse = await axios.get(`${API_URL}/admin/farms`);
        setFarms(Array.isArray(farmsResponse.data.farms) ? farmsResponse.data.farms : []);

        // Fetch subscription plans
        const plansResponse = await axios.get(`${API_URL}/admin/subscription-plans`);
        // Check if the response has a plans property, otherwise use the data directly
        const plansData = plansResponse.data.plans || plansResponse.data;
        setSubscriptionPlans(Array.isArray(plansData) ? plansData : []);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching farm data:', err);
        setError(err.response?.data?.error || 'Failed to load farm data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleAddFarm = () => {
    setFormData({
      name: '',
      billing_email: '',
      subscription_plan_id: '',
      subscription_status: 'active'
    });
    setShowAddModal(true);
  };

  const handleEditFarm = (farm: Farm) => {
    setCurrentFarm(farm);
    setFormData({
      name: farm.name,
      billing_email: farm.billing_email || '',
      subscription_plan_id: farm.subscription_plan_id || '',
      subscription_status: farm.subscription_status
    });
    setShowEditModal(true);
  };

  const handleDeleteFarm = (farm: Farm) => {
    setCurrentFarm(farm);
    setShowDeleteModal(true);
  };

  const confirmDeleteFarm = async () => {
    if (!currentFarm) return;

    try {
      setError(null);

      await axios.delete(`${API_URL}/admin/farms/${currentFarm.id}`);

      setFarms(prev => 
        Array.isArray(prev) ? prev.filter(farm => farm.id !== currentFarm.id) : []
      );

      setShowDeleteModal(false);
    } catch (err: any) {
      console.error('Error deleting farm:', err);
      setError(err.response?.data?.error || 'Failed to delete farm');
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);

      const response = await axios.post(`${API_URL}/admin/farms`, {
        name: formData.name,
        billing_email: formData.billing_email,
        subscription_plan_id: formData.subscription_plan_id || null,
        subscription_status: formData.subscription_status
      });

      setFarms(prev => Array.isArray(prev) ? [...prev, response.data.farm] : [response.data.farm]);
      setShowAddModal(false);
    } catch (err: any) {
      console.error('Error adding farm:', err);
      setError(err.response?.data?.error || 'Failed to add farm');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentFarm) return;

    try {
      setError(null);

      const response = await axios.put(`${API_URL}/admin/farms/${currentFarm.id}`, {
        name: formData.name,
        billing_email: formData.billing_email,
        subscription_plan_id: formData.subscription_plan_id || null,
        subscription_status: formData.subscription_status
      });

      setFarms(prev => 
        Array.isArray(prev) ? prev.map(farm => 
          farm.id === currentFarm.id ? response.data.farm : farm
        ) : [response.data.farm]
      );

      setShowEditModal(false);
    } catch (err: any) {
      console.error('Error updating farm:', err);
      setError(err.response?.data?.error || 'Failed to update farm');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Manage Farms</h2>
        <button 
          onClick={handleAddFarm}
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
        >
          Add Farm
        </button>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Subscription
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Billing Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.isArray(farms) ? farms.map(farm => (
              <tr key={farm.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{farm.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    farm.subscription_status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {farm.subscription_status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {farm.subscription_plan_id 
                    ? subscriptionPlans.find(plan => plan.id === farm.subscription_plan_id)?.name || 'Unknown'
                    : 'None'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {farm.billing_email || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(farm.created_at).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => handleEditFarm(farm)}
                    className="text-primary-600 hover:text-primary-900 mr-4"
                  >
                    Edit
                  </button>
                  <button 
                    onClick={() => handleDeleteFarm(farm)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            )) : <tr><td colSpan={6} className="px-6 py-4 text-center">No farms available</td></tr>}
          </tbody>
        </table>
      </div>

      {/* Add Farm Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Add New Farm</h3>
              <button 
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitAdd} className="p-5">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                  Farm Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="billing_email">
                  Billing Email
                </label>
                <input
                  type="email"
                  id="billing_email"
                  name="billing_email"
                  value={formData.billing_email}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="subscription_plan_id">
                  Subscription Plan
                </label>
                <select
                  id="subscription_plan_id"
                  name="subscription_plan_id"
                  value={formData.subscription_plan_id}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                >
                  <option value="">Select a plan</option>
                  {Array.isArray(subscriptionPlans) ? subscriptionPlans.map(plan => (
                    <option key={plan.id} value={plan.id}>{plan.name}</option>
                  )) : null}
                </select>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="subscription_status">
                  Status
                </label>
                <select
                  id="subscription_status"
                  name="subscription_status"
                  value={formData.subscription_status}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add Farm
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Farm Modal */}
      {showEditModal && currentFarm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Edit Farm</h3>
              <button 
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-5">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-name">
                  Farm Name
                </label>
                <input
                  type="text"
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-billing_email">
                  Billing Email
                </label>
                <input
                  type="email"
                  id="edit-billing_email"
                  name="billing_email"
                  value={formData.billing_email}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-subscription_plan_id">
                  Subscription Plan
                </label>
                <select
                  id="edit-subscription_plan_id"
                  name="subscription_plan_id"
                  value={formData.subscription_plan_id}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                >
                  <option value="">Select a plan</option>
                  {Array.isArray(subscriptionPlans) ? subscriptionPlans.map(plan => (
                    <option key={plan.id} value={plan.id}>{plan.name}</option>
                  )) : null}
                </select>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-subscription_status">
                  Status
                </label>
                <select
                  id="edit-subscription_status"
                  name="subscription_status"
                  value={formData.subscription_status}
                  onChange={handleFormChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update Farm
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Farm Confirmation Modal */}
      {showDeleteModal && currentFarm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Delete Farm</h3>
              <button 
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete the farm <span className="font-semibold">{currentFarm.name}</span>? This action cannot be undone.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={confirmDeleteFarm}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                >
                  Delete Farm
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FarmManagement;
