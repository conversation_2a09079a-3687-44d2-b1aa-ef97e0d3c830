import React, { useState, useEffect, createContext } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { useAuth } from '../../context/AuthContext';
import DashboardGrid from '../../components/DashboardGrid';
import WidgetRenderer from '../../components/WidgetRenderer';
import { Widget, DashboardContext } from '../../context/DashboardContext';
import WidgetSelector from '../../components/WidgetSelector';

const DashboardLayoutManagement: React.FC = () => {
  const { token } = useAuth();
  const [dashboardType, setDashboardType] = useState<'farm' | 'business'>('farm');
  const [dashboardLayout, setDashboardLayout] = useState<any>(null);
  const [isDefaultLayout, setIsDefaultLayout] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [showWidgetSelector, setShowWidgetSelector] = useState<boolean>(false);

  // Fetch the global dashboard layout
  const fetchGlobalDashboardLayout = async (type: 'farm' | 'business') => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/dashboard/global/${type}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setDashboardLayout(response.data.layout);
      setIsDefaultLayout(response.data.isDefault);
      setIsLoading(false);
    } catch (err: any) {
      console.error('Error fetching global dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to load dashboard layout');
      setIsLoading(false);
    }
  };

  // Save the global dashboard layout
  const saveGlobalDashboardLayout = async (layout: any) => {
    try {
      setIsLoading(true);
      setError(null);

      await axios.post(`${API_URL}/dashboard/global/${dashboardType}`, {
        layout
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setDashboardLayout(layout);
      setIsDefaultLayout(false);
      setIsLoading(false);
      setEditMode(false);
    } catch (err: any) {
      console.error('Error saving global dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to save dashboard layout');
      setIsLoading(false);
    }
  };

  // Handle dashboard type change
  const handleDashboardTypeChange = (type: 'farm' | 'business') => {
    setDashboardType(type);
    fetchGlobalDashboardLayout(type);
  };

  // Handle edit mode change
  const handleEditModeChange = (mode: boolean) => {
    if (!mode) {
      // Save the layout when exiting edit mode
      saveGlobalDashboardLayout(dashboardLayout);
    } else {
      setEditMode(mode);
    }
  };

  // Add a widget to the dashboard
  const handleAddWidget = (widget: Widget) => {
    if (!dashboardLayout) return;

    const updatedLayout = {
      ...dashboardLayout,
      widgets: [...dashboardLayout.widgets, widget]
    };

    setDashboardLayout(updatedLayout);
    setShowWidgetSelector(false);
  };

  // Remove a widget from the dashboard
  const handleRemoveWidget = (widgetId: string) => {
    if (!dashboardLayout) return;

    const updatedLayout = {
      ...dashboardLayout,
      widgets: dashboardLayout.widgets.filter((widget: Widget) => widget.id !== widgetId)
    };

    setDashboardLayout(updatedLayout);
  };

  // Update a widget's position
  const handleUpdateWidgetPosition = (widgetId: string, position: { x: number; y: number; w: number; h: number }) => {
    if (!dashboardLayout) return;

    const updatedLayout = {
      ...dashboardLayout,
      widgets: dashboardLayout.widgets.map((widget: Widget) => 
        widget.id === widgetId ? { ...widget, position } : widget
      )
    };

    setDashboardLayout(updatedLayout);
  };

  // Render a widget
  const renderWidget = (widget: Widget) => {
    return (
      <WidgetRenderer 
        widget={widget} 
        editMode={editMode}
        onRemove={editMode ? handleRemoveWidget : undefined} 
      />
    );
  };

  // Load the dashboard layout when the component mounts or dashboard type changes
  useEffect(() => {
    fetchGlobalDashboardLayout(dashboardType);
  }, [dashboardType]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        <p className="ml-3 text-gray-500">Loading dashboard layout...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Dashboard Layout Management</h1>
        <p className="text-gray-600 mb-4">
          Configure the default dashboard layouts for farms and businesses. These layouts will be used as the default for new users.
        </p>

        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => handleDashboardTypeChange('farm')}
            className={`px-4 py-2 rounded-md ${
              dashboardType === 'farm' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Farm Dashboard
          </button>
          <button
            onClick={() => handleDashboardTypeChange('business')}
            className={`px-4 py-2 rounded-md ${
              dashboardType === 'business' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Business Dashboard
          </button>
        </div>
      </div>

      {dashboardLayout && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              {dashboardType === 'farm' ? 'Farm' : 'Business'} Dashboard Layout
              {isDefaultLayout && (
                <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                  System Default
                </span>
              )}
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setEditMode(!editMode)}
                className={`btn ${editMode ? 'btn-primary' : 'btn-outline'}`}
              >
                {editMode ? 'Save Layout' : 'Edit Layout'}
              </button>
              {editMode && (
                <button
                  onClick={() => setShowWidgetSelector(true)}
                  className="btn btn-outline"
                >
                  Add Widget
                </button>
              )}
            </div>
          </div>

          <div className="dashboard-preview">
            {dashboardLayout && (
              <DashboardContext.Provider value={{
                dashboardLayout: dashboardLayout,
                isDefaultLayout: isDefaultLayout,
                isLoading: false,
                error: null,
                availableLayouts: [],
                currentLayoutName: 'Default',
                currentLayoutId: null,
                saveDashboardLayout: saveGlobalDashboardLayout,
                saveAsNewLayout: (layout, name) => Promise.resolve(),
                setActiveLayout: () => Promise.resolve(),
                fetchAvailableLayouts: () => Promise.resolve(),
                resetToFarmDefault: () => Promise.resolve(),
                deleteLayout: () => Promise.resolve(),
                addWidget: handleAddWidget,
                removeWidget: handleRemoveWidget,
                updateWidgetPosition: handleUpdateWidgetPosition,
                clearError: () => {}
              }}>
                <DashboardGrid
                  editMode={editMode}
                  onEditModeChange={handleEditModeChange}
                  renderWidget={renderWidget}
                />
              </DashboardContext.Provider>
            )}
          </div>
        </div>
      )}

      {/* Widget Selector Modal */}
      {showWidgetSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Add Widget</h3>
              <button
                onClick={() => setShowWidgetSelector(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            <WidgetSelector onClose={() => setShowWidgetSelector(false)} />
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardLayoutManagement;
