import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { useNavigate } from 'react-router-dom';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface Farm {
  id: string;
  name: string;
}

interface Comment {
  id: string;
  content: string;
  author: User;
  isInternal: boolean;
  createdAt: string;
}

interface Attachment {
  id: string;
  filename: string;
  fileSize: number;
  fileType: string;
  uploader: User;
  createdAt: string;
}

interface SupportTicket {
  id: string;
  subject: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string | null;
  creator: User;
  farm: Farm | null;
  assignee: User | null;
  comments: Comment[];
  attachments: Attachment[];
  createdAt: string;
  updatedAt: string;
  resolvedAt: string | null;
}

const SupportTicketManagement: React.FC = () => {
  const navigate = useNavigate();
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showTicketModal, setShowTicketModal] = useState<boolean>(false);
  const [currentTicket, setCurrentTicket] = useState<SupportTicket | null>(null);
  const [newComment, setNewComment] = useState<string>('');
  const [isInternalComment, setIsInternalComment] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    assignedTo: '',
    search: ''
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all support tickets
        const ticketsResponse = await axios.get(`${API_URL}/api/support`, {
          params: {
            ...filters
          }
        });
        setTickets(ticketsResponse.data.tickets || []);

        // Fetch all users for assignment dropdown
        const usersResponse = await axios.get(`${API_URL}/admin/users`);
        setUsers(usersResponse.data.users || []);

        // Fetch all farms for farm dropdown
        const farmsResponse = await axios.get(`${API_URL}/api/farms`);
        setFarms(farmsResponse.data.farms || []);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching support tickets:', err);
        setError(err.response?.data?.error || 'Failed to load support tickets');
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]);

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleViewTicket = async (ticketId: string) => {
    try {
      setError(null);
      const response = await axios.get(`${API_URL}/api/support/${ticketId}`);
      setCurrentTicket(response.data.ticket);
      setShowTicketModal(true);
    } catch (err: any) {
      console.error('Error fetching ticket details:', err);
      setError(err.response?.data?.error || 'Failed to load ticket details');
    }
  };

  const handleAssignTicket = async (ticketId: string, userId: string) => {
    try {
      setError(null);
      await axios.put(`${API_URL}/api/support/${ticketId}`, {
        assignedTo: userId === 'none' ? null : userId
      });

      // Update the ticket in the list
      setTickets(prev => 
        prev.map(ticket => {
          if (ticket.id === ticketId) {
            const assignee = userId === 'none' 
              ? null 
              : users.find(user => user.id === userId) || null;

            return { ...ticket, assignee };
          }
          return ticket;
        })
      );

      // If the current ticket is open in the modal, update it too
      if (currentTicket && currentTicket.id === ticketId) {
        setCurrentTicket(prev => {
          if (!prev) return null;

          const assignee = userId === 'none' 
            ? null 
            : users.find(user => user.id === userId) || null;

          return { ...prev, assignee };
        });
      }
    } catch (err: any) {
      console.error('Error assigning ticket:', err);
      setError(err.response?.data?.error || 'Failed to assign ticket');
    }
  };

  const handleUpdateStatus = async (ticketId: string, status: SupportTicket['status']) => {
    try {
      setError(null);
      await axios.put(`${API_URL}/api/support/${ticketId}`, { status });

      // Update the ticket in the list
      setTickets(prev => 
        prev.map(ticket => 
          ticket.id === ticketId ? { ...ticket, status } : ticket
        )
      );

      // If the current ticket is open in the modal, update it too
      if (currentTicket && currentTicket.id === ticketId) {
        setCurrentTicket(prev => {
          if (!prev) return null;
          return { ...prev, status };
        });
      }
    } catch (err: any) {
      console.error('Error updating ticket status:', err);
      setError(err.response?.data?.error || 'Failed to update ticket status');
    }
  };

  const handleUpdatePriority = async (ticketId: string, priority: SupportTicket['priority']) => {
    try {
      setError(null);
      await axios.put(`${API_URL}/api/support/${ticketId}`, { priority });

      // Update the ticket in the list
      setTickets(prev => 
        prev.map(ticket => 
          ticket.id === ticketId ? { ...ticket, priority } : ticket
        )
      );

      // If the current ticket is open in the modal, update it too
      if (currentTicket && currentTicket.id === ticketId) {
        setCurrentTicket(prev => {
          if (!prev) return null;
          return { ...prev, priority };
        });
      }
    } catch (err: any) {
      console.error('Error updating ticket priority:', err);
      setError(err.response?.data?.error || 'Failed to update ticket priority');
    }
  };

  const handleUpdateFarm = async (ticketId: string, farmId: string | null) => {
    try {
      setError(null);
      await axios.put(`${API_URL}/api/support/${ticketId}`, { farmId });

      // Update the ticket in the list
      setTickets(prev => 
        prev.map(ticket => {
          if (ticket.id === ticketId) {
            const farm = farmId ? farms.find(f => f.id === farmId) || null : null;
            return { ...ticket, farm };
          }
          return ticket;
        })
      );

      // If the current ticket is open in the modal, update it too
      if (currentTicket && currentTicket.id === ticketId) {
        setCurrentTicket(prev => {
          if (!prev) return null;
          const farm = farmId ? farms.find(f => f.id === farmId) || null : null;
          return { ...prev, farm };
        });
      }
    } catch (err: any) {
      console.error('Error updating ticket farm:', err);
      setError(err.response?.data?.error || 'Failed to update ticket farm');
    }
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTicket || !newComment.trim()) return;

    try {
      setError(null);
      const response = await axios.post(`${API_URL}/api/support/${currentTicket.id}/comments`, {
        content: newComment,
        isInternal: isInternalComment
      });

      // Add the new comment to the current ticket
      setCurrentTicket(prev => {
        if (!prev) return null;
        return {
          ...prev,
          comments: [...prev.comments, response.data.comment]
        };
      });

      // Clear the comment form
      setNewComment('');
      setIsInternalComment(false);
    } catch (err: any) {
      console.error('Error adding comment:', err);
      setError(err.response?.data?.error || 'Failed to add comment');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        setUploadError('File size exceeds the limit of 10MB.');
        setSelectedFile(null);
        return;
      }

      // Validate file type
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain', 'text/csv'
      ];

      if (!allowedTypes.includes(file.type)) {
        setUploadError('Invalid file type. Only images, documents, and PDFs are allowed.');
        setSelectedFile(null);
        return;
      }

      setSelectedFile(file);
      setUploadError(null);
    }
  };

  const handleUploadFile = async () => {
    if (!currentTicket || !selectedFile) return;

    try {
      setUploadError(null);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axios.post(
        `${API_URL}/api/support/${currentTicket.id}/attachments`, 
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(progress);
            }
          }
        }
      );

      // Add the new attachment to the current ticket
      setCurrentTicket(prev => {
        if (!prev) return null;
        return {
          ...prev,
          attachments: [...prev.attachments, response.data.attachment]
        };
      });

      // Clear the file input
      setSelectedFile(null);
      setUploadProgress(0);
    } catch (err: any) {
      console.error('Error uploading file:', err);
      setUploadError(err.response?.data?.error || 'Failed to upload file');
      setUploadProgress(0);
    }
  };

  const handleDownloadAttachment = async (attachmentId: string) => {
    try {
      setError(null);

      // Use window.open to download the file
      window.open(`${API_URL}/api/support/attachments/${attachmentId}/download`, '_blank');
    } catch (err: any) {
      console.error('Error downloading attachment:', err);
      setError(err.response?.data?.error || 'Failed to download attachment');
    }
  };

  const handleDeleteAttachment = async (attachmentId: string) => {
    if (!currentTicket) return;

    try {
      setError(null);
      await axios.delete(`${API_URL}/api/support/attachments/${attachmentId}`);

      // Remove the attachment from the current ticket
      setCurrentTicket(prev => {
        if (!prev) return null;
        return {
          ...prev,
          attachments: prev.attachments.filter(attachment => attachment.id !== attachmentId)
        };
      });
    } catch (err: any) {
      console.error('Error deleting attachment:', err);
      setError(err.response?.data?.error || 'Failed to delete attachment');
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusBadgeClass = (status: SupportTicket['status']) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityBadgeClass = (priority: SupportTicket['priority']) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'high':
        return 'bg-yellow-100 text-yellow-800';
      case 'urgent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Support Ticket Management</h2>
      </div>

      {/* Filters */}
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Statuses</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
            <select
              name="priority"
              value={filters.priority}
              onChange={handleFilterChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Assigned To</label>
            <select
              name="assignedTo"
              value={filters.assignedTo}
              onChange={handleFilterChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All Assignees</option>
              <option value="unassigned">Unassigned</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.firstName} {user.lastName}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              name="search"
              value={filters.search}
              onChange={handleFilterChange}
              placeholder="Search tickets..."
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Tickets Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Farm
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Subject
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created By
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Assigned To
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created At
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tickets.map(ticket => (
              <tr key={ticket.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {ticket.farm ? ticket.farm.name : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{ticket.subject}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(ticket.status)}`}>
                    {ticket.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityBadgeClass(ticket.priority)}`}>
                    {ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {ticket.creator ? `${ticket.creator.firstName} ${ticket.creator.lastName}` : 'Unknown'}
                  </div>
                  <div className="text-sm text-gray-500">{ticket.creator?.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={ticket.assignee?.id || 'none'}
                    onChange={(e) => handleAssignTicket(ticket.id, e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="none">Unassigned</option>
                    {users.map(user => (
                      <option key={user.id} value={user.id}>
                        {user.firstName} {user.lastName}
                      </option>
                    ))}
                  </select>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(ticket.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => handleViewTicket(ticket.id)}
                    className="text-primary-600 hover:text-primary-900 mr-4"
                  >
                    View
                  </button>
                  <select
                    value={ticket.status}
                    onChange={(e) => handleUpdateStatus(ticket.id, e.target.value as SupportTicket['status'])}
                    className="mr-2 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="open">Open</option>
                    <option value="in_progress">In Progress</option>
                    <option value="resolved">Resolved</option>
                    <option value="closed">Closed</option>
                  </select>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Ticket Detail Modal */}
      {showTicketModal && currentTicket && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Ticket Details</h3>
              <button 
                onClick={() => setShowTicketModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Subject</h4>
                  <p className="text-base">{currentTicket.subject}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Created By</h4>
                  <p className="text-base">
                    {currentTicket.creator ? `${currentTicket.creator.firstName} ${currentTicket.creator.lastName}` : 'Unknown'}
                    {currentTicket.creator && <span className="text-sm text-gray-500 ml-2">({currentTicket.creator.email})</span>}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <select
                    value={currentTicket.status}
                    onChange={(e) => handleUpdateStatus(currentTicket.id, e.target.value as SupportTicket['status'])}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="open">Open</option>
                    <option value="in_progress">In Progress</option>
                    <option value="resolved">Resolved</option>
                    <option value="closed">Closed</option>
                  </select>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Priority</h4>
                  <select
                    value={currentTicket.priority}
                    onChange={(e) => handleUpdatePriority(currentTicket.id, e.target.value as SupportTicket['priority'])}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Assigned To</h4>
                  <select
                    value={currentTicket.assignee?.id || 'none'}
                    onChange={(e) => handleAssignTicket(currentTicket.id, e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="none">Unassigned</option>
                    {users.map(user => (
                      <option key={user.id} value={user.id}>
                        {user.firstName} {user.lastName}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Created At</h4>
                  <p className="text-base">{formatDate(currentTicket.createdAt)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Farm</h4>
                  <select
                    value={currentTicket.farm?.id || 'none'}
                    onChange={(e) => handleUpdateFarm(currentTicket.id, e.target.value === 'none' ? null : e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="none">None</option>
                    {farms.map(farm => (
                      <option key={farm.id} value={farm.id}>
                        {farm.name}
                      </option>
                    ))}
                  </select>
                </div>
                {currentTicket.resolvedAt && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Resolved At</h4>
                    <p className="text-base">{formatDate(currentTicket.resolvedAt)}</p>
                  </div>
                )}
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500">Description</h4>
                <div className="mt-1 p-3 bg-gray-50 rounded-md">
                  <p className="text-base whitespace-pre-wrap">{currentTicket.description}</p>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Attachments</h4>
                {currentTicket.attachments && currentTicket.attachments.length > 0 ? (
                  <div className="space-y-2">
                    {currentTicket.attachments.map(attachment => (
                      <div 
                        key={attachment.id} 
                        className="p-3 rounded-md bg-gray-50 flex justify-between items-center"
                      >
                        <div>
                          <p className="text-sm font-medium">{attachment.filename}</p>
                          <p className="text-xs text-gray-500">
                            {(attachment.fileSize / 1024).toFixed(2)} KB • 
                            Uploaded by {attachment.uploader ? `${attachment.uploader.firstName} ${attachment.uploader.lastName}` : 'Unknown'} • 
                            {formatDate(attachment.createdAt)}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleDownloadAttachment(attachment.id)}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            Download
                          </button>
                          <button
                            onClick={() => handleDeleteAttachment(attachment.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No attachments yet.</p>
                )}

                <div className="mt-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="file"
                      id="file-upload"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="file-upload"
                      className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded cursor-pointer"
                    >
                      Select File
                    </label>
                    {selectedFile && (
                      <>
                        <span className="text-sm text-gray-700">{selectedFile.name}</span>
                        <button
                          onClick={handleUploadFile}
                          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                          disabled={uploadProgress > 0 && uploadProgress < 100}
                        >
                          {uploadProgress > 0 && uploadProgress < 100 ? `Uploading ${uploadProgress}%` : 'Upload'}
                        </button>
                      </>
                    )}
                  </div>
                  {uploadError && (
                    <p className="text-sm text-red-600 mt-1">{uploadError}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Max file size: 10MB. Allowed file types: Images, Documents, PDFs.
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Comments</h4>
                {currentTicket.comments && currentTicket.comments.length > 0 ? (
                  <div className="space-y-4">
                    {currentTicket.comments.map(comment => (
                      <div 
                        key={comment.id} 
                        className={`p-3 rounded-md ${comment.isInternal ? 'bg-yellow-50 border-l-4 border-yellow-400' : 'bg-gray-50'}`}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-sm font-medium">
                              {comment.author ? `${comment.author.firstName} ${comment.author.lastName}` : 'Unknown'}
                              {comment.isInternal && <span className="ml-2 text-xs text-yellow-600">(Internal Note)</span>}
                            </p>
                            <p className="text-xs text-gray-500">{formatDate(comment.createdAt)}</p>
                          </div>
                        </div>
                        <p className="mt-2 text-sm whitespace-pre-wrap">{comment.content}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No comments yet.</p>
                )}
              </div>

              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-500 mb-2">Add Comment</h4>
                <form onSubmit={handleAddComment}>
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    rows={4}
                    placeholder="Type your comment here..."
                    required
                  ></textarea>
                  <div className="flex items-center mt-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isInternalComment}
                        onChange={(e) => setIsInternalComment(e.target.checked)}
                        className="form-checkbox h-5 w-5 text-primary-600"
                      />
                      <span className="ml-2 text-sm text-gray-700">Internal note (not visible to the user)</span>
                    </label>
                  </div>
                  <div className="flex justify-end mt-4">
                    <button
                      type="submit"
                      className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                    >
                      Add Comment
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupportTicketManagement;
