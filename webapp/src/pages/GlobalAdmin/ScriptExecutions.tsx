import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface ScriptExecution {
  id: string;
  name: string;
  description: string;
  file_path: string;
  executed: boolean;
  executed_at: string | null;
  executed_by: string | null;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  output: string | null;
  error_message: string | null;
  created_at: string;
  updated_at: string;
  executedBy?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface ScriptStatus {
  pendingScripts: number;
  allScripts: number;
  executedScripts: number;
  allExecuted: boolean;
}

const ScriptExecutions: React.FC = () => {
  const [scripts, setScripts] = useState<ScriptExecution[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [scriptStatus, setScriptStatus] = useState<ScriptStatus | null>(null);
  const [scanningForScripts, setScanningForScripts] = useState<boolean>(false);
  const [executingScript, setExecutingScript] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [selectedScript, setSelectedScript] = useState<ScriptExecution | null>(null);
  const [activeFilter, setActiveFilter] = useState<'all' | 'pending' | 'executed'>('all');
  const [showOutputModal, setShowOutputModal] = useState<boolean>(false);

  useEffect(() => {
    fetchScripts();
    checkScriptStatus();
  }, [activeFilter]);

  const fetchScripts = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/script-executions`, {
        params: { filter: activeFilter }
      });
      setScripts(response.data.scriptExecutions || []);

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching scripts:', err);
      setError(err.response?.data?.error || 'Failed to load scripts');
      setLoading(false);
    }
  };

  const checkScriptStatus = async () => {
    try {
      const response = await axios.get(`${API_URL}/script-executions/status/check`);
      setScriptStatus(response.data);
    } catch (err: any) {
      console.error('Error checking script status:', err);
    }
  };

  const handleScanForScripts = async () => {
    try {
      setScanningForScripts(true);
      setError(null);

      const response = await axios.post(`${API_URL}/script-executions/scan`);

      // Refresh scripts with current filter
      fetchScripts();
      await checkScriptStatus();
      setScanningForScripts(false);
    } catch (err: any) {
      console.error('Error scanning for scripts:', err);
      setError(err.response?.data?.error || 'Failed to scan for scripts');
      setScanningForScripts(false);
    }
  };

  const confirmExecuteScript = (script: ScriptExecution) => {
    setSelectedScript(script);
    setShowConfirmModal(true);
  };

  const handleExecuteScript = async () => {
    if (!selectedScript) return;

    try {
      setExecutingScript(selectedScript.id);
      setError(null);
      setShowConfirmModal(false);

      const response = await axios.post(`${API_URL}/script-executions/${selectedScript.id}/execute`);

      // Refresh scripts with current filter
      fetchScripts();
      await checkScriptStatus();
      setExecutingScript(null);
    } catch (err: any) {
      console.error('Error executing script:', err);
      setError(err.response?.data?.error || 'Failed to execute script');
      setExecutingScript(null);

      // Refresh scripts to get updated status
      fetchScripts();
    }
  };

  const viewScriptOutput = (script: ScriptExecution) => {
    setSelectedScript(script);
    setShowOutputModal(true);
  };

  const getStatusBadgeClass = (status: string, executed: boolean) => {
    if (status === 'completed' || executed) return 'bg-green-100 text-green-800';
    if (status === 'in_progress') return 'bg-blue-100 text-blue-800';
    if (status === 'failed') return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (status: string, executed: boolean) => {
    if (status === 'completed' || executed) return 'Executed';
    if (status === 'in_progress') return 'In Progress';
    if (status === 'failed') return 'Failed';
    return 'Pending';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold">Script Executions</h2>
          {scriptStatus && (
            <p className="text-sm text-gray-600 mt-1">
              {scriptStatus.pendingScripts} pending, {scriptStatus.executedScripts} executed out of {scriptStatus.allScripts} total scripts
            </p>
          )}
        </div>
        <button 
          onClick={handleScanForScripts}
          disabled={scanningForScripts}
          className={`${
            scanningForScripts 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-primary-600 hover:bg-primary-700'
          } text-white font-bold py-2 px-4 rounded`}
        >
          {scanningForScripts ? 'Scanning...' : 'Scan for New Scripts'}
        </button>
      </div>

      <div className="flex space-x-4 mb-4">
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-all"
            checked={activeFilter === 'all'}
            onChange={() => setActiveFilter('all')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-all" className="ml-2 block text-sm text-gray-700">
            Show All
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-pending"
            checked={activeFilter === 'pending'}
            onChange={() => setActiveFilter('pending')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-pending" className="ml-2 block text-sm text-gray-700">
            Pending Scripts
          </label>
        </div>
        <div className="flex items-center">
          <input
            type="radio"
            id="filter-executed"
            checked={activeFilter === 'executed'}
            onChange={() => setActiveFilter('executed')}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <label htmlFor="filter-executed" className="ml-2 block text-sm text-gray-700">
            Executed Scripts
          </label>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg table-container">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Executed By
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Executed At
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {scripts.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                  No scripts found. Click "Scan for New Scripts" to detect available scripts.
                </td>
              </tr>
            ) : (
              scripts.map(script => (
                <tr key={script.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{script.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500">{script.description}</div>
                    {script.error_message && (
                      <div className="text-sm text-red-500 mt-1">Error occurred. View output for details.</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      getStatusBadgeClass(script.status, script.executed)
                    }`}>
                      {getStatusText(script.status, script.executed)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {script.executedBy ? `${script.executedBy.first_name} ${script.executedBy.last_name}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {script.executed_at ? new Date(script.executed_at).toLocaleString() : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {script.status !== 'in_progress' && (
                      <button 
                        onClick={() => confirmExecuteScript(script)}
                        disabled={executingScript === script.id}
                        className={`${
                          executingScript === script.id
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-primary-600 hover:text-primary-900'
                        } mr-4`}
                      >
                        {executingScript === script.id ? 'Executing...' : 'Execute'}
                      </button>
                    )}
                    {script.executed && (
                      <button 
                        onClick={() => viewScriptOutput(script)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View Output
                      </button>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Execute Confirmation Modal */}
      {showConfirmModal && selectedScript && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Confirm Script Execution</h3>
              <button 
                onClick={() => setShowConfirmModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <p className="mb-4">
                Are you sure you want to execute the script <strong>{selectedScript.name}</strong>?
              </p>
              {selectedScript.executed && (
                <p className="mb-4 text-sm text-orange-600 font-medium">
                  This script has already been executed. Re-executing it will run it again and update its output.
                </p>
              )}
              <p className="mb-4 text-sm text-gray-600">
                This will run the JavaScript file on the server and cannot be undone.
              </p>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowConfirmModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleExecuteScript}
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Execute Script
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Output Modal */}
      {showOutputModal && selectedScript && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Script Output: {selectedScript.name}</h3>
              <button 
                onClick={() => setShowOutputModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <div className="mb-4">
                <h4 className="text-md font-semibold mb-2">Output:</h4>
                <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60 text-sm">
                  {selectedScript.output || 'No output'}
                </pre>
              </div>
              {selectedScript.error_message && (
                <div className="mb-4">
                  <h4 className="text-md font-semibold mb-2 text-red-600">Error:</h4>
                  <pre className="bg-red-50 p-4 rounded overflow-auto max-h-60 text-sm text-red-600">
                    {selectedScript.error_message}
                  </pre>
                </div>
              )}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowOutputModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScriptExecutions;
