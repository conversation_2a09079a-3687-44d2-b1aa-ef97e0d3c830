import {useContext, useEffect, useState} from 'react';
import {Link, useNavigate} from 'react-router-dom';
import {AuthContext} from '../context/AuthContext';
import { redirectToFarmSubdomain } from '../utils/redirectUtils';
import { API_URL } from '../config';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [farmSubdomain, setFarmSubdomain] = useState<string | null>(null);
  const [farmName, setFarmName] = useState<string | null>(null);
  const [customLoginText, setCustomLoginText] = useState<string | null>(null);
  const [customLoginLogo, setCustomLoginLogo] = useState<string | null>(null);
  const [customerPortalEnabled, setCustomerPortalEnabled] = useState<boolean>(false);
  const [farmNotFound, setFarmNotFound] = useState<boolean>(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const { login, loginWithPasskey, isPasskeyAvailable, loading, error, clearError, user } = useContext(AuthContext);
  const [passkeySupported, setPasskeySupported] = useState<boolean>(false);
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    if (user) {
      // User is already logged in, redirect to dashboard
      navigate('/dashboard');
    }
  }, [user, navigate]);

  // Check if passkey authentication is supported
  useEffect(() => {
    const checkPasskeySupport = async () => {
      const supported = isPasskeyAvailable();
      setPasskeySupported(supported);
    };

    checkPasskeySupport();
  }, [isPasskeyAvailable]);

  // Extract subdomain from URL on component mount
  useEffect(() => {
    const hostname = window.location.hostname;
    const parts = hostname.split('.');

    // Check if we're on a subdomain
    if (parts.length > 2) {
      // Skip for other non-farm subdomains
      if (parts[0] === 'www' || parts[0] === 'api' || parts[0] === 'app') {
        return;
      }

      setFarmSubdomain(parts[0]);

      // Fetch farm details to get custom login text and logo
      const fetchFarmDetails = async () => {
        try {
          const response = await fetch(`${API_URL}/farms/by-subdomain/${parts[0]}`);
          if (response.ok) {
            const data = await response.json();
            setFarmName(data.name);
            setCustomLoginText(data.custom_login_text);
            setCustomLoginLogo(data.custom_login_logo);
            setCustomerPortalEnabled(data.customer_portal_enabled === true);
            setFarmNotFound(false);
          } else {
            // Farm not found for this subdomain
            setFarmNotFound(true);
          }
        } catch (error) {
          console.error('Error fetching farm details:', error);
          setFarmNotFound(true);
        }
      };

      fetchFarmDetails();
    }
  }, []);

  const handlePasskeyLogin = async () => {
    clearError();
    setLocalError(null);

    // If farm not found and we're on a subdomain, don't attempt login
    if (farmSubdomain && farmNotFound) {
      setLocalError('Farm not found for this subdomain. Please check the URL or contact support.');
      return;
    }

    try {
      const result = await loginWithPasskey();

      // Wait for the user state to be updated before redirecting
      setTimeout(() => {
        // Check if we need to redirect to a farm subdomain
        if (result.farmSubdomain) {
          // Use the redirectUtils function to handle the redirect logic
          const redirected = redirectToFarmSubdomain(result.farmSubdomain, '/dashboard', user);

          // If no redirect was performed, navigate to dashboard
          if (!redirected) {
            navigate('/dashboard');
          }
          return;
        }

        // If no farm subdomain, just navigate to dashboard
        navigate('/dashboard');
      }, 100); // Small delay to ensure state updates have completed
    } catch (err) {
      // Error is handled by the context
      console.error('Passkey login error:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    setLocalError(null);

    // If farm not found and we're on a subdomain, don't attempt login
    if (farmSubdomain && farmNotFound) {
      setLocalError('Farm not found for this subdomain. Please check the URL or contact support.');
      return;
    }

    try {
      // Pass the farmSubdomain to the login function
      const result = await login(email, password, farmSubdomain);

      if (result.requireTwoFactor) {
        // Redirect to 2FA verification page with userId and farmSubdomain
        navigate('/verify-2fa', {
          state: {
            userId: result.userId,
            farmSubdomain: result.farmSubdomain,
            farmId: result.farmId
          }
        });
      } else {
        // Wait for the user state to be updated before redirecting
        // This ensures the login is fully completed before any redirect happens
        setTimeout(() => {
          // Check if we need to redirect to a farm subdomain
          if (result.farmSubdomain) {
            // Use the redirectUtils function to handle the redirect logic
            const redirected = redirectToFarmSubdomain(result.farmSubdomain, '/dashboard', user);

            // If no redirect was performed, navigate to dashboard
            if (!redirected) {
              navigate('/dashboard');
            }
            return;
          }

          // If no farm subdomain, just navigate to dashboard
          navigate('/dashboard');
        }, 100); // Small delay to ensure state updates have completed
      }
    } catch (err) {
      // Error is handled by the context
      console.error('Login error:', err);
    }
  };

  // Determine the logo URL
  const logoUrl = customLoginLogo || '/logo.svg';

  // Determine the welcome text
  const welcomeText = farmName ? `Welcome to ${farmName}` : 'Welcome';

  // Determine the subtitle text
  const subtitleText = customLoginText || 'Sign in to your account to continue';

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
        <div className="flex flex-col items-center">
          <img
            src={logoUrl}
            alt={farmName ? `${farmName} Logo` : "nxtAcre Logo"}
            className="h-12 w-auto mb-4"
            onError={(e) => {
              // Fallback if logo doesn't exist
              const target = e.currentTarget;
              target.onerror = null;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                const span = document.createElement('span');
                span.className = 'text-3xl font-display font-bold text-primary-600';
                span.textContent = farmName || 'nxtAcre';
                parent.appendChild(span);
              }
            }}
          />
          <h2 className="text-center text-2xl font-display font-bold text-gray-900">
            {welcomeText}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {subtitleText}
          </p>
        </div>

        {farmNotFound && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">No farm was found at this address. Please check the URL and try again.</span>
          </div>
        )}

        {(error || localError) && !farmNotFound && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">{localError || error}</span>
          </div>
        )}

        {!farmNotFound && (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm">
                <Link to="/forgot-password" className="font-medium text-primary-600 hover:text-primary-700">
                  Forgot your password?
                </Link>
              </div>
            </div>

            <div className="space-y-4">
              <button
                type="submit"
                disabled={loading}
                className={`btn btn-primary w-full ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {loading ? 'Signing in...' : 'Sign in with password'}
              </button>

              {passkeySupported && (
                <button
                  type="button"
                  onClick={handlePasskeyLogin}
                  disabled={loading}
                  className={`flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 16V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 8H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  {loading ? 'Signing in...' : 'Sign in with Passkey'}
                </button>
              )}
            </div>

            <div className="text-center pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-4">
                Don't have an account?{' '}
                {farmSubdomain ? (
                  <Link to={`/register?farmSubdomain=${farmSubdomain}`} className="font-medium text-primary-600 hover:text-primary-700">
                    Sign up as a farm employee
                  </Link>
                ) : (
                  <Link to="/register" className="font-medium text-primary-600 hover:text-primary-700">
                    Sign up
                  </Link>
                )}
              </p>
              {!farmSubdomain && (
                <>
                  <p className="text-sm text-gray-600">
                    Are you a supplier, vendor, or vet?{' '}<br/>
                    <Link to="/business-register" className="font-medium text-primary-600 hover:text-primary-700">
                      Register a business account
                    </Link>
                  </p>

                  <div className="mt-4 p-4 bg-yellow-50 rounded-md border border-yellow-200">
                    <h3 className="text-sm font-medium text-yellow-800 mb-2">Looking for Customer Portal?</h3>
                    <p className="text-xs text-yellow-700">
                      If you're a customer trying to access invoices or make payments, please contact your farm directly to get the correct login link for their customer portal.
                    </p>
                  </div>
                </>
              )}

              {farmSubdomain && customerPortalEnabled && (
                <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-200">
                  <h3 className="text-sm font-medium text-blue-800 mb-2">Customer Portal Access</h3>
                  <p className="text-xs text-blue-700 mb-3">
                    If you're a customer looking to access invoices or make payments, please use the customer portal.
                  </p>
                  <div className="flex flex-col space-y-2">
                    <a 
                      href="/customer/login" 
                      className="text-sm text-center py-2 px-4 border border-blue-300 rounded-md text-blue-700 bg-white hover:bg-blue-50"
                    >
                      Customer Login
                    </a>
                    <a 
                      href="/customer/register" 
                      className="text-sm text-center py-2 px-4 border border-blue-300 rounded-md text-blue-700 bg-white hover:bg-blue-50"
                    >
                      Register as a Customer
                    </a>
                  </div>
                </div>
              )}
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default Login;
