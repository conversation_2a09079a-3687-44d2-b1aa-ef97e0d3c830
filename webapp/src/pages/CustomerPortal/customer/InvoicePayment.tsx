import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../../config';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

// Initialize Stripe with the publishable key from environment variables
const stripePromise = loadStripe(import.meta.env.VITE_REACT_APP_STRIPE_PUBLISHABLE_KEY || '');

interface Invoice {
  id: string;
  invoice_number: string;
  total_amount: number;
  status: string;
  farm_name: string;
  customer_pays_fees: boolean;
}

// Payment form component that uses Stripe Elements
const PaymentForm: React.FC<{
  invoice: Invoice;
  clientSecret: string;
  paymentMethod: 'card' | 'ach';
  processingFee: number;
  onPaymentMethodChange: (method: 'card' | 'ach') => void;
  onPaymentComplete: () => void;
}> = ({ invoice, clientSecret, paymentMethod, processingFee, onPaymentMethodChange, onPaymentComplete }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [bankName, setBankName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [routingNumber, setRoutingNumber] = useState('');
  const [accountHolderName, setAccountHolderName] = useState('');
  const [accountType, setAccountType] = useState<'individual' | 'company'>('individual');
  const navigate = useNavigate();

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      let result;

      if (paymentMethod === 'card') {
        const cardElement = elements.getElement(CardElement);

        if (!cardElement) {
          throw new Error('Card element not found');
        }

        result = await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: cardElement,
          },
        });
      } else {
        // ACH payment
        result = await stripe.confirmAchDebitPayment(clientSecret, {
          payment_method: {
            us_bank_account: {
              routing_number: routingNumber,
              account_number: accountNumber,
              account_holder_type: accountType,
              account_holder_name: accountHolderName,
            },
          },
        });
      }

      if (result.error) {
        throw new Error(result.error.message || 'Payment failed');
      }

      if (result.paymentIntent && result.paymentIntent.status === 'succeeded') {
        // Payment succeeded, call the API to mark the invoice as paid
        const token = localStorage.getItem('customerToken');
        await axios.post(
          `${API_URL}/customer/invoices/${invoice.id}/pay`,
          { 
            paymentIntentId: result.paymentIntent.id,
            paymentMethodId: result.paymentIntent.payment_method
          },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );

        onPaymentComplete();
      } else if (result.paymentIntent && result.paymentIntent.status === 'requires_confirmation') {
        // For ACH payments that require additional confirmation
        // This would typically redirect to a confirmation page
        navigate(`/customer/invoices/${invoice.id}/payment-confirmation?payment_intent=${result.paymentIntent.id}`);
      }
    } catch (err: any) {
      console.error('Payment error:', err);
      setError(err.message || 'An error occurred during payment processing. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const totalWithFees = invoice.total_amount + (invoice.customer_pays_fees ? processingFee : 0);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-md mb-4">
        <div className="flex justify-between mb-2">
          <span className="text-sm text-gray-500">Invoice Amount:</span>
          <span className="text-sm font-medium text-gray-900">
            ${invoice.total_amount.toFixed(2)}
          </span>
        </div>

        {invoice.customer_pays_fees && (
          <div className="flex justify-between mb-2">
            <span className="text-sm text-gray-500">Processing Fee:</span>
            <span className="text-sm text-gray-900">
              ${processingFee.toFixed(2)}
            </span>
          </div>
        )}

        <div className="flex justify-between pt-2 border-t border-gray-200">
          <span className="text-sm font-medium text-gray-900">Total:</span>
          <span className="text-sm font-medium text-gray-900">
            ${totalWithFees.toFixed(2)}
          </span>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex space-x-4 mb-4">
          <button
            type="button"
            onClick={() => onPaymentMethodChange('card')}
            className={`flex-1 py-2 px-4 border rounded-md text-sm font-medium ${
              paymentMethod === 'card'
                ? 'bg-primary-100 border-primary-300 text-primary-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Credit/Debit Card
          </button>
          <button
            type="button"
            onClick={() => onPaymentMethodChange('ach')}
            className={`flex-1 py-2 px-4 border rounded-md text-sm font-medium ${
              paymentMethod === 'ach'
                ? 'bg-primary-100 border-primary-300 text-primary-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Bank Account (ACH)
          </button>
        </div>

        {paymentMethod === 'card' ? (
          <div className="space-y-4">
            <label className="block text-sm font-medium text-gray-700">
              Card Details
            </label>
            <div className="p-3 border border-gray-300 rounded-md">
              <CardElement
                options={{
                  style: {
                    base: {
                      fontSize: '16px',
                      color: '#424770',
                      '::placeholder': {
                        color: '#aab7c4',
                      },
                    },
                    invalid: {
                      color: '#9e2146',
                    },
                  },
                }}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label htmlFor="accountHolderName" className="block text-sm font-medium text-gray-700">
                Account Holder Name
              </label>
              <input
                type="text"
                id="accountHolderName"
                value={accountHolderName}
                onChange={(e) => setAccountHolderName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            <div>
              <label htmlFor="routingNumber" className="block text-sm font-medium text-gray-700">
                Routing Number
              </label>
              <input
                type="text"
                id="routingNumber"
                value={routingNumber}
                onChange={(e) => setRoutingNumber(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            <div>
              <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700">
                Account Number
              </label>
              <input
                type="text"
                id="accountNumber"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            <div>
              <label htmlFor="bankName" className="block text-sm font-medium text-gray-700">
                Bank Name
              </label>
              <input
                type="text"
                id="bankName"
                value={bankName}
                onChange={(e) => setBankName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            <div>
              <label htmlFor="accountType" className="block text-sm font-medium text-gray-700">
                Account Type
              </label>
              <select
                id="accountType"
                value={accountType}
                onChange={(e) => setAccountType(e.target.value as 'individual' | 'company')}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              >
                <option value="individual">Personal</option>
                <option value="company">Business</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <Link
          to={`/customer/invoices/${invoice.id}`}
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </Link>
        <button
          type="submit"
          disabled={!stripe || processing}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
            (!stripe || processing) ? 'opacity-70 cursor-not-allowed' : ''
          }`}
        >
          {processing ? 'Processing...' : `Pay $${totalWithFees.toFixed(2)}`}
        </button>
      </div>
    </form>
  );
};

// Main payment page component
const CustomerInvoicePayment: React.FC = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'ach'>('card');
  const [processingFee, setProcessingFee] = useState(0);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchInvoiceAndCreatePaymentIntent = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('customerToken');
        if (!token) {
          navigate('/customer/login');
          return;
        }

        // Fetch invoice details
        const invoiceResponse = await axios.get(`${API_URL}/customer/invoices/${invoiceId}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        const invoiceData = invoiceResponse.data.invoice;
        setInvoice(invoiceData);

        // Check if invoice is already paid
        if (invoiceData.status.toLowerCase() === 'paid') {
          navigate(`/customer/invoices/${invoiceId}`);
          return;
        }

        // Create payment intent
        const paymentIntentResponse = await axios.post(
          `${API_URL}/customer/invoices/${invoiceId}/payment-intent`,
          { 
            paymentMethodType: paymentMethod === 'ach' ? 'ach_debit' : paymentMethod 
          },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );

        setClientSecret(paymentIntentResponse.data.clientSecret);
        setProcessingFee(paymentIntentResponse.data.processingFee || 0);
      } catch (err: any) {
        console.error('Error setting up payment:', err);
        setError(err.response?.data?.error || 'Failed to set up payment. Please try again later.');

        // If unauthorized, redirect to login
        if (err.response?.status === 401) {
          localStorage.removeItem('customerToken');
          navigate('/customer/login');
        }
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId) {
      fetchInvoiceAndCreatePaymentIntent();
    }
  }, [invoiceId, navigate, paymentMethod]);

  const handlePaymentMethodChange = async (method: 'card' | 'ach') => {
    setPaymentMethod(method);
    // This will trigger the useEffect to create a new payment intent with the new payment method
  };

  const handlePaymentComplete = () => {
    setPaymentComplete(true);
    // Redirect to the invoice page after a delay
    setTimeout(() => {
      navigate(`/customer/invoices/${invoiceId}`);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link to={`/customer/invoices/${invoiceId}`} className="text-primary-600 hover:text-primary-900 flex items-center">
            <svg className="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Invoice
          </Link>
        </div>

        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Pay Invoice
            </h3>
            {invoice && (
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Invoice #{invoice.invoice_number} from {invoice.farm_name}
              </p>
            )}
          </div>

          <div className="px-4 py-5 sm:px-6">
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="ml-2 text-sm text-gray-500">Setting up payment...</span>
              </div>
            ) : paymentComplete ? (
              <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-green-700">
                      Payment successful! Redirecting to invoice page...
                    </p>
                  </div>
                </div>
              </div>
            ) : invoice && clientSecret ? (
              <Elements stripe={stripePromise}>
                <PaymentForm
                  invoice={invoice}
                  clientSecret={clientSecret}
                  paymentMethod={paymentMethod}
                  processingFee={processingFee}
                  onPaymentMethodChange={handlePaymentMethodChange}
                  onPaymentComplete={handlePaymentComplete}
                />
              </Elements>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerInvoicePayment;
