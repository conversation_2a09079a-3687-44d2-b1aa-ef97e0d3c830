import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  createAlertRule, 
  updateAlertRule, 
  getAlertRules, 
  testAlertRule,
  AlertCondition, 
  AlertAction 
} from '../../services/alertService';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';

const AlertRuleForm: React.FC = () => {
  const { ruleId } = useParams<{ ruleId: string }>();
  const navigate = useNavigate();
  const { selectedFarm } = useFarm();
  const isEditMode = !!ruleId;

  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    enabled: boolean;
    conditions: AlertCondition[];
    actions: AlertAction[];
  }>({
    name: '',
    description: '',
    enabled: true,
    conditions: [{ 
      id: `condition-${Date.now()}`, 
      type: 'threshold', 
      parameter: '', 
      operator: '>', 
      value: '' 
    }],
    actions: [{ 
      id: `action-${Date.now()}`, 
      type: 'notification' 
    }]
  });

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isTesting, setIsTesting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<{ message: string; triggered: boolean } | null>(null);

  // Helper function to safely get string values
  const getStringValue = (value: any): string => {
    if (value === undefined || value === null) return '';
    return String(value);
  };

  // Helper function to handle schedule value changes
  const handleScheduleValueChange = (index: number, dayOrDate: string, time: string) => {
    handleConditionChange(index, 'value', `${dayOrDate},${time}`);
  };

  useEffect(() => {
    if (isEditMode && ruleId && selectedFarm) {
      fetchAlertRule();
    }
  }, [ruleId, selectedFarm]);

  const fetchAlertRule = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!selectedFarm) {
        setError('No farm selected');
        return;
      }
      const rules = await getAlertRules(selectedFarm.id);
      const rule = rules.find(r => r.id === ruleId);

      if (!rule) {
        setError('Alert rule not found');
        return;
      }

      setFormData({
        name: rule.name,
        description: rule.description || '',
        enabled: rule.enabled,
        conditions: rule.conditions,
        actions: rule.actions
      });
    } catch (err) {
      console.error('Error fetching alert rule:', err);
      setError('Failed to load alert rule. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleConditionChange = (index: number, field: string, value: string) => {
    setFormData(prev => {
      const newConditions = [...prev.conditions];
      newConditions[index] = { ...newConditions[index], [field]: value };
      return { ...prev, conditions: newConditions };
    });
  };

  const handleActionChange = (index: number, field: string, value: any) => {
    setFormData(prev => {
      const newActions = [...prev.actions];
      newActions[index] = { ...newActions[index], [field]: value };
      return { ...prev, actions: newActions };
    });
  };

  const addCondition = () => {
    setFormData(prev => ({
      ...prev,
      conditions: [
        ...prev.conditions,
        { 
          id: `condition-${Date.now()}`, 
          type: 'threshold', 
          parameter: '', 
          operator: '>', 
          value: '' 
        }
      ]
    }));
  };

  const removeCondition = (index: number) => {
    if (formData.conditions.length <= 1) {
      return; // Keep at least one condition
    }

    setFormData(prev => {
      const newConditions = [...prev.conditions];
      newConditions.splice(index, 1);
      return { ...prev, conditions: newConditions };
    });
  };

  const addAction = () => {
    setFormData(prev => ({
      ...prev,
      actions: [
        ...prev.actions,
        { 
          id: `action-${Date.now()}`, 
          type: 'notification' 
        }
      ]
    }));
  };

  const removeAction = (index: number) => {
    if (formData.actions.length <= 1) {
      return; // Keep at least one action
    }

    setFormData(prev => {
      const newActions = [...prev.actions];
      newActions.splice(index, 1);
      return { ...prev, actions: newActions };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFarm) {
      setError('Please select a farm first');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const ruleData = {
        ...formData,
        farmId: selectedFarm.id
      };

      if (isEditMode && ruleId) {
        await updateAlertRule(ruleId, ruleData);
      } else {
        await createAlertRule(ruleData);
      }

      navigate('/alerts/rules');
    } catch (err) {
      console.error('Error saving alert rule:', err);
      setError('Failed to save alert rule. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestRule = async () => {
    if (!selectedFarm) {
      setError('Please select a farm first');
      return;
    }

    try {
      setIsTesting(true);
      setError(null);
      setTestResult(null);

      const ruleData = {
        ...formData,
        farmId: selectedFarm.id
      };

      const result = await testAlertRule(ruleData);
      setTestResult(result);
    } catch (err) {
      console.error('Error testing alert rule:', err);
      setError('Failed to test alert rule. Please try again later.');
    } finally {
      setIsTesting(false);
    }
  };

  if (isLoading && isEditMode) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">
            {isEditMode ? 'Edit Alert Rule' : 'Create Alert Rule'}
          </h1>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {testResult && (
          <div className={`border px-4 py-3 rounded mb-4 ${
            testResult.triggered 
              ? 'bg-yellow-100 border-yellow-400 text-yellow-700' 
              : 'bg-green-100 border-green-400 text-green-700'
          }`}>
            <p>{testResult.message}</p>
          </div>
        )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
            Rule Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Enter rule name"
            required
          />
        </div>

        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Enter rule description"
            rows={3}
          />
        </div>

        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="enabled"
              checked={formData.enabled}
              onChange={handleCheckboxChange}
              className="mr-2"
            />
            <span className="text-gray-700 text-sm font-bold">Enabled</span>
          </label>
        </div>

        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-gray-700">Conditions</h3>
            <button
              type="button"
              onClick={addCondition}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              + Add Condition
            </button>
          </div>

          {formData.conditions.map((condition, index) => (
            <div key={condition.id} className="bg-gray-50 p-4 rounded mb-3">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">Condition {index + 1}</h4>
                <button
                  type="button"
                  onClick={() => removeCondition(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                  disabled={formData.conditions.length <= 1}
                >
                  Remove
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-1">
                    Type
                  </label>
                  <select
                    value={condition.type}
                    onChange={(e) => handleConditionChange(index, 'type', e.target.value)}
                    className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  >
                    <option value="threshold">Threshold</option>
                    <option value="schedule">Schedule</option>
                    <option value="status_change">Status Change</option>
                    <option value="weather">Weather</option>
                    <option value="inventory_level">Inventory Level</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    {condition.type === 'threshold' && 'Trigger when a value exceeds or falls below a threshold'}
                    {condition.type === 'schedule' && 'Trigger at specific times or on a schedule'}
                    {condition.type === 'status_change' && 'Trigger when a status changes'}
                    {condition.type === 'weather' && 'Trigger based on weather conditions'}
                    {condition.type === 'inventory_level' && 'Trigger based on inventory levels'}
                  </p>
                </div>

                {condition.type === 'threshold' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Parameter
                    </label>
                    <select
                      value={condition.parameter || ''}
                      onChange={(e) => handleConditionChange(index, 'parameter', e.target.value)}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value="">Select parameter</option>
                      <optgroup label="Equipment">
                        <option value="equipment.hours">Equipment Hours</option>
                        <option value="equipment.fuel_level">Fuel Level</option>
                        <option value="equipment.maintenance_due">Days Until Maintenance</option>
                      </optgroup>
                      <optgroup label="Crops">
                        <option value="crop.growth_stage">Growth Stage</option>
                        <option value="crop.days_to_harvest">Days to Harvest</option>
                      </optgroup>
                      <optgroup label="Financial">
                        <option value="financial.balance">Account Balance</option>
                        <option value="financial.expenses">Monthly Expenses</option>
                        <option value="financial.revenue">Monthly Revenue</option>
                      </optgroup>
                      <optgroup label="IoT Sensors">
                        <option value="iot.temperature">Temperature</option>
                        <option value="iot.humidity">Humidity</option>
                        <option value="iot.soil_moisture">Soil Moisture</option>
                      </optgroup>
                    </select>
                  </div>
                )}

                {condition.type === 'status_change' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Parameter
                    </label>
                    <select
                      value={condition.parameter || ''}
                      onChange={(e) => handleConditionChange(index, 'parameter', e.target.value)}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value="">Select parameter</option>
                      <option value="equipment.status">Equipment Status</option>
                      <option value="task.status">Task Status</option>
                      <option value="crop.status">Crop Status</option>
                      <option value="invoice.status">Invoice Status</option>
                    </select>
                  </div>
                )}

                {condition.type === 'weather' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Parameter
                    </label>
                    <select
                      value={condition.parameter || ''}
                      onChange={(e) => handleConditionChange(index, 'parameter', e.target.value)}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value="">Select parameter</option>
                      <option value="temperature">Temperature</option>
                      <option value="humidity">Humidity</option>
                      <option value="precipitation">Precipitation</option>
                      <option value="windSpeed">Wind Speed</option>
                      <option value="forecast">Forecast</option>
                    </select>
                  </div>
                )}

                {condition.type === 'inventory_level' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Parameter
                    </label>
                    <select
                      value={condition.parameter || ''}
                      onChange={(e) => handleConditionChange(index, 'parameter', e.target.value)}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value="">Select inventory item</option>
                      <optgroup label="Seeds">
                        <option value="seed.corn">Corn Seed</option>
                        <option value="seed.soybean">Soybean Seed</option>
                        <option value="seed.wheat">Wheat Seed</option>
                      </optgroup>
                      <optgroup label="Chemicals">
                        <option value="chemical.herbicide">Herbicide</option>
                        <option value="chemical.fungicide">Fungicide</option>
                        <option value="chemical.insecticide">Insecticide</option>
                      </optgroup>
                      <optgroup label="Fertilizers">
                        <option value="fertilizer.nitrogen">Nitrogen</option>
                        <option value="fertilizer.phosphorus">Phosphorus</option>
                        <option value="fertilizer.potassium">Potassium</option>
                      </optgroup>
                      <optgroup label="Fuel">
                        <option value="fuel.diesel">Diesel</option>
                        <option value="fuel.gasoline">Gasoline</option>
                      </optgroup>
                    </select>
                  </div>
                )}

                {condition.type === 'schedule' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Schedule Type
                    </label>
                    <select
                      value={condition.parameter || 'daily'}
                      onChange={(e) => handleConditionChange(index, 'parameter', e.target.value)}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                )}

                {condition.type !== 'schedule' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Operator
                    </label>
                    <select
                      value={condition.operator}
                      onChange={(e) => handleConditionChange(index, 'operator', e.target.value)}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value=">">Greater than (&gt;)</option>
                      <option value="<">Less than (&lt;)</option>
                      <option value="=">Equal to (=)</option>
                      <option value=">=">Greater than or equal to (≥)</option>
                      <option value="<=">Less than or equal to (≤)</option>
                      <option value="contains">Contains</option>
                      <option value="not_contains">Does not contain</option>
                    </select>
                  </div>
                )}

                {condition.type === 'schedule' && condition.parameter === 'weekly' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Day of Week
                    </label>
                    <select
                      value={getStringValue(condition.value?.split(',')[0] || '0')}
                      onChange={(e) => {
                        const time = getStringValue(condition.value?.split(',')[1] || '12:00');
                        handleScheduleValueChange(index, e.target.value, time);
                      }}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      <option value="0">Sunday</option>
                      <option value="1">Monday</option>
                      <option value="2">Tuesday</option>
                      <option value="3">Wednesday</option>
                      <option value="4">Thursday</option>
                      <option value="5">Friday</option>
                      <option value="6">Saturday</option>
                    </select>
                  </div>
                )}

                {condition.type === 'schedule' && condition.parameter === 'monthly' && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Day of Month
                    </label>
                    <select
                      value={getStringValue(condition.value?.split(',')[0] || '1')}
                      onChange={(e) => {
                        const time = getStringValue(condition.value?.split(',')[1] || '12:00');
                        handleScheduleValueChange(index, e.target.value, time);
                      }}
                      className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    >
                      {[...Array(31)].map((_, i) => (
                        <option key={i + 1} value={i + 1}>{i + 1}</option>
                      ))}
                    </select>
                  </div>
                )}

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-1">
                    {condition.type === 'schedule' ? 'Time' : 'Value'}
                  </label>
                  {condition.type === 'schedule' ? (
                    <input
                      type="time"
                      value={
                        condition.parameter === 'daily' 
                          ? getStringValue(condition.value || '12:00')
                          : getStringValue(condition.value?.split(',')[1] || '12:00')
                      }
                      onChange={(e) => {
                        if (condition.parameter === 'daily') {
                          handleConditionChange(index, 'value', e.target.value);
                        } else if (condition.parameter === 'weekly') {
                          const day = getStringValue(condition.value?.split(',')[0] || '0');
                          handleScheduleValueChange(index, day, e.target.value);
                        } else if (condition.parameter === 'monthly') {
                          const date = getStringValue(condition.value?.split(',')[0] || '1');
                          handleScheduleValueChange(index, date, e.target.value);
                        }
                      }}
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    />
                  ) : (
                    <input
                      type="text"
                      value={condition.value || ''}
                      onChange={(e) => handleConditionChange(index, 'value', e.target.value)}
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      placeholder="Value"
                    />
                  )}
                </div>
              </div>

              {/* Help text for the condition */}
              <div className="mt-2 text-sm text-gray-600 bg-blue-50 p-2 rounded">
                {condition.type === 'threshold' && (
                  <p>This condition will trigger when the selected parameter {condition.operator} {condition.value || '[value]'}.</p>
                )}
                {condition.type === 'schedule' && condition.parameter === 'daily' && (
                  <p>This condition will trigger daily at {condition.value || '12:00'}.</p>
                )}
                {condition.type === 'schedule' && condition.parameter === 'weekly' && (
                  <p>This condition will trigger every {
                    ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][Number(getStringValue(condition.value?.split(',')[0] || '0'))]
                  } at {getStringValue(condition.value?.split(',')[1] || '12:00')}.</p>
                )}
                {condition.type === 'schedule' && condition.parameter === 'monthly' && (
                  <p>This condition will trigger on day {condition.value?.split(',')[0] || '1'} of each month at {condition.value?.split(',')[1] || '12:00'}.</p>
                )}
                {condition.type === 'status_change' && (
                  <p>This condition will trigger when the status of {condition.parameter || '[parameter]'} {condition.operator} {condition.value || '[value]'}.</p>
                )}
                {condition.type === 'weather' && (
                  <p>This condition will trigger when the weather {condition.parameter || '[parameter]'} {condition.operator} {condition.value || '[value]'}.</p>
                )}
                {condition.type === 'inventory_level' && (
                  <p>This condition will trigger when the inventory level for {condition.parameter || '[parameter]'} {condition.operator} {condition.value || '[value]'}.</p>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-gray-700">Actions</h3>
            <button
              type="button"
              onClick={addAction}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              + Add Action
            </button>
          </div>

          {formData.actions.map((action, index) => (
            <div key={action.id} className="bg-gray-50 p-4 rounded mb-3">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">Action {index + 1}</h4>
                <button
                  type="button"
                  onClick={() => removeAction(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                  disabled={formData.actions.length <= 1}
                >
                  Remove
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-1">
                    Type
                  </label>
                  <select
                    value={action.type}
                    onChange={(e) => handleActionChange(index, 'type', e.target.value)}
                    className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  >
                    <option value="notification">In-app Notification</option>
                    <option value="email">Email</option>
                    <option value="sms">SMS</option>
                    <option value="task_creation">Create Task</option>
                  </select>
                </div>

                {(action.type === 'email' || action.type === 'sms') && (
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-1">
                      Recipients (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={action.recipients?.join(', ') || ''}
                      onChange={(e) => handleActionChange(
                        index, 
                        'recipients', 
                        e.target.value.split(',').map(r => r.trim())
                      )}
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      placeholder="<EMAIL>, <EMAIL>"
                    />
                  </div>
                )}

                {action.type === 'task_creation' && (
                  <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-gray-700 text-sm font-bold mb-1">
                        Task Title
                      </label>
                      <input
                        type="text"
                        value={action.taskDetails?.title || ''}
                        onChange={(e) => {
                          const taskDetails = action.taskDetails || {};
                          handleActionChange(
                            index, 
                            'taskDetails', 
                            { ...taskDetails, title: e.target.value }
                          );
                        }}
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        placeholder="Task title"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 text-sm font-bold mb-1">
                        Priority
                      </label>
                      <select
                        value={action.taskDetails?.priority || 'medium'}
                        onChange={(e) => {
                          const taskDetails = action.taskDetails || {};
                          handleActionChange(
                            index, 
                            'taskDetails', 
                            { ...taskDetails, priority: e.target.value }
                          );
                        }}
                        className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-gray-700 text-sm font-bold mb-1">
                        Due Date
                      </label>
                      <input
                        type="date"
                        value={action.taskDetails?.dueDate || new Date().toISOString().split('T')[0]}
                        onChange={(e) => {
                          const taskDetails = action.taskDetails || {};
                          handleActionChange(
                            index, 
                            'taskDetails', 
                            { ...taskDetails, dueDate: e.target.value }
                          );
                        }}
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-gray-700 text-sm font-bold mb-1">
                        Description
                      </label>
                      <textarea
                        value={action.taskDetails?.description || ''}
                        onChange={(e) => {
                          const taskDetails = action.taskDetails || {};
                          handleActionChange(
                            index, 
                            'taskDetails', 
                            { ...taskDetails, description: e.target.value }
                          );
                        }}
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        placeholder="Task description"
                        rows={2}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-between">
          <div>
            <button
              type="button"
              onClick={handleTestRule}
              disabled={isLoading || isTesting}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2 disabled:opacity-50"
            >
              {isTesting ? 'Testing...' : 'Test Rule'}
            </button>
            <button
              type="button"
              onClick={() => navigate('/alerts/rules')}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            >
              Cancel
            </button>
          </div>
          <button
            type="submit"
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {isLoading ? 'Saving...' : 'Save Rule'}
          </button>
        </div>
      </form>
      </div>
    </Layout>
  );
};

export default AlertRuleForm;
