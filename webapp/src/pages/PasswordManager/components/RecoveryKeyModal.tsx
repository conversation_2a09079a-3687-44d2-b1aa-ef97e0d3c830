import React, { useState } from 'react';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Alert } from '../../../components/ui/Alert';
import { Spinner } from '../../../components/ui/Spinner';
import { FaKey, FaCopy, FaCheck } from 'react-icons/fa';

interface RecoveryKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerated: () => void;
}

const RecoveryKeyModal: React.FC<RecoveryKeyModalProps> = ({
  isOpen,
  onClose,
  onGenerated,
}) => {
  const [masterPassword, setMasterPassword] = useState('');
  const [confirmMasterPassword, setConfirmMasterPassword] = useState('');
  const [recoveryKey, setRecoveryKey] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [confirmed, setConfirmed] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!masterPassword) {
      setError('Master password is required');
      return;
    }

    if (masterPassword !== confirmMasterPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/password-manager/recovery-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ masterPassword }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate recovery key');
      }

      const data = await response.json();
      setRecoveryKey(data.data.recoveryKey);
    } catch (error) {
      console.error('Error generating recovery key:', error);
      setError('Failed to generate recovery key. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyRecoveryKey = () => {
    if (recoveryKey) {
      navigator.clipboard.writeText(recoveryKey);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleConfirm = () => {
    setConfirmed(true);
    onGenerated();
  };

  const handleClose = () => {
    // Reset state when closing the modal
    setMasterPassword('');
    setConfirmMasterPassword('');
    setRecoveryKey(null);
    setError(null);
    setCopied(false);
    setConfirmed(false);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Generate Recovery Key"
      size="md"
      closeOnClickOutside={false}
    >
      {!recoveryKey ? (
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              A recovery key allows you to access your passwords if you forget your master password.
              You will need to confirm your master password to generate a recovery key.
            </p>

            <div className="mb-4">
              <label htmlFor="masterPassword" className="block font-medium text-gray-700 mb-1">
                Master Password
              </label>
              <Input
                id="masterPassword"
                type="password"
                value={masterPassword}
                onChange={(e) => setMasterPassword(e.target.value)}
                placeholder="Enter your master password"
                autoFocus
              />
            </div>

            <div className="mb-4">
              <label htmlFor="confirmMasterPassword" className="block font-medium text-gray-700 mb-1">
                Confirm Master Password
              </label>
              <Input
                id="confirmMasterPassword"
                type="password"
                value={confirmMasterPassword}
                onChange={(e) => setConfirmMasterPassword(e.target.value)}
                placeholder="Confirm your master password"
              />
            </div>

            {error && (
              <Alert
                type="error"
                title="Error"
                message={error}
                className="mb-4"
              />
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={!masterPassword || !confirmMasterPassword || loading}
            >
              {loading ? <Spinner size="sm" className="mr-2" /> : <FaKey className="mr-2" />}
              Generate Recovery Key
            </Button>
          </div>
        </form>
      ) : (
        <div>
          <Alert
            type="warning"
            title="Important"
            message="Save this recovery key in a secure location. It will only be shown once and cannot be retrieved later."
            className="mb-4"
          />

          <div className="mb-6">
            <label className="block font-medium text-gray-700 mb-2">
              Your Recovery Key
            </label>
            <div className="relative">
              <Input
                type="text"
                value={recoveryKey}
                readOnly
                className="pr-10 font-mono text-sm"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={handleCopyRecoveryKey}
                title="Copy to clipboard"
              >
                {copied ? (
                  <FaCheck className="text-green-500" />
                ) : (
                  <FaCopy className="text-gray-500" />
                )}
              </button>
            </div>
            {copied && (
              <p className="text-green-500 text-sm mt-1">Copied to clipboard!</p>
            )}
          </div>

          <div className="mb-6">
            <p className="text-gray-600">
              Store this key somewhere safe, such as a password manager or a secure physical location.
              You will need this key if you ever forget your master password.
            </p>
          </div>

          <div className="flex justify-end">
            <Button
              type="button"
              variant="primary"
              onClick={handleConfirm}
            >
              I've Saved My Recovery Key
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default RecoveryKeyModal;
