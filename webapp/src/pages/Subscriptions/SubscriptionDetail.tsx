import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';
import {
  cancelSubscription, 
  getInvoices, 
  Invoice 
} from '../../services/paymentService';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: any;
  max_farms: number;
  max_users: number;
  is_active: boolean;
}

interface SubscriptionTransaction {
  id: string;
  farm_id: string;
  subscription_plan_id: string;
  amount: number;
  currency: string;
  status: string;
  payment_method: string;
  payment_reference: string;
  transaction_date: string;
  billing_period_start: string;
  billing_period_end: string;
  SubscriptionPlan?: SubscriptionPlan;
}

interface Farm {
  id: string;
  name: string;
  subscription_plan_id: string;
  subscription_status: string;
  subscription_start_date: string;
  subscription_end_date: string;
  billing_email: string;
  billing_address: string;
  billing_city: string;
  billing_state: string;
  billing_zip_code: string;
  billing_country: string;
  payment_method_id: string;
  stripe_customer_id: string;
  SubscriptionPlan?: SubscriptionPlan;
}

const SubscriptionDetail = () => {
  const { planId } = useParams<{ planId: string }>();
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null);
  const [farm, setFarm] = useState<Farm | null>(null);
  const [transactions, setTransactions] = useState<SubscriptionTransaction[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancelingSubscription, setCancelingSubscription] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [cancelAtPeriodEnd, setCancelAtPeriodEnd] = useState(true);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch subscription plan
        const planResponse = await axios.get(`${API_URL}/subscriptions/plans/${planId}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        setPlan(planResponse.data.plan || null);

        // Fetch farm information if user has a farm
        if (user.farm_id) {
          const farmResponse = await axios.get(`${API_URL}/farms/${user.farm_id}`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setFarm(farmResponse.data.farm || null);

          // Fetch subscription transactions
          const transactionsResponse = await axios.get(`${API_URL}/subscriptions/transactions/farm/${user.farm_id}`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          setTransactions(transactionsResponse.data.transactions || []);

          // Fetch invoices
          try {
            const fetchedInvoices = await getInvoices(user.farm_id);
            setInvoices(fetchedInvoices);
          } catch (invoiceErr) {
            console.error('Error fetching invoices:', invoiceErr);
            // Don't fail the whole request if invoices can't be fetched
          }
        }
      } catch (err: any) {
        console.error('Error fetching subscription data:', err);
        setError(err.response?.data?.error || 'Failed to load subscription information');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, navigate, planId]);

  const handleCancelSubscription = () => {
    setShowCancelConfirmation(true);
  };

  const confirmCancelSubscription = async () => {
    try {
      if (!user?.farm_id) return;

      setCancelingSubscription(true);
      setError(null);

      // Call the cancelSubscription function from the payment service
      await cancelSubscription(user.farm_id, cancelAtPeriodEnd);

      // Refresh farm data
      const farmResponse = await axios.get(`${API_URL}/farms/${user.farm_id}`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      setFarm(farmResponse.data.farm || null);
      setShowCancelConfirmation(false);
      setCancelingSubscription(false);

      // Show success message
      alert(cancelAtPeriodEnd 
        ? 'Your subscription will be canceled at the end of the current billing period.' 
        : 'Your subscription has been canceled immediately.');
    } catch (err: any) {
      console.error('Error canceling subscription:', err);
      setError(err.response?.data?.error || 'Failed to cancel subscription');
      setCancelingSubscription(false);
    }
  };

  const formatPrice = (price: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Subscription Details</h1>
        <Link
          to="/subscriptions"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Plans
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading subscription details...</p>
        </div>
      ) : !plan || !farm ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Subscription not found</h3>
          <p className="text-gray-500 mb-6">The subscription plan you're looking for doesn't exist or you don't have access to it.</p>
          <Link
            to="/subscriptions"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            View Available Plans
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Subscription Overview */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Subscription Overview</h3>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Plan</dt>
                  <dd className="mt-1 text-lg font-semibold text-gray-900">{plan.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      farm.subscription_status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : farm.subscription_status === 'canceled'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {farm.subscription_status.charAt(0).toUpperCase() + farm.subscription_status.slice(1)}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Start Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {farm.subscription_start_date ? formatDate(farm.subscription_start_date) : 'N/A'}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Renewal Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {farm.subscription_end_date ? formatDate(farm.subscription_end_date) : 'N/A'}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Monthly Price</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatPrice(plan.price_monthly)}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Yearly Price</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatPrice(plan.price_yearly)}</dd>
                </div>
              </dl>
            </div>
            <div className="border-t border-gray-200 px-4 py-4 sm:px-6 bg-gray-50">
              <div className="flex justify-end">
                <button
                  onClick={handleCancelSubscription}
                  disabled={farm.subscription_status === 'canceled'}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white 
                    ${farm.subscription_status === 'canceled' 
                      ? 'bg-gray-400 cursor-not-allowed' 
                      : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'}`}
                >
                  {farm.subscription_status === 'canceled' ? 'Subscription Canceled' : 'Cancel Subscription'}
                </button>
              </div>
            </div>
          </div>

          {/* Plan Features */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Plan Features</h3>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              <ul className="space-y-3">
                {plan.features && Object.entries(plan.features).map(([key, value]) => (
                  <li key={key} className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-gray-600">
                      {key}: {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                    </span>
                  </li>
                ))}
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-600">Valid for 1 farm</span>
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-600">Up to {plan.max_users} users</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Billing Information */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Billing Information</h3>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900">{farm.billing_email || 'Not set'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Payment Method</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {farm.payment_method_id ? 'Credit Card (ending in *****)' : 'Not set'}
                  </dd>
                </div>
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Billing Address</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {farm.billing_address ? (
                      <>
                        {farm.billing_address}<br />
                        {farm.billing_city}, {farm.billing_state} {farm.billing_zip_code}<br />
                        {farm.billing_country}
                      </>
                    ) : (
                      'Not set'
                    )}
                  </dd>
                </div>
              </dl>
            </div>
            <div className="border-t border-gray-200 px-4 py-4 sm:px-6 bg-gray-50">
              <div className="flex justify-end">
                <button
                  onClick={() => alert('This would open a form to update billing information')}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Update Billing Info
                </button>
              </div>
            </div>
          </div>

          {/* Invoices */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 flex justify-between items-center">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Invoices</h3>
              <span className="text-sm text-gray-500">View and download your invoices</span>
            </div>
            <div className="border-t border-gray-200">
              {invoices.length === 0 ? (
                <div className="px-4 py-5 sm:p-6 text-center">
                  <p className="text-sm text-gray-500">No invoices found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {invoices.map((invoice) => (
                        <tr key={invoice.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(invoice.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatPrice(invoice.amount, invoice.currency)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              invoice.status === 'paid' 
                                ? 'bg-green-100 text-green-800' 
                                : invoice.status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {invoice.pdfUrl ? (
                              <a 
                                href={invoice.pdfUrl} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-primary-600 hover:text-primary-900"
                              >
                                Download PDF
                              </a>
                            ) : (
                              <span className="text-gray-400">No PDF available</span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>

          {/* Transaction History */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Transaction History</h3>
            </div>
            <div className="border-t border-gray-200">
              {transactions.length === 0 ? (
                <div className="px-4 py-5 sm:p-6 text-center">
                  <p className="text-sm text-gray-500">No transactions found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Period
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {transactions.map((transaction) => (
                        <tr key={transaction.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(transaction.transaction_date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatPrice(transaction.amount, transaction.currency)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              transaction.status === 'succeeded' 
                                ? 'bg-green-100 text-green-800' 
                                : transaction.status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {transaction.billing_period_start && transaction.billing_period_end ? (
                              <>
                                {formatDate(transaction.billing_period_start)} - {formatDate(transaction.billing_period_end)}
                              </>
                            ) : (
                              'N/A'
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Cancel Subscription Confirmation Modal */}
      {showCancelConfirmation && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-semibold">Cancel Subscription</h3>
              <button 
                onClick={() => setShowCancelConfirmation(false)}
                disabled={cancelingSubscription}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-5">
              <div className="mb-4">
                <svg className="h-12 w-12 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-2">Are you sure you want to cancel?</h3>
                <p className="text-sm text-gray-500 text-center">
                  This will cancel your subscription to the {plan?.name} plan.
                </p>
              </div>

              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={cancelAtPeriodEnd}
                    onChange={(e) => setCancelAtPeriodEnd(e.target.checked)}
                    className="form-checkbox h-5 w-5 text-primary-600"
                    disabled={cancelingSubscription}
                  />
                  <span className="ml-2 text-gray-700">Cancel at the end of the billing period</span>
                </label>
                <p className="text-xs text-gray-500 mt-1 ml-7">
                  {cancelAtPeriodEnd 
                    ? 'Your subscription will remain active until the end of the current billing period.' 
                    : 'Your subscription will be canceled immediately and you will lose access to premium features.'}
                </p>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowCancelConfirmation(false)}
                  disabled={cancelingSubscription}
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Keep Subscription
                </button>
                <button
                  onClick={confirmCancelSubscription}
                  disabled={cancelingSubscription}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  {cancelingSubscription ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Canceling...
                    </>
                  ) : (
                    'Cancel Subscription'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default SubscriptionDetail;
