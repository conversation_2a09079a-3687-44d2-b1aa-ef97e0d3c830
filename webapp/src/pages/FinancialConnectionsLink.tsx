import { useState, useContext, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, useStripe } from '@stripe/react-stripe-js';
import { AuthContext } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';
import Layout from '../components/Layout';
import { createFinancialConnectionsSession, handleFinancialConnectionsCallback } from '../services/stripeFinancialConnectionsService';
import { STRIPE_PUBLISHABLE_KEY } from '../config';

const FinancialConnectionsForm = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [institutionName, setInstitutionName] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();
  const location = useLocation();
  const stripe = useStripe();

  // Check for callback parameters in URL
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const sessionIdParam = queryParams.get('session_id');

    if (sessionIdParam && currentFarm) {
      handleCallback(sessionIdParam);
    }
  }, [location, currentFarm]);

  // Handle callback from Stripe Financial Connections
  const handleCallback = async (sessionIdParam: string) => {
    try {
      setLoading(true);
      setError(null);

      if (!currentFarm) {
        setError('No farm selected. Please select a farm to connect a bank account.');
        setLoading(false);
        return;
      }

      // Handle the callback
      const result = await handleFinancialConnectionsCallback(
        sessionIdParam,
        currentFarm.id
      );

      setSuccess(true);
      setInstitutionName('Your bank'); // Stripe doesn't always provide institution name in the callback

      // Redirect to transactions page after a short delay
      setTimeout(() => {
        navigate('/transactions');
      }, 3000);
    } catch (err: any) {
      console.error('Error handling callback:', err);
      setError(err.response?.data?.error || 'Failed to link account');
    } finally {
      setLoading(false);
    }
  };

  // Fetch a new session
  const fetchNewSession = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!currentFarm || !user) {
        setError('Unable to refresh session: Farm or user information is missing');
        return false;
      }

      const returnUrl = `${window.location.origin}${window.location.pathname}`;
      const response = await createFinancialConnectionsSession(currentFarm.id, user.id, returnUrl);

      setSessionId(response.session_id);
      setClientSecret(response.client_secret);
      return true;
    } catch (err: any) {
      console.error('Error refreshing Financial Connections session:', err);
      setError(err.response?.data?.error || 'Failed to refresh session');
      return false;
    } finally {
      setLoading(false);
    }
  }, [currentFarm, user, setLoading, setError, setSessionId, setClientSecret]);

  // Create a Financial Connections session when the component mounts
  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    // Check if farm is selected
    if (!currentFarm) {
      setError('Please select a farm to connect a bank account');
      setLoading(false);
      return;
    }

    // Don't create a new session if we're handling a callback
    if (location.search.includes('session_id')) {
      return;
    }

    const fetchSession = async () => {
      try {
        setLoading(true);
        setError(null);

        const returnUrl = `${window.location.origin}${window.location.pathname}`;
        const response = await createFinancialConnectionsSession(currentFarm.id, user.id, returnUrl);

        setSessionId(response.session_id);
        setClientSecret(response.client_secret);
      } catch (err: any) {
        console.error('Error creating Financial Connections session:', err);
        setError(err.response?.data?.error || 'Failed to create session');
      } finally {
        setLoading(false);
      }
    };

    fetchSession();
  }, [user, currentFarm, navigate, location.search]);

  // Add a listener for Stripe errors that might occur during the session
  useEffect(() => {
    if (!stripe || !clientSecret) return;

    // This function will be called if the session expires while the component is mounted
    const checkSessionValidity = async () => {
      try {
        // Try to use the session (this will fail if it's expired)
        const { error } = await stripe.collectFinancialConnectionsAccounts({
          clientSecret: clientSecret,
          returnURL: window.location.href,
          // We're just checking validity, not actually opening the modal
          expand: ['institution'],
        });

        if (error && error.message && error.message.includes('expired')) {
          console.log('Session expired during component lifecycle, refreshing...');
          fetchNewSession();
        }
      } catch (err) {
        // Ignore errors here as we're just proactively checking
      }
    };

    // Check session validity after a delay (e.g., 4 minutes)
    // This helps catch expiration before the user clicks the button
    const timer = setTimeout(checkSessionValidity, 4 * 60 * 1000);

    return () => clearTimeout(timer);
  }, [stripe, clientSecret, fetchNewSession]);

  // Handle the button click to open Stripe Financial Connections
  const handleLinkAccount = async () => {
    if (!stripe || !clientSecret) {
      setError('Stripe is not initialized or session is not ready');
      return;
    }

    try {
      setLoading(true);

      // Open the Financial Connections modal
      const { error } = await stripe.collectFinancialConnectionsAccounts({
        clientSecret: clientSecret,
      });

      if (error) {
        console.error('Error collecting financial connections accounts:', error);

        // Check if the error is due to an expired session
        if (error.message && error.message.includes('expired')) {
          setError('Session expired. Refreshing...');

          // Fetch a new session
          const refreshed = await fetchNewSession();

          if (refreshed && stripe && clientSecret) {
            // Try again with the new session
            setTimeout(() => {
              handleLinkAccount();
            }, 1000); // Small delay to ensure the UI updates
            return;
          }
        } else {
          setError(error.message);
        }
      }
    } catch (err: any) {
      console.error('Error opening Financial Connections:', err);
      setError('Failed to open bank connection interface');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="p-6">
            <div className="flex items-center justify-center mb-4">
              <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <h2 className="text-2xl font-bold text-center text-gray-800 mb-2">Account Connected</h2>
            <p className="text-center text-gray-600 mb-6">
              Your bank account has been successfully linked to nxtAcre.
            </p>
            <p className="text-center text-sm text-gray-500">
              Redirecting to transactions page...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto">
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Connect Your Bank Account</h2>
          <p className="text-gray-600 mb-6">
            Link your bank account to automatically import transactions
          </p>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
              <span className="block sm:inline">{error}</span>
            </div>
          )}

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-2">Why connect your bank?</h3>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Automatically import transactions</li>
              <li>Save time on manual data entry</li>
              <li>Keep your financial records up to date</li>
              <li>Get insights into your farm's finances</li>
            </ul>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-2">Is it secure?</h3>
            <p className="text-sm text-gray-600">
              We use Stripe to securely connect to your bank. Your credentials are never stored on our servers, and all data is encrypted.
            </p>
          </div>

          <button
            onClick={handleLinkAccount}
            disabled={loading || !clientSecret || !stripe}
            className={`w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(loading || !clientSecret || !stripe) ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {loading ? 'Loading...' : 'Connect Bank Account'}
          </button>

          <p className="text-xs text-center text-gray-500 mt-4">
            By connecting your account, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </div>
  );
};

const FinancialConnectionsLink = () => {
  // Initialize Stripe with the publishable key from config
  const [stripePromise, setStripePromise] = useState<Promise<any> | null>(
    loadStripe(STRIPE_PUBLISHABLE_KEY)
  );
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  return (
    <Layout>
      {stripePromise ? (
        <Elements stripe={stripePromise}>
          <FinancialConnectionsForm />
        </Elements>
      ) : (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      )}
    </Layout>
  );
};

export default FinancialConnectionsLink;
