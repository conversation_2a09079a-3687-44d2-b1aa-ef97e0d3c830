import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import CameraForm from '../../components/Camera/CameraForm';
import { getIoTDevice, createIoTDevice, updateIoTDevice } from '../../services/iotService';

interface IoTDeviceFormData {
  farm_id: string;
  name: string;
  device_type: string;
  manufacturer: string;
  model: string;
  serial_number: string;
  firmware_version: string;
  location_description: string;
  latitude: number | null;
  longitude: number | null;
  status: string;
  configuration: any | null;
}

const IoTDeviceForm = () => {
  const { deviceId } = useParams<{ deviceId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!deviceId;

  const [device, setDevice] = useState<IoTDeviceFormData>({
    farm_id: '',
    name: '',
    device_type: '',
    manufacturer: '',
    model: '',
    serial_number: '',
    firmware_version: '',
    location_description: '',
    latitude: null,
    longitude: null,
    status: 'active',
    configuration: null
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Use selectedFarm from FarmContext
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      // Set the selected farm for new devices
      setDevice(prev => ({ ...prev, farm_id: selectedFarm.id }));
    }
  }, [selectedFarm, isEditMode]);

  // Fetch device data if in edit mode
  useEffect(() => {
    const fetchDevice = async () => {
      if (!deviceId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await getIoTDevice(deviceId);

        // Map the response data to the form data structure
        const formattedData: IoTDeviceFormData = {
          farm_id: response.device.farm_id,
          name: response.device.name,
          device_type: response.device.device_type,
          manufacturer: response.device.manufacturer || '',
          model: response.device.model || '',
          serial_number: response.device.serial_number || '',
          firmware_version: response.device.firmware_version || '',
          location_description: response.device.location_description || '',
          latitude: response.device.latitude,
          longitude: response.device.longitude,
          status: response.device.status,
          configuration: response.device.configuration
        };

        setDevice(formattedData);
      } catch (err: any) {
        console.error('Error fetching IoT device:', err);
        setError('Failed to load IoT device data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchDevice();
    }
  }, [deviceId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Handle numeric values
    if (name === 'latitude' || name === 'longitude') {
      setDevice(prev => ({ 
        ...prev, 
        [name]: value === '' ? null : parseFloat(value) 
      }));
    } else {
      setDevice(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!selectedFarm || !device.name || !device.device_type) {
        setError('Farm, device name, and device type are required.');
        setLoading(false);
        return;
      }

      // Prepare data for API
      const deviceData = {
        farmId: selectedFarm.id,
        name: device.name,
        deviceType: device.device_type,
        manufacturer: device.manufacturer || undefined,
        model: device.model || undefined,
        serialNumber: device.serial_number || undefined,
        firmwareVersion: device.firmware_version || undefined,
        locationDescription: device.location_description || undefined,
        latitude: device.latitude || undefined,
        longitude: device.longitude || undefined,
        status: device.status,
        configuration: device.configuration || undefined
      };

      if (isEditMode && deviceId) {
        // Update existing device
        await updateIoTDevice(deviceId, deviceData);
      } else {
        // Create new device
        await createIoTDevice(deviceData);
      }

      // Redirect to device list
      navigate('/iot');
    } catch (err: any) {
      console.error('Error saving IoT device:', err);
      setError('Failed to save IoT device. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit IoT Device' : 'Add New IoT Device'}
        </h1>
        <Link
          to="/iot"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to IoT Devices
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm Selection */}
            <div>
              <label htmlFor="farm_id" className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="p-2 border rounded bg-gray-50">
                {selectedFarm ? (
                  <span className="text-gray-700">{selectedFarm.name}</span>
                ) : (
                  <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                To change the farm, use the farm selector in the header.
              </p>
              <input
                type="hidden"
                id="farm_id"
                name="farm_id"
                value={selectedFarm?.id || ''}
              />
            </div>

            {/* Device Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Device Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={device.name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Device Type */}
            <div>
              <label htmlFor="device_type" className="block text-sm font-medium text-gray-700 mb-1">
                Device Type <span className="text-red-500">*</span>
              </label>
              <select
                id="device_type"
                name="device_type"
                value={device.device_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="" disabled>Select a type</option>
                <option value="sensor">Sensor</option>
                <option value="tracker">Tracker</option>
                <option value="controller">Controller</option>
                <option value="gateway">Gateway</option>
                <option value="camera">Camera</option>
                <option value="weather_station">Weather Station</option>
                <option value="soil_monitor">Soil Monitor</option>
                <option value="irrigation_controller">Irrigation Controller</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Manufacturer */}
            <div>
              <label htmlFor="manufacturer" className="block text-sm font-medium text-gray-700 mb-1">
                Manufacturer
              </label>
              <input
                type="text"
                id="manufacturer"
                name="manufacturer"
                value={device.manufacturer}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Model */}
            <div>
              <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                Model
              </label>
              <input
                type="text"
                id="model"
                name="model"
                value={device.model}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Serial Number */}
            <div>
              <label htmlFor="serial_number" className="block text-sm font-medium text-gray-700 mb-1">
                Serial Number
              </label>
              <input
                type="text"
                id="serial_number"
                name="serial_number"
                value={device.serial_number}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Firmware Version */}
            <div>
              <label htmlFor="firmware_version" className="block text-sm font-medium text-gray-700 mb-1">
                Firmware Version
              </label>
              <input
                type="text"
                id="firmware_version"
                name="firmware_version"
                value={device.firmware_version}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Location Description */}
            <div>
              <label htmlFor="location_description" className="block text-sm font-medium text-gray-700 mb-1">
                Location Description
              </label>
              <input
                type="text"
                id="location_description"
                name="location_description"
                value={device.location_description}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Latitude */}
            <div>
              <label htmlFor="latitude" className="block text-sm font-medium text-gray-700 mb-1">
                Latitude
              </label>
              <input
                type="number"
                id="latitude"
                name="latitude"
                value={device.latitude === null ? '' : device.latitude}
                onChange={handleChange}
                step="0.0000001"
                min="-90"
                max="90"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Longitude */}
            <div>
              <label htmlFor="longitude" className="block text-sm font-medium text-gray-700 mb-1">
                Longitude
              </label>
              <input
                type="number"
                id="longitude"
                name="longitude"
                value={device.longitude === null ? '' : device.longitude}
                onChange={handleChange}
                step="0.0000001"
                min="-180"
                max="180"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={device.status}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="maintenance">Maintenance</option>
                <option value="offline">Offline</option>
              </select>
            </div>
          </div>

          {/* Camera Configuration Form */}
          <CameraForm 
            deviceType={device.device_type}
            configuration={device.configuration}
            onConfigurationChange={(config) => {
              setDevice(prev => ({ ...prev, configuration: config }));
            }}
          />

          {/* Configuration (JSON) - Only show for non-camera devices */}
          {device.device_type !== 'camera' && (
            <div className="mt-6">
              <label htmlFor="configuration" className="block text-sm font-medium text-gray-700 mb-1">
                Configuration (JSON)
              </label>
              <textarea
                id="configuration"
                name="configuration"
                value={device.configuration ? JSON.stringify(device.configuration, null, 2) : ''}
                onChange={(e) => {
                  try {
                    const config = e.target.value ? JSON.parse(e.target.value) : null;
                    setDevice(prev => ({ ...prev, configuration: config }));
                  } catch (err) {
                    // Allow invalid JSON during typing, but don't update the state
                    console.log('Invalid JSON, not updating state');
                  }
                }}
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm font-mono"
                placeholder='{"key": "value"}'
              ></textarea>
              <p className="mt-1 text-sm text-gray-500">
                Enter device configuration as valid JSON. Leave empty if not applicable.
              </p>
            </div>
          )}

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/iot"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Device'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default IoTDeviceForm;
