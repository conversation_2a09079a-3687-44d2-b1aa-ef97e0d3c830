import axios from 'axios';
import { API_URL } from '../config';

export interface MarketplaceProduct {
  id: string;
  name: string;
  description: string;
  marketplace_description?: string;
  marketplace_category?: string;
  category?: string;
  price: number;
  unit: string;
  farm_id: string;
  farm_name?: string;
  is_marketplace_visible: boolean;
  override_farm_fulfillment?: boolean;
  offers_delivery?: boolean;
  offers_pickup?: boolean;
  sku?: string;
  images?: Array<{
    id: string;
    file_path: string;
    is_primary: boolean;
    url?: string;
  }>;
}

/**
 * Get all products visible in the marketplace
 * @param options Optional filter options
 * @returns Promise<MarketplaceProduct[]> Array of marketplace products
 */
export const getMarketplaceProducts = async (
  options: {
    search?: string;
    category?: string;
    subcategory?: string;
    farmId?: string;
    farmIds?: string[];
    minPrice?: number;
    maxPrice?: number;
    page?: number;
    limit?: number;
  } = {}
): Promise<{ products: MarketplaceProduct[]; total: number }> => {
  try {
    // Create a params object with the options
    const params: any = { ...options };

    const response = await axios.get(`${API_URL}/marketplace/products`, {
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching marketplace products:', error);
    return { products: [], total: 0 };
  }
};

/**
 * Get all products for a specific farm in the marketplace
 * @param farmId The ID of the farm
 * @param options Optional filter options
 * @returns Promise<MarketplaceProduct[]> Array of marketplace products for the farm
 */
export const getFarmMarketplaceProducts = async (
  farmId: string,
  options: {
    search?: string;
    category?: string;
    subcategory?: string;
    minPrice?: number;
    maxPrice?: number;
    page?: number;
    limit?: number;
  } = {}
): Promise<{ products: MarketplaceProduct[]; farmInfo: any; total: number }> => {
  try {
    // Create a params object with the options
    const params: any = { ...options };

    const response = await axios.get(`${API_URL}/marketplace/farms/${farmId}/products`, {
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching farm marketplace products:', error);
    return { products: [], farmInfo: null, total: 0 };
  }
};

/**
 * Get product categories for marketplace filtering
 * @returns Promise<string[]> Array of category strings
 */
export const getMarketplaceCategories = async (): Promise<string[]> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/products/categories`);
    return response.data;
  } catch (error) {
    console.error('Error fetching marketplace categories:', error);
    return [];
  }
};

/**
 * Get a single product from the marketplace
 * @param productId The ID of the product
 * @returns Promise<MarketplaceProduct | null> Marketplace product
 */
export const getMarketplaceProduct = async (
  productId: string
): Promise<MarketplaceProduct | null> => {
  try {
    const response = await axios.get(`${API_URL}/marketplace/products/${productId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching marketplace product:', error);
    return null;
  }
};

/**
 * Search for products in the marketplace
 * @param query Search query
 * @param filters Optional filter options
 * @returns Promise<MarketplaceProduct[]> Array of marketplace products matching the search
 */
export const searchMarketplaceProducts = async (
  query: string,
  filters: {
    category?: string;
    subcategory?: string;
    farmId?: string;
    farmIds?: string[];
    minPrice?: number;
    maxPrice?: number;
    page?: number;
    limit?: number;
  } = {}
): Promise<{ products: MarketplaceProduct[]; total: number }> => {
  try {
    // Create a params object with the query and filters
    const params: any = {
      q: query,
      ...filters
    };

    const response = await axios.get(`${API_URL}/marketplace/products/search`, {
      params
    });
    return response.data;
  } catch (error) {
    console.error('Error searching marketplace products:', error);
    return { products: [], total: 0 };
  }
};
