import axios from 'axios';
import { API_URL } from '../config';

// Define interfaces for the grants data
export interface Grant {
  id: string;
  title: string;
  description: string;
  agency: string;
  opportunityNumber: string;
  category: string;
  eligibility: string;
  fundingAmount: string;
  closeDate: string;
  url: string;
  createdAt: string;
}

export interface USDAGrant {
  id: string;
  title: string;
  description: string;
  category: string;
  eligibility: string;
  details: any;
  url: string;
}

export interface FarmersGovGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  fundingAmount: string;
  closeDate: string;
  url: string;
  createdAt: string;
}

export interface FSAGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  details: any;
  url: string;
  createdAt: string;
}

export interface RuralDevelopmentGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  fundingAmount: string;
  closeDate: string;
  url: string;
  createdAt: string;
  programType: string;
  ruralAreaEligibility: boolean;
}

export interface NRCSGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  details: any;
  url: string;
  createdAt: string;
  conservationFocus: string[];
}

export interface NIFAGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  fundingAmount: string;
  closeDate: string;
  url: string;
  createdAt: string;
  researchFocus: string[];
}

export interface RMAProgram {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  details: any;
  url: string;
  createdAt: string;
  riskManagementType: string;
  cropsCovered: string[];
}

export interface AMSGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  category: string;
  eligibility: string;
  fundingAmount: string;
  closeDate: string;
  url: string;
  createdAt: string;
  marketingFocus: string[];
}

export interface DataGovGrant {
  id: string;
  title: string;
  description: string;
  agency: string;
  opportunityNumber: string;
  category: string;
  eligibility: string;
  fundingAmount: string;
  closeDate: string;
  url: string;
  createdAt: string;
  source: string;
  details?: {
    program_objectives?: string;
    eligible_expenses?: string;
    application_process?: string;
    reporting_requirements?: string;
    contact_information?: {
      email?: string;
      phone?: string;
      website?: string;
    };
  };
}

// Farm-specific grant interface
export interface FarmGrant {
  id: string;
  name: string;
  provider: string;
  amount: number;
  status: 'applied' | 'approved' | 'rejected' | 'pending';
  deadline: string;
  description: string;
  requirements: string[];
  applicationDate?: string;
  approvalDate?: string;
  disbursementDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Farm-specific subsidy interface
export interface Subsidy {
  id: string;
  name: string;
  provider: string;
  amount: number;
  status: 'active' | 'expired' | 'pending' | 'rejected';
  startDate: string;
  endDate: string;
  description: string;
  requirements: string[];
  lastPaymentDate?: string;
  nextPaymentDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Union type for all grant types
export type AnyGrant = Grant | USDAGrant | FarmersGovGrant | FSAGrant | RuralDevelopmentGrant | NRCSGrant | NIFAGrant | RMAProgram | AMSGrant | DataGovGrant;

/**
 * Fetch grants from grants.gov API
 */
export const fetchGrantsGovGrants = async (
  category: string = 'agriculture',
  limit: number = 20
): Promise<Grant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/grants-gov`, {
      params: { category, limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching grants.gov grants:', error);
    throw error;
  }
};

/**
 * Fetch USDA grants from ARMS Data API
 */
export const fetchUSDAGrants = async (
  limit: number = 20
): Promise<USDAGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/usda`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching USDA grants:', error);
    throw error;
  }
};

/**
 * Fetch grants from farmers.gov API
 */
export const fetchFarmersGovGrants = async (
  limit: number = 20
): Promise<FarmersGovGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/farmers-gov`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching farmers.gov grants:', error);
    throw error;
  }
};

/**
 * Fetch grants from Farm Service Agency API
 */
export const fetchFSAGrants = async (
  limit: number = 20
): Promise<FSAGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/fsa`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching FSA grants:', error);
    throw error;
  }
};

/**
 * Fetch grants from USDA Rural Development API
 */
export const fetchRuralDevelopmentGrants = async (
  limit: number = 20
): Promise<RuralDevelopmentGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/rural-development`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching Rural Development grants:', error);
    throw error;
  }
};

/**
 * Fetch grants from NRCS (Natural Resources Conservation Service) API
 */
export const fetchNRCSGrants = async (
  limit: number = 20
): Promise<NRCSGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/nrcs`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching NRCS grants:', error);
    throw error;
  }
};

/**
 * Fetch grants from NIFA (National Institute of Food and Agriculture) API
 */
export const fetchNIFAGrants = async (
  limit: number = 20
): Promise<NIFAGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/nifa`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching NIFA grants:', error);
    throw error;
  }
};

/**
 * Fetch programs from RMA (Risk Management Agency) API
 */
export const fetchRMAPrograms = async (
  limit: number = 20
): Promise<RMAProgram[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/rma`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching RMA programs:', error);
    throw error;
  }
};

/**
 * Fetch grants from AMS (Agricultural Marketing Service) API
 */
export const fetchAMSGrants = async (
  limit: number = 20
): Promise<AMSGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/ams`, {
      params: { limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching AMS grants:', error);
    throw error;
  }
};

/**
 * Fetch agricultural grants from data.gov API
 */
export const fetchDataGovGrants = async (
  category: string = 'agriculture',
  limit: number = 20
): Promise<DataGovGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/data-gov`, {
      params: { category, limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching data.gov grants:', error);
    throw error;
  }
};

/**
 * Fetch all grants from all sources
 */
export const fetchAllGrants = async (
  category: string = 'agriculture',
  limit: number = 20
): Promise<AnyGrant[]> => {
  try {
    // Fetch grants from the database
    const response = await axios.get(`${API_URL}/grants`);
    const dbGrants = response.data;

    // If we have grants in the database, return them
    if (dbGrants && dbGrants.length > 0) {
      return dbGrants;
    }

    // Return empty array instead of falling back to mock data
    return [];
  } catch (error) {
    console.error('Error fetching all grants:', error);
    throw error;
  }
};

/**
 * Search for grants by keyword
 */
export const searchGrants = async (
  keyword: string,
  source: 'grants-gov' | 'usda' | 'farmers-gov' | 'fsa' | 'rural-development' | 'nrcs' | 'nifa' | 'rma' | 'ams' | 'data-gov' | 'all' = 'all',
  category: string = 'all',
  limit: number = 50
): Promise<AnyGrant[]> => {
  try {
    const response = await axios.get(`${API_URL}/grants/search`, {
      params: { keyword, source, category, limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error searching grants:', error);
    throw error;
  }
};

/**
 * Get grant details by ID
 */
export const getGrantDetails = async (
  id: string,
  source: 'grants-gov' | 'usda' | 'farmers-gov' | 'fsa' | 'rural-development' | 'nrcs' | 'nifa' | 'rma' | 'ams' | 'data-gov'
): Promise<AnyGrant> => {
  try {
    const response = await axios.get(`${API_URL}/grants/${source}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching grant details:', error);
    throw error;
  }
};

/**
 * Farm Grants API Functions
 */

// Get all grants for a farm
export const getFarmGrants = async (farmId: string, status?: string, searchTerm?: string): Promise<FarmGrant[]> => {
  try {
    const params: any = {};
    if (status && status !== 'all') params.status = status;
    if (searchTerm) params.searchTerm = searchTerm;

    const response = await axios.get(`${API_URL}/farm-grants/farms/${farmId}/grants`, { params });
    return response.data.grants;
  } catch (error) {
    console.error('Error fetching farm grants:', error);
    throw error;
  }
};

// Get a specific grant for a farm
export const getFarmGrant = async (farmId: string, grantId: string): Promise<FarmGrant> => {
  try {
    const response = await axios.get(`${API_URL}/farm-grants/farms/${farmId}/grants/${grantId}`);
    return response.data.grant;
  } catch (error) {
    console.error('Error fetching farm grant:', error);
    throw error;
  }
};

// Create a new grant for a farm
export const createFarmGrant = async (
  farmId: string, 
  grantData: Omit<FarmGrant, 'id' | 'createdAt' | 'updatedAt'>
): Promise<FarmGrant> => {
  try {
    const response = await axios.post(`${API_URL}/farm-grants/farms/${farmId}/grants`, grantData);
    return response.data.grant;
  } catch (error) {
    console.error('Error creating farm grant:', error);
    throw error;
  }
};

// Update a grant for a farm
export const updateFarmGrant = async (
  farmId: string, 
  grantId: string, 
  grantData: Partial<Omit<FarmGrant, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<FarmGrant> => {
  try {
    const response = await axios.put(`${API_URL}/farm-grants/farms/${farmId}/grants/${grantId}`, grantData);
    return response.data.grant;
  } catch (error) {
    console.error('Error updating farm grant:', error);
    throw error;
  }
};

// Delete a grant for a farm
export const deleteFarmGrant = async (farmId: string, grantId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/farm-grants/farms/${farmId}/grants/${grantId}`);
  } catch (error) {
    console.error('Error deleting farm grant:', error);
    throw error;
  }
};

/**
 * Farm Subsidies API Functions
 */

// Get all subsidies for a farm
export const getFarmSubsidies = async (farmId: string, status?: string, searchTerm?: string): Promise<Subsidy[]> => {
  try {
    const params: any = {};
    if (status && status !== 'all') params.status = status;
    if (searchTerm) params.searchTerm = searchTerm;

    const response = await axios.get(`${API_URL}/farm-grants/farms/${farmId}/subsidies`, { params });
    return response.data.subsidies;
  } catch (error) {
    console.error('Error fetching farm subsidies:', error);
    throw error;
  }
};

// Get a specific subsidy for a farm
export const getFarmSubsidy = async (farmId: string, subsidyId: string): Promise<Subsidy> => {
  try {
    const response = await axios.get(`${API_URL}/farm-grants/farms/${farmId}/subsidies/${subsidyId}`);
    return response.data.subsidy;
  } catch (error) {
    console.error('Error fetching farm subsidy:', error);
    throw error;
  }
};

// Create a new subsidy for a farm
export const createFarmSubsidy = async (
  farmId: string, 
  subsidyData: Omit<Subsidy, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Subsidy> => {
  try {
    const response = await axios.post(`${API_URL}/farm-grants/farms/${farmId}/subsidies`, subsidyData);
    return response.data.subsidy;
  } catch (error) {
    console.error('Error creating farm subsidy:', error);
    throw error;
  }
};

// Update a subsidy for a farm
export const updateFarmSubsidy = async (
  farmId: string, 
  subsidyId: string, 
  subsidyData: Partial<Omit<Subsidy, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<Subsidy> => {
  try {
    const response = await axios.put(`${API_URL}/farm-grants/farms/${farmId}/subsidies/${subsidyId}`, subsidyData);
    return response.data.subsidy;
  } catch (error) {
    console.error('Error updating farm subsidy:', error);
    throw error;
  }
};

// Delete a subsidy for a farm
export const deleteFarmSubsidy = async (farmId: string, subsidyId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/farm-grants/farms/${farmId}/subsidies/${subsidyId}`);
  } catch (error) {
    console.error('Error deleting farm subsidy:', error);
    throw error;
  }
};
