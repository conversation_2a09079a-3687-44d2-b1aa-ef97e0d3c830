import axios from 'axios';
import { API_URL } from '../config';

/**
 * Interface for harvest direction map
 */
export interface HarvestDirectionMap {
  id: string;
  field_id: string;
  name: string;
  description?: string;
  order_index: number;
  elements: DrawingElement[];
  created_at: string;
  updated_at: string;
}

/**
 * Interface for drawing element
 */
export interface DrawingElement {
  id: string;
  type: 'polygon' | 'polyline' | 'marker';
  coordinates: { lat: number; lng: number }[] | { lat: number; lng: number };
  color: string;
  label?: string;
}

/**
 * Get all harvest direction maps for a field
 * @param fieldId The field ID
 * @returns Promise<HarvestDirectionMap[]> All harvest direction maps for the field
 */
export const getFieldHarvestDirectionMaps = async (fieldId: string): Promise<HarvestDirectionMap[]> => {
  try {
    const response = await axios.get(`${API_URL}/fields/${fieldId}/harvest-direction-maps`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching harvest direction maps:', error);
    throw error;
  }
};

/**
 * Get a single harvest direction map by ID
 * @param mapId The harvest direction map ID
 * @returns Promise<HarvestDirectionMap> The harvest direction map
 */
export const getHarvestDirectionMapById = async (mapId: string): Promise<HarvestDirectionMap> => {
  try {
    const response = await axios.get(`${API_URL}/fields/harvest-direction-maps/${mapId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching harvest direction map:', error);
    throw error;
  }
};

/**
 * Create a new harvest direction map
 * @param harvestDirectionMap The harvest direction map to create
 * @returns Promise<HarvestDirectionMap> The created harvest direction map
 */
export const createHarvestDirectionMap = async (harvestDirectionMap: Omit<HarvestDirectionMap, 'id' | 'created_at' | 'updated_at'>): Promise<HarvestDirectionMap> => {
  try {
    const response = await axios.post(`${API_URL}/fields/harvest-direction-maps`, harvestDirectionMap, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error creating harvest direction map:', error);
    throw error;
  }
};

/**
 * Update an existing harvest direction map
 * @param mapId The harvest direction map ID
 * @param harvestDirectionMap The harvest direction map data to update
 * @returns Promise<HarvestDirectionMap> The updated harvest direction map
 */
export const updateHarvestDirectionMap = async (mapId: string, harvestDirectionMap: Partial<Omit<HarvestDirectionMap, 'id' | 'created_at' | 'updated_at'>>): Promise<HarvestDirectionMap> => {
  try {
    const response = await axios.put(`${API_URL}/fields/harvest-direction-maps/${mapId}`, harvestDirectionMap, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error updating harvest direction map:', error);
    throw error;
  }
};

/**
 * Delete a harvest direction map
 * @param mapId The harvest direction map ID
 * @returns Promise<void>
 */
export const deleteHarvestDirectionMap = async (mapId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/fields/harvest-direction-maps/${mapId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error deleting harvest direction map:', error);
    throw error;
  }
};