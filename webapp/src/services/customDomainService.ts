import axios from 'axios';
import { API_URL } from '../config';

/**
 * Service for managing custom domains
 */
export const customDomainService = {
  /**
   * Get the custom domain for a farm
   * @param farmId - The ID of the farm
   * @returns The custom domain information
   */
  async getCustomDomain(farmId: string) {
    const response = await axios.get(`${API_URL}/farms/${farmId}/custom-domain`);
    return response.data;
  },

  /**
   * Set up a custom domain for a farm
   * @param farmId - The ID of the farm
   * @param customDomain - The custom domain to set up
   * @returns The result of the operation
   */
  async setupCustomDomain(farmId: string, customDomain: string) {
    const response = await axios.post(`${API_URL}/farms/${farmId}/custom-domain`, { customDomain });
    return response.data;
  },

  /**
   * Verify a custom domain for a farm
   * @param farmId - The ID of the farm
   * @returns The result of the verification
   */
  async verifyCustomDomain(farmId: string) {
    const response = await axios.put(`${API_URL}/farms/${farmId}/custom-domain/verify`);
    return response.data;
  },

  /**
   * Remove a custom domain from a farm
   * @param farmId - The ID of the farm
   * @returns The result of the operation
   */
  async removeCustomDomain(farmId: string) {
    const response = await axios.delete(`${API_URL}/farms/${farmId}/custom-domain`);
    return response.data;
  }
};