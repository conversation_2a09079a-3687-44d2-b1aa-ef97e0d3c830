import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';
import { handleApiError } from '../utils/errorHandler';

// Define DeliveryTracking interface
export interface DeliveryTracking {
  id: string;
  route_id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  created_at: string;
}

// Define DeliveryRoute interface
export interface DeliveryRoute {
  id: string;
  farm_id: string;
  name: string;
  description: string;
  delivery_date: string;
  driver_id: string;
  status: string;
  start_time: string | null;
  end_time: string | null;
  created_at: string;
  updated_at: string;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

// Get driver location tracking points for a route
export const getDriverLocationTracking = async (routeId: string, limit?: number) => {
  try {
    const token = getAuthToken();
    
    // Build query string
    const queryParams = new URLSearchParams();
    if (limit) queryParams.append('limit', limit.toString());
    
    const queryString = queryParams.toString();
    const url = `${API_URL}/delivery-scheduling/routes/${routeId}/tracking${queryString ? `?${queryString}` : ''}`;
    
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching driver location tracking:', error);
    
    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch driver location tracking');
    
    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;
    
    throw customError;
  }
};

// Get delivery route details
export const getDeliveryRoute = async (routeId: string) => {
  try {
    const token = getAuthToken();
    
    const response = await axios.get(`${API_URL}/delivery-scheduling/routes/${routeId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return response.data;
  } catch (error: unknown) {
    console.error('Error fetching delivery route:', error);
    
    // Use the error handler to get a structured error
    const structuredError = handleApiError(error, undefined, 'Failed to fetch delivery route');
    
    // Create a custom error with the structured information
    const customError = new Error(structuredError.message);
    (customError as any).structuredError = structuredError;
    
    throw customError;
  }
};

// Format date for display
export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};