import axios from 'axios';
import { API_URL } from '../config';
import { Bill, BillCategory, BillFormData, BillCategoryFormData, BillPaymentFormData, BillTransactionLinkFormData, BillStatistics } from '../types/bills';

// Bill Categories
export const getFarmBillCategories = async (farmId: string): Promise<BillCategory[]> => {
  try {
    const response = await axios.get(`${API_URL}/bill-categories/farm/${farmId}`);
    return response.data.categories || [];
  } catch (error) {
    console.error('Error fetching bill categories:', error);
    throw error;
  }
};

export const getBillCategoryById = async (categoryId: string): Promise<BillCategory> => {
  try {
    const response = await axios.get(`${API_URL}/bill-categories/${categoryId}`);
    return response.data.category;
  } catch (error) {
    console.error('Error fetching bill category:', error);
    throw error;
  }
};

export const createBillCategory = async (categoryData: BillCategoryFormData): Promise<BillCategory> => {
  try {
    const response = await axios.post(`${API_URL}/bill-categories`, categoryData);
    return response.data.category;
  } catch (error) {
    console.error('Error creating bill category:', error);
    throw error;
  }
};

export const updateBillCategory = async (categoryId: string, categoryData: BillCategoryFormData): Promise<BillCategory> => {
  try {
    const response = await axios.put(`${API_URL}/bill-categories/${categoryId}`, categoryData);
    return response.data.category;
  } catch (error) {
    console.error('Error updating bill category:', error);
    throw error;
  }
};

export const deleteBillCategory = async (categoryId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/bill-categories/${categoryId}`);
  } catch (error) {
    console.error('Error deleting bill category:', error);
    throw error;
  }
};

export const getCategoryUsageStats = async (farmId: string): Promise<any> => {
  try {
    const response = await axios.get(`${API_URL}/bill-categories/farm/${farmId}/stats`);
    return response.data.categoryStats;
  } catch (error) {
    console.error('Error fetching category usage stats:', error);
    throw error;
  }
};

// Bills
export const getFarmBills = async (farmId: string, filters?: any): Promise<Bill[]> => {
  try {
    let url = `${API_URL}/bills/farm/${farmId}`;
    
    // Add query parameters for filters if provided
    if (filters) {
      const queryParams = new URLSearchParams();
      
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.vendor) queryParams.append('vendor', filters.vendor);
      if (filters.startDate) queryParams.append('startDate', filters.startDate);
      if (filters.endDate) queryParams.append('endDate', filters.endDate);
      if (filters.search) queryParams.append('search', filters.search);
      
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }
    
    const response = await axios.get(url);
    return response.data.bills || [];
  } catch (error) {
    console.error('Error fetching bills:', error);
    throw error;
  }
};

export const getBillById = async (billId: string): Promise<Bill> => {
  try {
    const response = await axios.get(`${API_URL}/bills/${billId}`);
    return response.data.bill;
  } catch (error) {
    console.error('Error fetching bill:', error);
    throw error;
  }
};

export const createBill = async (billData: BillFormData): Promise<Bill> => {
  try {
    const response = await axios.post(`${API_URL}/bills`, billData);
    return response.data.bill;
  } catch (error) {
    console.error('Error creating bill:', error);
    throw error;
  }
};

export const updateBill = async (billId: string, billData: BillFormData): Promise<Bill> => {
  try {
    const response = await axios.put(`${API_URL}/bills/${billId}`, billData);
    return response.data.bill;
  } catch (error) {
    console.error('Error updating bill:', error);
    throw error;
  }
};

export const deleteBill = async (billId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/bills/${billId}`);
  } catch (error) {
    console.error('Error deleting bill:', error);
    throw error;
  }
};

export const addBillPayment = async (billId: string, paymentData: BillPaymentFormData): Promise<any> => {
  try {
    const response = await axios.post(`${API_URL}/bills/${billId}/payments`, paymentData);
    return response.data;
  } catch (error) {
    console.error('Error adding bill payment:', error);
    throw error;
  }
};

export const linkTransaction = async (billId: string, linkData: BillTransactionLinkFormData): Promise<any> => {
  try {
    const response = await axios.post(`${API_URL}/bills/${billId}/transactions`, linkData);
    return response.data;
  } catch (error) {
    console.error('Error linking transaction:', error);
    throw error;
  }
};

export const unlinkTransaction = async (billId: string, linkId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/bills/${billId}/transactions/${linkId}`);
  } catch (error) {
    console.error('Error unlinking transaction:', error);
    throw error;
  }
};

export const uploadAttachment = async (billId: string, file: File): Promise<any> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await axios.post(`${API_URL}/bills/${billId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error uploading attachment:', error);
    throw error;
  }
};

export const deleteAttachment = async (billId: string, attachmentId: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/bills/${billId}/attachments/${attachmentId}`);
  } catch (error) {
    console.error('Error deleting attachment:', error);
    throw error;
  }
};

export const getBillStatistics = async (farmId: string, dateRange?: { startDate?: string, endDate?: string }): Promise<BillStatistics> => {
  try {
    let url = `${API_URL}/bills/farm/${farmId}/statistics`;
    
    if (dateRange) {
      const queryParams = new URLSearchParams();
      if (dateRange.startDate) queryParams.append('startDate', dateRange.startDate);
      if (dateRange.endDate) queryParams.append('endDate', dateRange.endDate);
      
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }
    
    const response = await axios.get(url);
    return response.data.statistics;
  } catch (error) {
    console.error('Error fetching bill statistics:', error);
    throw error;
  }
};