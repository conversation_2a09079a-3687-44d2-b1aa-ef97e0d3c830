import axios from 'axios';
import { API_URL } from '../config';

export interface WorkflowAutomation {
  id: string;
  farm_id: string;
  name: string;
  description: string | null;
  trigger_type: string;
  trigger_config: any;
  action_type: string;
  action_config: any;
  conditions: any | null;
  is_active: boolean;
  last_executed: string | null;
  execution_count: number;
  created_at: string;
  updated_at: string;
}

export interface WorkflowExecutionResult {
  status: string;
  message: string;
  taskDetails?: any;
  notificationDetails?: any;
}

/**
 * Get all workflow automations for a farm
 */
export const getFarmWorkflows = async (farmId: string): Promise<WorkflowAutomation[]> => {
  try {
    const response = await axios.get(`${API_URL}/workflows/farm/${farmId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching farm workflows:', error);
    throw error;
  }
};

/**
 * Get a specific workflow automation
 */
export const getWorkflow = async (workflowId: string): Promise<{ workflow: WorkflowAutomation }> => {
  try {
    const response = await axios.get(`${API_URL}/workflows/${workflowId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching workflow:', error);
    throw error;
  }
};

/**
 * Create a new workflow automation
 */
export const createWorkflow = async (data: {
  farm_id: string;
  name: string;
  description?: string;
  trigger_type: string;
  trigger_config: any;
  action_type: string;
  action_config: any;
  conditions?: any;
  is_active?: boolean;
}): Promise<{ message: string; workflow: WorkflowAutomation }> => {
  try {
    const response = await axios.post(`${API_URL}/workflows`, data);
    return response.data;
  } catch (error) {
    console.error('Error creating workflow:', error);
    throw error;
  }
};

/**
 * Update a workflow automation
 */
export const updateWorkflow = async (
  workflowId: string,
  data: {
    name?: string;
    description?: string;
    trigger_type?: string;
    trigger_config?: any;
    action_type?: string;
    action_config?: any;
    conditions?: any;
    is_active?: boolean;
  }
): Promise<{ message: string; workflow: WorkflowAutomation }> => {
  try {
    const response = await axios.put(`${API_URL}/workflows/${workflowId}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating workflow:', error);
    throw error;
  }
};

/**
 * Delete a workflow automation
 */
export const deleteWorkflow = async (workflowId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(`${API_URL}/workflows/${workflowId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting workflow:', error);
    throw error;
  }
};

/**
 * Execute a workflow manually
 */
export const executeWorkflow = async (workflowId: string): Promise<{ message: string; result: WorkflowExecutionResult }> => {
  try {
    const response = await axios.post(`${API_URL}/workflows/${workflowId}/execute`);
    return response.data;
  } catch (error) {
    console.error('Error executing workflow:', error);
    throw error;
  }
};

/**
 * Process all workflow triggers (admin function)
 */
export const processWorkflowTriggers = async (): Promise<{ message: string; results: any[] }> => {
  try {
    const response = await axios.post(`${API_URL}/workflows/process-triggers`);
    return response.data;
  } catch (error) {
    console.error('Error processing workflow triggers:', error);
    throw error;
  }
};
