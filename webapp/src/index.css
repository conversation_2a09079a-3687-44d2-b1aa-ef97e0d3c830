@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Ensure modals don't exceed window height */
  .fixed.inset-0.overflow-y-auto > div > div.bg-white,
  .fixed.inset-0.overflow-y-auto > div > div.inline-block,
  .fixed.inset-0.bg-gray-600 > div.relative.bg-white {
    @apply max-h-[90vh];
    overflow-y: auto;
  }
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    @apply overflow-x-hidden;
  }

  body {
    @apply bg-gray-50 text-gray-900 min-h-screen overflow-x-hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  a {
    @apply text-primary-600 hover:text-primary-700;
  }

  img {
    @apply max-w-full h-auto;
  }

  /* Ensure tables don't cause overflow */
  .table-container {
    @apply overflow-x-auto;
  }

  table {
    @apply w-full;
  }
}
