import React from 'react';

interface NotificationBadgeProps {
  count: number;
  className?: string;
}

/**
 * Notification badge component for displaying unread message counts
 */
const NotificationBadge: React.FC<NotificationBadgeProps> = ({ count, className = '' }) => {
  if (count <= 0) return null;

  // Limit the displayed count to 99+
  const displayCount = count > 99 ? '99+' : count.toString();

  return (
    <div 
      className={`absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center ${className}`}
      style={{ 
        minWidth: count > 99 ? '24px' : (count > 9 ? '20px' : '16px'),
        height: '16px',
        padding: '0 4px'
      }}
    >
      {displayCount}
    </div>
  );
};

export default NotificationBadge;