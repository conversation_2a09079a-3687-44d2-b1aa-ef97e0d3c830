import React from 'react';

interface PinIconProps {
  size?: number;
  color?: string;
}

/**
 * Pin icon component
 */
const PinIcon: React.FC<PinIconProps> = ({ 
  size = 24, 
  color = 'currentColor' 
}) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke={color} 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    >
      <path d="M12 2L12 22"></path>
      <path d="M5 5H19L18 10H6L5 5Z"></path>
      <path d="M8 10V16L6 19L18 19L16 16V10"></path>
    </svg>
  );
};

export default PinIcon;