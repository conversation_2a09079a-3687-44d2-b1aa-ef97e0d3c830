import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * Component that checks if the current hostname is store.nxtacre.com
 * and redirects to the root path if a /store path is accessed
 */
const StoreSubdomainCheck: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isStoreSubdomain, setIsStoreSubdomain] = useState<boolean | null>(null);
  
  useEffect(() => {
    // Check if we're on the store subdomain
    const hostname = window.location.hostname;
    const isStore = hostname === 'store.nxtacre.com';
    setIsStoreSubdomain(isStore);
    
    // If we're on the store subdomain and the path starts with /store,
    // redirect to the same path without the /store prefix
    if (isStore && location.pathname.startsWith('/store')) {
      const newPath = location.pathname.substring(6) || '/'; // Remove '/store' prefix
      navigate(newPath + location.search + location.hash, { replace: true });
    }
  }, [location, navigate]);
  
  // This component doesn't render anything
  return null;
};

export default StoreSubdomainCheck;