import React from 'react';
import { Link } from 'react-router-dom';

const MarketplaceFooter: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-5 bg-dark text-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          <div className="col-span-1 md:col-span-1">
            <img 
              src="/logo.svg" 
              alt="nxtAcre Logo" 
              className="h-10 w-auto mb-3"
              onError={(e) => {
                // Fallback if logo doesn't exist
                e.currentTarget.style.display = 'none';
              }}
            />
            <p className="text-sm">nxtAcre is a comprehensive farm management platform designed to support modern agricultural operations.</p>
          </div>
          
          <div className="col-span-1">
            <h5 className="font-semibold mb-3">Product</h5>
            <ul className="space-y-2">
              <li><a href="/features" className="text-white hover:text-gray-300">Features</a></li>
              <li><a href="/pricing" className="text-white hover:text-gray-300">Pricing</a></li>
            </ul>
          </div>
          
          <div className="col-span-1">
            <h5 className="font-semibold mb-3">Company</h5>
            <ul className="space-y-2">
              <li><a href="/about" className="text-white hover:text-gray-300">About Us</a></li>
              <li><a href="/contact" className="text-white hover:text-gray-300">Contact</a></li>
            </ul>
          </div>
          
          <div className="col-span-1">
            <h5 className="font-semibold mb-3">Resources</h5>
            <ul className="space-y-2">
              <li><a href="#" className="text-white hover:text-gray-300">Documentation</a></li>
              <li><a href="#" className="text-white hover:text-gray-300">Help Center</a></li>
            </ul>
          </div>
          
          <div className="col-span-1">
            <h5 className="font-semibold mb-3">Legal</h5>
            <ul className="space-y-2">
              <li><a href="/privacy-policy" className="text-white hover:text-gray-300">Privacy Policy</a></li>
              <li><a href="/terms-and-conditions" className="text-white hover:text-gray-300">Terms of Service</a></li>
              <li><a href="/cookie-policy" className="text-white hover:text-gray-300">Cookie Policy</a></li>
              <li><a href="/gdpr-policy" className="text-white hover:text-gray-300">GDPR</a></li>
            </ul>
          </div>
        </div>
        
        <hr className="my-4 border-gray-600" />
        
        <div className="flex flex-col md:flex-row md:justify-between items-center">
          <p className="mb-4 md:mb-0">&copy; {currentYear} nxtAcre. All rights reserved.</p>
          <div className="flex space-x-4">
            <Link to="/customer/login" className="px-4 py-2 border border-white text-white hover:bg-white hover:text-gray-900 rounded">
              Login
            </Link>
            <Link to="/customer/register" className="px-4 py-2 bg-white text-gray-900 hover:bg-gray-200 rounded">
              Sign Up
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default MarketplaceFooter;