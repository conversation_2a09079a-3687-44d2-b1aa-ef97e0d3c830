import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useHelp } from '../../context/HelpContext';
import { stripMarkdown } from '../../utils/markdownUtils';
import Layout from '../Layout';

const HelpCenter: React.FC = () => {
  const { 
    guides, 
    loading, 
    error, 
    searchTerm, 
    setSearchTerm, 
    searchGuides, 
    searchResults 
  } = useHelp();
  const [categories, setCategories] = useState<Record<string, any>>({});
  const navigate = useNavigate();

  // Group guides by category and subcategory
  useEffect(() => {
    if (guides.length > 0) {
      const categorized: Record<string, any> = {};

      guides.forEach(guide => {
        if (!categorized[guide.category]) {
          categorized[guide.category] = {
            subcategories: {},
            guides: []
          };
        }

        if (guide.subcategory) {
          if (!categorized[guide.category].subcategories[guide.subcategory]) {
            categorized[guide.category].subcategories[guide.subcategory] = [];
          }
          categorized[guide.category].subcategories[guide.subcategory].push(guide);
        } else {
          categorized[guide.category].guides.push(guide);
        }
      });

      setCategories(categorized);
    }
  }, [guides]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    searchGuides(searchTerm);
  };

  // Handle guide click
  const handleGuideClick = (slug: string) => {
    navigate(`/help/guides/${slug}`);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <Link to="/support" className="text-primary-600 hover:text-primary-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Support
          </Link>
        </div>
        
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Help Center</h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Find answers to your questions and learn how to get the most out of nxtAcre.
          </p>

          {/* Search form */}
          <form onSubmit={handleSearchSubmit} className="mt-8 max-w-xl mx-auto">
            <div className="flex shadow-sm rounded-md">
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search for help..."
                className="flex-1 min-w-0 block w-full px-4 py-3 rounded-l-md border-gray-300 focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-r-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Search
              </button>
            </div>
          </form>
        </div>

        {/* Search results */}
        {searchTerm && searchResults.length > 0 && (
          <div className="mb-12">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Search Results</h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {searchResults.map(guide => (
                <div 
                  key={guide.id} 
                  className="bg-white shadow rounded-lg p-6 hover:shadow-md cursor-pointer transition-shadow"
                  onClick={() => handleGuideClick(guide.slug)}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{guide.title}</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    {guide.category} {guide.subcategory ? `› ${guide.subcategory}` : ''}
                  </p>
                  <p className="text-gray-600 line-clamp-3">
                    {stripMarkdown(guide.content, 150)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No search results message */}
        {searchTerm && searchResults.length === 0 && (
          <div className="text-center mb-12 p-8 bg-gray-50 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No results found</h2>
            <p className="text-gray-600">
              We couldn't find any help guides matching your search. Try using different keywords or browse the categories below.
            </p>
          </div>
        )}

        {/* Categories */}
        {!searchTerm && Object.keys(categories).length > 0 && (
          <div className="space-y-12">
            {Object.entries(categories).map(([category, data]: [string, any]) => (
              <div key={category} className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">{category}</h2>
                </div>

                <div className="p-6">
                  {/* Guides without subcategory */}
                  {data.guides.length > 0 && (
                    <div className="mb-8">
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {data.guides.map(guide => (
                          <div 
                            key={guide.id} 
                            className="p-4 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer"
                            onClick={() => handleGuideClick(guide.slug)}
                          >
                            <h3 className="text-md font-medium text-gray-900 mb-1">{guide.title}</h3>
                            <p className="text-sm text-gray-500 line-clamp-2">
                              {stripMarkdown(guide.content, 100)}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Subcategories */}
                  {Object.keys(data.subcategories).length > 0 && (
                    <div className="space-y-8">
                      {Object.entries(data.subcategories).map(([subcategory, guides]: [string, any]) => (
                        <div key={subcategory}>
                          <h3 className="text-lg font-medium text-gray-900 mb-4">{subcategory}</h3>
                          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {guides.map(guide => (
                              <div 
                                key={guide.id} 
                                className="p-4 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer"
                                onClick={() => handleGuideClick(guide.slug)}
                              >
                                <h4 className="text-md font-medium text-gray-900 mb-1">{guide.title}</h4>
                                <p className="text-sm text-gray-500 line-clamp-2">
                                  {stripMarkdown(guide.content, 100)}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default HelpCenter;