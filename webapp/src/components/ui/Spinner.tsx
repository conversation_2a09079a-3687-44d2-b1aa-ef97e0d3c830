import React, { HTMLAttributes } from 'react';

export interface SpinnerProps extends HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg';
}

export const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className = '', size = 'md', ...props }, ref) => {
    // Size styles
    let sizeClasses = '';
    
    switch (size) {
      case 'sm':
        sizeClasses = 'w-4 h-4 border-2';
        break;
      case 'lg':
        sizeClasses = 'w-8 h-8 border-4';
        break;
      default: // 'md'
        sizeClasses = 'w-6 h-6 border-3';
        break;
    }

    return (
      <div
        ref={ref}
        className={`inline-block animate-spin rounded-full border-solid border-primary-500 border-t-transparent ${sizeClasses} ${className}`}
        role="status"
        aria-label="Loading"
        {...props}
      />
    );
  }
);

Spinner.displayName = 'Spinner';