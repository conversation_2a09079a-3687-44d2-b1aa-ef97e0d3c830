import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';

interface FieldHealth {
  fieldId: string;
  fieldName: string;
  ndviScore: number;
  lastUpdated: string;
  status: 'good' | 'warning' | 'critical';
}

const FieldHealthWidget: React.FC = () => {
  const { token } = useAuth();
  const { currentFarm } = useFarm();
  const [fieldHealthData, setFieldHealthData] = useState<FieldHealth[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFieldHealthData = async () => {
      if (!currentFarm || !token) return;

      try {
        setLoading(true);
        setError(null);

        // Get all fields for the current farm
        const fieldsResponse = await fetch(`/api/fields/farm/${currentFarm.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!fieldsResponse.ok) {
          throw new Error('Failed to fetch fields');
        }

        const fields = await fieldsResponse.json();

        // Get health data for each field
        const healthDataPromises = fields.map(async (field: any) => {
          const healthResponse = await fetch(`/api/field-health/${field.id}`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          if (!healthResponse.ok) {
            console.warn(`Failed to fetch health data for field ${field.id}`);
            return null;
          }

          const healthData = await healthResponse.json();
          return {
            fieldId: field.id,
            fieldName: field.name,
            ndviScore: healthData.ndviScore,
            lastUpdated: healthData.lastUpdated,
            status: healthData.status
          };
        });

        const healthResults = await Promise.all(healthDataPromises);
        const validHealthData = healthResults.filter(data => data !== null) as FieldHealth[];

        setFieldHealthData(validHealthData);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching field health data:', err);
        setError(err.response?.data?.error || err.message || 'Failed to load field health data');
        setLoading(false);
      }
    };

    fetchFieldHealthData();
  }, [currentFarm, token]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-500">Loading field health data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        <p>Error: {error}</p>
      </div>
    );
  }

  if (fieldHealthData.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>No field health data available.</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="space-y-4">
        {fieldHealthData.map((field) => (
          <div key={field.fieldId} className="border rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-gray-900">{field.fieldName}</h3>
              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(field.status)}`}>
                {field.status.charAt(0).toUpperCase() + field.status.slice(1)}
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className={`h-2.5 rounded-full ${
                    field.ndviScore > 0.7 ? 'bg-green-600' : 
                    field.ndviScore > 0.5 ? 'bg-yellow-500' : 'bg-red-600'
                  }`} 
                  style={{ width: `${field.ndviScore * 100}%` }}
                ></div>
              </div>
              <span className="ml-2 text-xs text-gray-500">NDVI: {field.ndviScore.toFixed(2)}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Last updated: {new Date(field.lastUpdated).toLocaleDateString()}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FieldHealthWidget;
