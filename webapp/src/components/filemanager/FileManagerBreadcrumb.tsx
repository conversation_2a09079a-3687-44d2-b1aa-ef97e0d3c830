import React from 'react';
import { BreadcrumbItem } from './FileManagerTypes';

interface FileManagerBreadcrumbProps {
  items: BreadcrumbItem[];
  onNavigate: (id: string | null, name: string) => void;
}

const FileManagerBreadcrumb: React.FC<FileManagerBreadcrumbProps> = ({ items, onNavigate }) => {
  return (
    <nav className="px-4 py-3 border-b border-gray-200 file-manager-breadcrumb">
      <ol className="flex flex-wrap items-center space-x-2">
        {items.map((item, index) => (
          <li key={item.id || 'root'} className="flex items-center">
            {index > 0 && <span className="text-gray-500 mx-2">/</span>}
            <button
              onClick={() => onNavigate(item.id, item.name)}
              className={`px-2 py-1 text-sm rounded ${
                index === items.length - 1
                  ? 'bg-primary-100 text-primary-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {index === 0 ? (
                <div className="flex items-center">
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                    />
                  </svg>
                  {item.name}
                </div>
              ) : (
                item.name
              )}
            </button>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default FileManagerBreadcrumb;
