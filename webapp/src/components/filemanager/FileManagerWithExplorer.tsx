import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import axios from 'axios';
import { useDropzone } from 'react-dropzone';
import { AuthContext } from '../../context/AuthContext';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';
import { 
  FileItem, 
  BreadcrumbItem, 
  ViewMode, 
  FileManagerProps 
} from './FileManagerTypes';
import ContextMenu from '../ContextMenu';
import FileManagerToolbar from './FileManagerToolbar';
import FileManagerBreadcrumb from './FileManagerBreadcrumb';
import FileManagerGrid from './FileManagerGrid';
import FileManagerList from './FileManagerList';
import FileManagerUploadModal from './FileManagerUploadModal';
import FileManagerCreateFolderModal from './FileManagerCreateFolderModal';
import FileManagerCreateDocumentModal from './FileManagerCreateDocumentModal';
import FileManagerRenameModal from './FileManagerRenameModal';
import FileManagerDeleteModal from './FileManagerDeleteModal';
import FileManagerPreview from './FileManagerPreview';
import FileManagerMoveModal from './FileManagerMoveModal';
import FileManagerPermissionModal from './FileManagerPermissionModal';
import FileManagerFolderExplorer from './FileManagerFolderExplorer';
import './FileManagerStyles.css';
import FileManagerShareModal from './FileManagerShareModal';

const FileManagerWithExplorer: React.FC<FileManagerProps> = ({ 
  farmId,
  onFileOpen,
  onFolderOpen,
  initialFolderId = null,
  onFolderNavigation,
  className = ''
}) => {
  // This component is a wrapper around the original FileManager component
  // It adds a folder explorer on the left side of the file manager

  // State
  const [items, setItems] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { id: null, name: 'Root' }
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    // Initialize from localStorage if available, otherwise default to 'grid'
    const savedViewMode = localStorage.getItem('fileManagerViewMode');
    return (savedViewMode === 'list' || savedViewMode === 'grid') ? savedViewMode : 'grid';
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [focusedItemIndex, setFocusedItemIndex] = useState<number>(-1);
  const [isDragActive, setIsDragActive] = useState(false);

  // Drag selection state
  const [isDragSelecting, setIsDragSelecting] = useState(false);
  const [dragStartPosition, setDragStartPosition] = useState<{ x: number, y: number } | null>(null);
  const [dragCurrentPosition, setDragCurrentPosition] = useState<{ x: number, y: number } | null>(null);
  const [itemRefs, setItemRefs] = useState<Map<string, HTMLElement>>(new Map());

  // Clipboard state for copy, cut, paste operations
  const [clipboardItems, setClipboardItems] = useState<FileItem[]>([]);
  const [clipboardOperation, setClipboardOperation] = useState<'copy' | 'cut' | null>(null);

  // Modals
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [createFolderModalOpen, setCreateFolderModalOpen] = useState(false);
  const [createDocumentModalOpen, setCreateDocumentModalOpen] = useState(false);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [moveModalOpen, setMoveModalOpen] = useState(false);
  const [permissionModalOpen, setPermissionModalOpen] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [itemToRename, setItemToRename] = useState<FileItem | null>(null);
  const [itemsToDelete, setItemsToDelete] = useState<FileItem[]>([]);
  const [itemToPreview, setItemToPreview] = useState<FileItem | null>(null);
  const [itemsToMove, setItemsToMove] = useState<FileItem[]>([]);
  const [itemToManagePermissions, setItemToManagePermissions] = useState<FileItem | null>(null);
  const [itemToShare, setItemToShare] = useState<FileItem | null>(null);

  // Context
  const { user } = useContext(AuthContext);

  // Cache for API responses
  const cache = useRef<{
    [key: string]: {
      files: FileItem[];
      folders: FileItem[];
      timestamp: number;
    }
  }>({});

  // Invalidate cache for current folder
  const invalidateCache = useCallback(() => {
    // Clear all cache entries related to the current folder
    const prefix = `${farmId || user?.farm_id}_${currentFolder || 'root'}`;
    Object.keys(cache.current).forEach(key => {
      if (key.startsWith(prefix)) {
        delete cache.current[key];
      }
    });
  }, [farmId, user?.farm_id, currentFolder]);

  // Fetch files and folders
  const fetchItems = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Create cache key based on current parameters
      const cacheKey = `${farmId || user?.farm_id}_${currentFolder || 'root'}_${searchQuery}`;

      // Check if we have a valid cached response (less than 30 seconds old)
      const now = Date.now();
      const cachedData = cache.current[cacheKey];
      const isCacheValid = cachedData && (now - cachedData.timestamp < 30000) && !searchQuery;

      let files: FileItem[] = [];
      let folders: FileItem[] = [];

      if (isCacheValid) {
        // Use cached data
        console.log('Using cached data for', cacheKey);
        files = cachedData.files;
        folders = cachedData.folders;
      } else {
        // Fetch files
        let filesUrl = `${API_URL}/documents/farm/${farmId || user?.farm_id}/documents`;
        const fileParams: Record<string, string> = {};

        if (currentFolder) {
          fileParams.folderId = currentFolder;
        } else if (currentFolder === null) {
          fileParams.folderId = 'null'; // Explicitly request root files
        }

        if (searchQuery) {
          fileParams.search = searchQuery;
        }

        // Add query parameters
        if (Object.keys(fileParams).length > 0) {
          const queryString = new URLSearchParams(fileParams).toString();
          filesUrl = `${filesUrl}?${queryString}`;
        }

        const filesResponse = await axios.get(filesUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        files = (filesResponse.data.documents || []).map((doc: any) => ({
          ...doc,
          type: 'file'
        }));

        // Fetch folders
        let foldersUrl = `${API_URL}/documents/farm/${farmId || user?.farm_id}/folders`;
        const folderParams: Record<string, string> = {};

        if (currentFolder) {
          folderParams.parentFolderId = currentFolder;
        } else if (currentFolder === null) {
          folderParams.parentFolderId = 'null'; // Explicitly request root folders
        }

        if (searchQuery) {
          folderParams.search = searchQuery;
        }

        // Add query parameters
        if (Object.keys(folderParams).length > 0) {
          const queryString = new URLSearchParams(folderParams).toString();
          foldersUrl = `${foldersUrl}?${queryString}`;
        }

        const foldersResponse = await axios.get(foldersUrl, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        folders = (foldersResponse.data || []).map((folder: any) => ({
          ...folder,
          type: 'folder'
        }));

        // Update cache if not a search query
        if (!searchQuery) {
          cache.current[cacheKey] = {
            files,
            folders,
            timestamp: now
          };
        }
      }

      // Combine and sort items
      const allItems = [...folders, ...files];
      const sortedItems = sortItems(allItems, sortBy, sortDirection);

      setItems(sortedItems);
    } catch (err: any) {
      console.error('Error fetching items:', err);
      const errorMessage = err.response?.data?.error || 'Failed to load files and folders. Please try again later.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [API_URL, farmId, user?.farm_id, currentFolder, searchQuery, sortBy, sortDirection]);

  // Sort items
  const sortItems = useCallback((items: FileItem[], sortField: string, direction: 'asc' | 'desc') => {
    return [...items].sort((a, b) => {
      // Always sort folders before files
      if (a.type === 'folder' && b.type === 'file') return -1;
      if (a.type === 'file' && b.type === 'folder') return 1;

      // Then sort by the specified field
      let aValue: any = a[sortField as keyof FileItem];
      let bValue: any = b[sortField as keyof FileItem];

      // Handle special cases
      if (sortField === 'file_size') {
        aValue = a.file_size || 0;
        bValue = b.file_size || 0;
      } else if (sortField === 'created_at' || sortField === 'updated_at') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      } else if (typeof aValue === 'string' && typeof bValue === 'string') {
        return direction === 'asc'
          ? aValue.localeCompare(bValue, undefined, { sensitivity: 'base' })
          : bValue.localeCompare(aValue, undefined, { sensitivity: 'base' });
      }

      // Default comparison
      if (aValue === bValue) return 0;
      if (direction === 'asc') {
        return aValue < bValue ? -1 : 1;
      } else {
        return aValue > bValue ? -1 : 1;
      }
    });
  }, []);

  // Fetch items when parameters change
  useEffect(() => {
    fetchItems();
  }, [fetchItems]);

  // Initialize with initial folder if provided, but only after the file manager is loaded
  useEffect(() => {
    // Only proceed if we have an initialFolderId that's different from the current folder
    // AND the file manager is not in a loading state
    if (initialFolderId && initialFolderId !== currentFolder && !loading) {
      const fetchFolderDetails = async () => {
        try {
          const response = await axios.get(`${API_URL}/documents/farm/${farmId || user?.farm_id}/folders/${initialFolderId}`, {
            headers: {
              Authorization: `Bearer ${getAuthToken()}`
            }
          });

          const folder = response.data;
          setCurrentFolder(initialFolderId);
          setBreadcrumbs([
            { id: null, name: 'Root' },
            { id: initialFolderId, name: folder.name }
          ]);
        } catch (err) {
          console.error('Error fetching initial folder details:', err);
          setCurrentFolder(initialFolderId);
          setBreadcrumbs(prev => [...prev, { id: initialFolderId, name: 'Folder' }]);
        }
      };

      fetchFolderDetails();
    }
  }, [initialFolderId, currentFolder, loading, API_URL, farmId, user?.farm_id]);

  // Handle folder navigation
  const navigateToFolder = useCallback((folderId: string | null, folderName: string) => {
    setCurrentFolder(folderId);
    setSelectedItems([]);

    if (folderId === null) {
      // Reset to root
      setBreadcrumbs([{ id: null, name: 'Root' }]);
    } else {
      // Add to breadcrumbs
      setBreadcrumbs(prev => {
        // Check if we're navigating to a folder that's already in the breadcrumbs
        const existingIndex = prev.findIndex(item => item.id === folderId);
        if (existingIndex >= 0) {
          // If so, truncate the breadcrumbs to that point
          return prev.slice(0, existingIndex + 1);
        }
        // Otherwise, add the new folder to the breadcrumbs
        return [...prev, { id: folderId, name: folderName }];
      });
    }

    // Update URL via the callback if provided
    if (onFolderNavigation) {
      onFolderNavigation(folderId, folderName);
    }
  }, [onFolderNavigation]);

  // Register item ref for drag selection
  const registerItemRef = useCallback((itemId: string, element: HTMLElement | null) => {
    if (element) {
      setItemRefs(prev => {
        const newMap = new Map(prev);
        newMap.set(itemId, element);
        return newMap;
      });
    }
  }, []);

  // Check if an item is within the drag selection area
  const isItemInSelectionArea = useCallback((itemId: string) => {
    if (!isDragSelecting || !dragStartPosition || !dragCurrentPosition || !itemRefs.has(itemId)) {
      return false;
    }

    const itemElement = itemRefs.get(itemId);
    if (!itemElement) return false;

    const itemRect = itemElement.getBoundingClientRect();

    // Calculate selection rectangle
    const selectionRect = {
      left: Math.min(dragStartPosition.x, dragCurrentPosition.x),
      top: Math.min(dragStartPosition.y, dragCurrentPosition.y),
      right: Math.max(dragStartPosition.x, dragCurrentPosition.x),
      bottom: Math.max(dragStartPosition.y, dragCurrentPosition.y)
    };

    // Check if the item intersects with the selection rectangle
    return !(
      itemRect.right < selectionRect.left ||
      itemRect.left > selectionRect.right ||
      itemRect.bottom < selectionRect.top ||
      itemRect.top > selectionRect.bottom
    );
  }, [isDragSelecting, dragStartPosition, dragCurrentPosition, itemRefs]);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string, isMultiSelect: boolean) => {
    setSelectedItems(prev => {
      if (isMultiSelect) {
        // Toggle selection with multi-select (Ctrl/Cmd key)
        return prev.includes(itemId)
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId];
      } else {
        // Single selection
        return prev.includes(itemId) && prev.length === 1
          ? [] // Deselect if already selected
          : [itemId]; // Select only this item
      }
    });
  }, []);

  // Handle item double click
  const handleItemDoubleClick = useCallback((item: FileItem) => {
    if (item.type === 'folder') {
      navigateToFolder(item.id, item.name);
      if (onFolderOpen) onFolderOpen(item);
    } else {
      // Open preview for files
      setItemToPreview(item);
      setPreviewModalOpen(true);

      // Still call onFileOpen if provided (for compatibility)
      if (onFileOpen) onFileOpen(item);
    }
  }, [navigateToFolder, onFileOpen, onFolderOpen]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle view mode change
  const handleViewModeChange = useCallback((mode: ViewMode) => {
    // Save to localStorage to persist the user's preference
    localStorage.setItem('fileManagerViewMode', mode);
    setViewMode(mode);
  }, []);

  // Handle sort change
  const handleSortChange = useCallback((field: string) => {
    setSortBy(prev => {
      if (prev === field) {
        // If already sorting by this field, toggle direction
        setSortDirection(prevDir => prevDir === 'asc' ? 'desc' : 'asc');
        return field;
      } else {
        // If sorting by a new field, default to ascending
        setSortDirection('asc');
        return field;
      }
    });
  }, []);

  // Get selected items
  const getSelectedItems = useCallback(() => {
    return items.filter(item => selectedItems.includes(item.id));
  }, [items, selectedItems]);

  // Handle file upload
  const handleUpload = useCallback(async (files: File[], targetFolderId: string | null) => {
    try {
      const formData = new FormData();

      // Add each file to the form data
      files.forEach(file => {
        formData.append('files', file);
      });

      // Add folder ID if provided
      if (targetFolderId) {
        formData.append('folderId', targetFolderId);
      }

      // Add farm ID
      formData.append('farmId', farmId || user?.farm_id || '');

      // Upload files
      await axios.post(`${API_URL}/documents/farm/${farmId || user?.farm_id}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Refresh the file list
      invalidateCache();
      fetchItems();
    } catch (err: any) {
      console.error('Error uploading files:', err);
      const errorMessage = err.response?.data?.error || 'Failed to upload files. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, invalidateCache, fetchItems]);

  // Handle folder creation
  const handleCreateFolder = useCallback(async (name: string, description: string = '') => {
    try {
      await axios.post(`${API_URL}/documents/farm/${farmId || user?.farm_id}/folders`, {
        name,
        description,
        parent_folder_id: currentFolder
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Refresh the folder list
      invalidateCache();
      fetchItems();
    } catch (err: any) {
      console.error('Error creating folder:', err);
      const errorMessage = err.response?.data?.error || 'Failed to create folder. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, currentFolder, invalidateCache, fetchItems]);

  // Handle document creation
  const handleCreateDocument = useCallback(async (name: string, content: string = '') => {
    try {
      await axios.post(`${API_URL}/documents/farm/${farmId || user?.farm_id}/documents`, {
        name,
        content,
        folderId: currentFolder
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      // Refresh the document list
      invalidateCache();
      fetchItems();
    } catch (err: any) {
      console.error('Error creating document:', err);
      const errorMessage = err.response?.data?.error || 'Failed to create document. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, currentFolder, invalidateCache, fetchItems]);

  // Handle item rename
  const handleRename = useCallback(async (item: FileItem, newName: string) => {
    try {
      if (item.type === 'folder') {
        await axios.put(`${API_URL}/documents/farm/${farmId || user?.farm_id}/folders/${item.id}`, {
          name: newName
        }, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      } else {
        await axios.put(`${API_URL}/documents/farm/${farmId || user?.farm_id}/documents/${item.id}`, {
          name: newName
        }, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Refresh the item list
      invalidateCache();
      fetchItems();
    } catch (err: any) {
      console.error('Error renaming item:', err);
      const errorMessage = err.response?.data?.error || 'Failed to rename item. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, invalidateCache, fetchItems]);

  // Handle item deletion
  const handleDelete = useCallback(async (items: FileItem[]) => {
    try {
      // Group items by type
      const folders = items.filter(item => item.type === 'folder');
      const files = items.filter(item => item.type === 'file');

      // Delete folders
      for (const folder of folders) {
        await axios.delete(`${API_URL}/documents/farm/${farmId || user?.farm_id}/folders/${folder.id}?recursive=true`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Delete files
      for (const file of files) {
        await axios.delete(`${API_URL}/documents/farm/${farmId || user?.farm_id}/documents/${file.id}`, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Refresh the item list
      invalidateCache();
      fetchItems();
      setSelectedItems([]);
    } catch (err: any) {
      console.error('Error deleting items:', err);
      const errorMessage = err.response?.data?.error || 'Failed to delete items. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, invalidateCache, fetchItems]);

  // Handle item move
  const handleMove = useCallback(async (itemIds: string[], targetFolderId: string | null) => {
    try {
      // Find the items to move
      const itemsToMove = items.filter(item => itemIds.includes(item.id));

      // Group items by type
      const folders = itemsToMove.filter(item => item.type === 'folder');
      const files = itemsToMove.filter(item => item.type === 'file');

      // Move folders
      for (const folder of folders) {
        await axios.put(`${API_URL}/documents/farm/${farmId || user?.farm_id}/folders/${folder.id}`, {
          parent_folder_id: targetFolderId
        }, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Move files
      for (const file of files) {
        await axios.put(`${API_URL}/documents/farm/${farmId || user?.farm_id}/documents/${file.id}`, {
          folderId: targetFolderId
        }, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });
      }

      // Refresh the item list
      invalidateCache();
      fetchItems();
      setSelectedItems([]);
    } catch (err: any) {
      console.error('Error moving items:', err);
      const errorMessage = err.response?.data?.error || 'Failed to move items. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, items, invalidateCache, fetchItems]);

  // Handle file download
  const handleDownloadMultiple = useCallback(async (items: FileItem[]) => {
    try {
      // Only download files (not folders)
      const files = items.filter(item => item.type === 'file');

      for (const file of files) {
        // Create a temporary anchor element
        const link = document.createElement('a');
        link.href = `${API_URL}/documents/${file.id}/download`;
        link.download = file.name;
        link.target = '_blank';

        // Add authorization header via fetch
        const response = await fetch(link.href, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        });

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        link.href = url;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (err: any) {
      console.error('Error downloading files:', err);
      const errorMessage = err.response?.data?.error || 'Failed to download files. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id]);

  // Wrapper for single item download to match the expected type
  const handleDownload = useCallback((item: FileItem) => {
    handleDownloadMultiple([item]);
  }, [handleDownloadMultiple]);

  // Handle copy to clipboard
  const handleCopy = useCallback((items: FileItem[]) => {
    setClipboardItems(items);
    setClipboardOperation('copy');
  }, []);

  // Handle cut to clipboard
  const handleCut = useCallback((items: FileItem[]) => {
    setClipboardItems(items);
    setClipboardOperation('cut');
  }, []);

  // Handle paste from clipboard
  const handlePaste = useCallback(async (targetFolderId: string | null) => {
    if (clipboardItems.length === 0 || !clipboardOperation) return;

    try {
      if (clipboardOperation === 'copy') {
        // Copy operation - create new copies
        for (const item of clipboardItems) {
          if (item.type === 'folder') {
            // Copy folder
            await axios.post(`${API_URL}/documents/farm/${farmId || user?.farm_id}/folders/copy`, {
              folderId: item.id,
              targetFolderId
            }, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });
          } else {
            // Copy file
            await axios.post(`${API_URL}/documents/farm/${farmId || user?.farm_id}/documents/copy`, {
              documentId: item.id,
              targetFolderId
            }, {
              headers: {
                Authorization: `Bearer ${getAuthToken()}`
              }
            });
          }
        }
      } else {
        // Cut operation - move items
        await handleMove(clipboardItems.map(item => item.id), targetFolderId);

        // Clear clipboard after cut operation
        setClipboardItems([]);
        setClipboardOperation(null);
      }

      // Refresh the item list
      invalidateCache();
      fetchItems();
    } catch (err: any) {
      console.error('Error pasting items:', err);
      const errorMessage = err.response?.data?.error || 'Failed to paste items. Please try again later.';
      setError(errorMessage);
    }
  }, [API_URL, farmId, user?.farm_id, clipboardItems, clipboardOperation, handleMove, invalidateCache, fetchItems]);

  // Handle initiate move
  const handleInitiateMove = useCallback((items: FileItem[]) => {
    setItemsToMove(items);
    setMoveModalOpen(true);
  }, []);

  // Handle initiate permissions
  const handleInitiatePermissions = useCallback((item: FileItem) => {
    setItemToManagePermissions(item);
    setPermissionModalOpen(true);
  }, []);

  // Handle initiate share
  const handleInitiateShare = useCallback((item: FileItem) => {
    setItemToShare(item);
    setShareModalOpen(true);
  }, []);

  // Handle mouse down for drag selection
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // Only start drag selection with left mouse button
    if (e.button !== 0) return;

    // Don't start drag selection if clicking on an item or if Ctrl/Cmd key is pressed
    if ((e.target as HTMLElement).closest('.file-item') || e.ctrlKey || e.metaKey) return;

    setIsDragSelecting(true);
    setDragStartPosition({ x: e.clientX, y: e.clientY });
    setDragCurrentPosition({ x: e.clientX, y: e.clientY });

    // Clear selection if not holding Shift key
    if (!e.shiftKey) {
      setSelectedItems([]);
    }
  }, []);

  // Handle mouse move for drag selection
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragSelecting) return;

    setDragCurrentPosition({ x: e.clientX, y: e.clientY });

    // Update selected items based on drag selection
    const newSelectedItems = new Set(e.shiftKey ? selectedItems : []);

    items.forEach(item => {
      if (isItemInSelectionArea(item.id)) {
        newSelectedItems.add(item.id);
      }
    });

    setSelectedItems(Array.from(newSelectedItems));
  }, [isDragSelecting, items, isItemInSelectionArea, selectedItems]);

  // Handle mouse up to end drag selection
  const handleMouseUp = useCallback(() => {
    setIsDragSelecting(false);
    setDragStartPosition(null);
    setDragCurrentPosition(null);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Only handle keyboard navigation if we have items
    if (items.length === 0) return;

    // Get the currently focused item index
    let newFocusedIndex = focusedItemIndex;

    // If no item is focused, focus the first item
    if (newFocusedIndex === -1) {
      newFocusedIndex = 0;
    }

    // Handle arrow keys
    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        newFocusedIndex = Math.max(0, newFocusedIndex - 1);
        break;
      case 'ArrowDown':
        e.preventDefault();
        newFocusedIndex = Math.min(items.length - 1, newFocusedIndex + 1);
        break;
      case 'ArrowLeft':
        if (viewMode === 'grid') {
          e.preventDefault();
          newFocusedIndex = Math.max(0, newFocusedIndex - 1);
        }
        break;
      case 'ArrowRight':
        if (viewMode === 'grid') {
          e.preventDefault();
          newFocusedIndex = Math.min(items.length - 1, newFocusedIndex + 1);
        }
        break;
      case 'Home':
        e.preventDefault();
        newFocusedIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newFocusedIndex = items.length - 1;
        break;
      case 'Enter':
        e.preventDefault();
        if (newFocusedIndex >= 0 && newFocusedIndex < items.length) {
          const focusedItem = items[newFocusedIndex];
          handleItemDoubleClick(focusedItem);
        }
        break;
      case 'Backspace':
        e.preventDefault();
        // Navigate to parent folder if possible
        const parentFolder = breadcrumbs[breadcrumbs.length - 2];
        if (parentFolder) {
          navigateToFolder(parentFolder.id, parentFolder.name);
        }
        break;
      case 'a':
        // Select all items with Ctrl+A
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          setSelectedItems(items.map(item => item.id));
        }
        break;
      case 'c':
        // Copy selected items with Ctrl+C
        if ((e.ctrlKey || e.metaKey) && selectedItems.length > 0) {
          e.preventDefault();
          handleCopy(getSelectedItems());
        }
        break;
      case 'x':
        // Cut selected items with Ctrl+X
        if ((e.ctrlKey || e.metaKey) && selectedItems.length > 0) {
          e.preventDefault();
          handleCut(getSelectedItems());
        }
        break;
      case 'v':
        // Paste items with Ctrl+V
        if ((e.ctrlKey || e.metaKey) && clipboardItems.length > 0) {
          e.preventDefault();
          handlePaste(currentFolder);
        }
        break;
      case 'Delete':
        // Delete selected items
        if (selectedItems.length > 0) {
          e.preventDefault();
          setItemsToDelete(getSelectedItems());
          setDeleteModalOpen(true);
        }
        break;
      default:
        break;
    }

    // Update focused index if changed
    if (newFocusedIndex !== focusedItemIndex) {
      setFocusedItemIndex(newFocusedIndex);

      // Also select the focused item if not using Ctrl/Cmd key
      if (!e.ctrlKey && !e.metaKey && !e.shiftKey) {
        setSelectedItems([items[newFocusedIndex].id]);
      }
      // Add to selection with Shift key
      else if (e.shiftKey) {
        const startIndex = Math.min(focusedItemIndex, newFocusedIndex);
        const endIndex = Math.max(focusedItemIndex, newFocusedIndex);
        const itemsToSelect = items.slice(startIndex, endIndex + 1).map(item => item.id);
        setSelectedItems(prev => {
          const newSelection = new Set(prev);
          itemsToSelect.forEach(id => newSelection.add(id));
          return Array.from(newSelection);
        });
      }
    }
  }, [items, focusedItemIndex, viewMode, breadcrumbs, selectedItems, clipboardItems, currentFolder, handleItemDoubleClick, navigateToFolder, handleCopy, handleCut, handlePaste, getSelectedItems]);

  // Set up dropzone for file uploads
  const { getRootProps, getInputProps, isDragActive: isDropzoneActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      handleUpload(acceptedFiles, currentFolder);
    },
    noClick: true, // Disable click to open file dialog (we'll handle this manually)
    noKeyboard: true // Disable keyboard navigation (we'll handle this manually)
  });

  // Update drag active state
  useEffect(() => {
    setIsDragActive(isDropzoneActive);
  }, [isDropzoneActive]);

  // Context menu items for file explorer area
  const fileExplorerContextMenuItems = [
    {
      label: 'Upload Files',
      onClick: () => setUploadModalOpen(true)
    },
    {
      label: 'Create Folder',
      onClick: () => setCreateFolderModalOpen(true)
    },
    {
      label: 'Create Document',
      onClick: () => setCreateDocumentModalOpen(true)
    },
    {
      label: 'Paste',
      onClick: () => handlePaste(currentFolder),
      disabled: clipboardItems.length === 0 || clipboardOperation === null
    }
  ];

  // Global context menu state
  const [globalContextMenu, setGlobalContextMenu] = useState({
    isVisible: false,
    x: 0,
    y: 0,
    items: [] as any[]
  });

  // Handle showing context menu
  useEffect(() => {
    const handleShowContextMenu = (e: any) => {
      setGlobalContextMenu({
        isVisible: true,
        x: e.detail.x,
        y: e.detail.y,
        items: e.detail.items
      });
    };

    document.addEventListener('showContextMenu', handleShowContextMenu);

    return () => {
      document.removeEventListener('showContextMenu', handleShowContextMenu);
    };
  }, []);

  // Handle clicking outside context menu
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (globalContextMenu.isVisible) {
        setGlobalContextMenu(prev => ({ ...prev, isVisible: false }));
      }
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && globalContextMenu.isVisible) {
        setGlobalContextMenu(prev => ({ ...prev, isVisible: false }));
      }
    };

    document.addEventListener('mouseup', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mouseup', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [globalContextMenu.isVisible]);

  return (
    <DndProvider backend={HTML5Backend}>
      <ContextMenu items={fileExplorerContextMenuItems}>
        <div 
          {...getRootProps()}
          className={`bg-white rounded-lg shadow relative ${className} ${isDragActive ? 'ring-2 ring-primary-500 ring-opacity-50' : ''}`}
          tabIndex={0} // Make the div focusable
          onKeyDown={handleKeyDown} // Attach keyboard event handler
          onMouseDown={handleMouseDown} // Start drag selection
          onMouseMove={handleMouseMove} // Update drag selection
          onMouseUp={handleMouseUp} // End drag selection
          onMouseLeave={handleMouseUp} // End drag selection if mouse leaves the component
        >
          <input {...getInputProps()} />

          {/* Drag overlay */}
          {isDragActive && (
            <div className="absolute inset-0 bg-primary-100 bg-opacity-70 flex items-center justify-center z-10 pointer-events-none rounded-lg">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <svg
                  className="mx-auto h-12 w-12 text-primary-500 mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-1">Drop files here</h3>
                <p className="text-sm text-gray-500">Drop files to upload them to this folder</p>
              </div>
            </div>
          )}

          {/* Drag selection overlay */}
          {isDragSelecting && dragStartPosition && dragCurrentPosition && (
            <div 
              className="absolute bg-primary-500 bg-opacity-20 border border-primary-500 border-opacity-50 pointer-events-none z-10"
              style={{
                left: Math.min(dragStartPosition.x, dragCurrentPosition.x) + 'px',
                top: Math.min(dragStartPosition.y, dragCurrentPosition.y) + 'px',
                width: Math.abs(dragCurrentPosition.x - dragStartPosition.x) + 'px',
                height: Math.abs(dragCurrentPosition.y - dragStartPosition.y) + 'px',
                position: 'fixed'
              }}
            />
          )}

          {/* Toolbar */}
          <FileManagerToolbar
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onUpload={() => setUploadModalOpen(true)}
            onCreateFolder={() => setCreateFolderModalOpen(true)}
            onCreateDocument={() => setCreateDocumentModalOpen(true)}
            onSearch={handleSearch}
            searchQuery={searchQuery}
            selectedItems={getSelectedItems()}
            onDownload={handleDownload}
            onRename={(item) => {
              setItemToRename(item);
              setRenameModalOpen(true);
            }}
            onDelete={(items) => {
              setItemsToDelete(items);
              setDeleteModalOpen(true);
            }}
            onMove={handleInitiateMove}
            onManagePermissions={handleInitiatePermissions}
            onShare={handleInitiateShare}
            onCopy={handleCopy}
            onCut={handleCut}
            onPaste={() => handlePaste(currentFolder)}
            canPaste={clipboardItems.length > 0 && clipboardOperation !== null}
          />

          {/* Breadcrumb */}
          <FileManagerBreadcrumb
            items={breadcrumbs}
            onNavigate={navigateToFolder}
          />

          {/* Error message */}
          {error && (
            <div className="m-4 p-4 text-sm text-red-700 bg-red-100 rounded-md flex items-center">
              <svg className="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{error}</span>
              <button 
                onClick={() => setError(null)} 
                className="ml-auto text-red-700 hover:text-red-900"
                aria-label="Dismiss error"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          {/* Main content with folder explorer */}
          <div className="flex flex-row">
            {/* Folder Explorer */}
            <div className="w-64 border-r border-gray-200 p-4">
              <FileManagerFolderExplorer
                farmId={farmId || user?.farm_id || ''}
                currentFolder={currentFolder}
                onNavigate={navigateToFolder}
              />
            </div>

            {/* Main content area */}
            <div className="flex-1">
              {/* Loading state */}
              {loading ? (
                <div className="flex flex-col justify-center items-center h-64">
                  <svg
                    className="animate-spin h-10 w-10 text-primary-500 mb-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <p className="text-gray-600">Loading files and folders...</p>
                </div>
              ) : (
                <>
                  {/* Empty state */}
                  {items.length === 0 && (
                    <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 m-4">
                      <svg
                        className="h-12 w-12 text-gray-400 mb-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <p className="text-gray-500 mb-1">No files or folders found</p>
                      <p className="text-gray-400 text-sm mb-3">Drag and drop files here or use the buttons below</p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setUploadModalOpen(true)}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Upload files
                        </button>
                        <button
                          onClick={() => setCreateFolderModalOpen(true)}
                          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Create folder
                        </button>
                      </div>
                    </div>
                  )}

                  {/* File/Folder Grid or List */}
                  {viewMode === 'grid' ? (
                    <FileManagerGrid
                      items={items}
                      selectedItems={selectedItems}
                      focusedItemIndex={focusedItemIndex}
                      onSelect={handleItemSelect}
                      onDoubleClick={handleItemDoubleClick}
                      onMove={handleMove}
                      currentFolder={currentFolder}
                      onManagePermissions={handleInitiatePermissions}
                      onShare={handleInitiateShare}
                      onCopy={handleCopy}
                      onCut={handleCut}
                      onPaste={handlePaste}
                      canPaste={clipboardItems.length > 0 && clipboardOperation !== null}
                      registerItemRef={registerItemRef}
                    />
                  ) : (
                    <FileManagerList
                      items={items}
                      selectedItems={selectedItems}
                      focusedItemIndex={focusedItemIndex}
                      onSelect={handleItemSelect}
                      onDoubleClick={handleItemDoubleClick}
                      onSort={handleSortChange}
                      sortBy={sortBy}
                      sortDirection={sortDirection}
                      onMove={handleMove}
                      currentFolder={currentFolder}
                      onManagePermissions={handleInitiatePermissions}
                      onShare={handleInitiateShare}
                      onCopy={handleCopy}
                      onCut={handleCut}
                      onPaste={handlePaste}
                      canPaste={clipboardItems.length > 0 && clipboardOperation !== null}
                      registerItemRef={registerItemRef}
                    />
                  )}
                </>
              )}
            </div>
          </div>

          {/* Modals */}
          <FileManagerUploadModal
            isOpen={uploadModalOpen}
            onClose={() => setUploadModalOpen(false)}
            onUpload={(files) => handleUpload(files, currentFolder)}
          />

          <FileManagerCreateFolderModal
            isOpen={createFolderModalOpen}
            onClose={() => setCreateFolderModalOpen(false)}
            onCreateFolder={handleCreateFolder}
          />

          <FileManagerCreateDocumentModal
            isOpen={createDocumentModalOpen}
            onClose={() => setCreateDocumentModalOpen(false)}
            onCreateDocument={handleCreateDocument}
          />

          {itemToRename && (
            <FileManagerRenameModal
              isOpen={renameModalOpen}
              onClose={() => {
                setRenameModalOpen(false);
                setItemToRename(null);
              }}
              item={itemToRename}
              onRename={handleRename}
            />
          )}

          <FileManagerDeleteModal
            isOpen={deleteModalOpen}
            onClose={() => {
              setDeleteModalOpen(false);
              setItemsToDelete([]);
            }}
            items={itemsToDelete}
            onDelete={handleDelete}
          />

          {/* File Preview Modal */}
          <FileManagerPreview
            isOpen={previewModalOpen}
            onClose={() => {
              setPreviewModalOpen(false);
              setItemToPreview(null);
            }}
            file={itemToPreview}
          />

          {/* Move Modal */}
          <FileManagerMoveModal
            isOpen={moveModalOpen}
            onClose={() => {
              setMoveModalOpen(false);
              setItemsToMove([]);
            }}
            items={itemsToMove}
            onMove={handleMove}
            currentFolder={currentFolder}
            farmId={farmId || user?.farm_id || ''}
          />

          {/* Permission Modal */}
          <FileManagerPermissionModal
            isOpen={permissionModalOpen}
            onClose={() => {
              setPermissionModalOpen(false);
              setItemToManagePermissions(null);
            }}
            item={itemToManagePermissions}
          />

          {/* Share Modal */}
          <FileManagerShareModal
            isOpen={shareModalOpen}
            onClose={() => {
              setShareModalOpen(false);
              setItemToShare(null);
            }}
            item={itemToShare}
          />
        </div>
      </ContextMenu>
    </DndProvider>
  );
};

export default FileManagerWithExplorer;
