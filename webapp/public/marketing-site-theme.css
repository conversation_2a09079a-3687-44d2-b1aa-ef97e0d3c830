/* nxtAcre Marketing Website Styles */

/* Global Styles */
.marketing-site-theme {
    --primary-color: #0ea5e9;
    --secondary-color: #0284c7;
    --accent-color: #38bdf8;
    --text-color: #333333;
    --light-color: #F8F9FA;
    --dark-color: #212529;
    --gray-color: #6C757D;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
}

.marketing-site-theme a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.marketing-site-theme a:hover {
    color: var(--secondary-color);
}

.marketing-site-theme .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.marketing-site-theme .btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.marketing-site-theme .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.marketing-site-theme .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Navigation */
.marketing-site-theme .navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.marketing-site-theme .navbar-brand img {
    max-height: 40px;
}

/* Hero Section */
.marketing-site-theme .hero-section {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.marketing-site-theme .hero-section h1 {
    color: var(--dark-color);
    font-weight: 700;
}

.marketing-site-theme .hero-section .lead {
    font-size: 1.25rem;
    color: var(--gray-color);
}

.marketing-site-theme .hero-section img {
    max-width: 100%;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Features Section */
.marketing-site-theme #features {
    padding: 80px 0;
}

.marketing-site-theme .feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(14, 165, 233, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.marketing-site-theme .feature-icon i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

/* Override Tailwind styles for the marketplace pages */
.marketing-site-theme h1, 
.marketing-site-theme h2, 
.marketing-site-theme h3, 
.marketing-site-theme h4, 
.marketing-site-theme h5, 
.marketing-site-theme h6 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
}

.marketing-site-theme .text-primary-600 {
    color: var(--primary-color) !important;
}

.marketing-site-theme .bg-primary-500 {
    background-color: var(--primary-color) !important;
}

.marketing-site-theme .border-primary-500 {
    border-color: var(--primary-color) !important;
}