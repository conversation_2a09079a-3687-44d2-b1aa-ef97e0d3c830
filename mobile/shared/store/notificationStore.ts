import { create } from 'zustand';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define notification settings interface
interface NotificationSettings {
  taskAssignments: boolean;
  dueDateAlerts: boolean;
  statusChanges: boolean;
  systemAnnouncements: boolean;
  newDeliveries: boolean;
  deliveryUpdates: boolean;
  scheduleChanges: boolean;
  messages: boolean;
}

// Define notification store state interface
interface NotificationState {
  isNotificationsEnabled: boolean;
  notificationSettings: NotificationSettings;
  pushToken: string | null;
  expoPushToken: string | null;
  initializePushNotifications: () => Promise<void>;
  toggleNotifications: (enabled: boolean) => void;
  updateNotificationSetting: (key: keyof NotificationSettings, value: boolean) => void;
  saveNotificationSettings: () => Promise<void>;
}

// Create notification store
export const useNotificationStore = create<NotificationState>((set, get) => ({
  isNotificationsEnabled: true,
  notificationSettings: {
    taskAssignments: true,
    dueDateAlerts: true,
    statusChanges: true,
    systemAnnouncements: true,
    newDeliveries: true,
    deliveryUpdates: true,
    scheduleChanges: true,
    messages: true,
  },
  pushToken: null,
  expoPushToken: null,

  // Initialize push notifications
  initializePushNotifications: async () => {
    if (!Device.isDevice) {
      console.log('Push notifications are not available in the simulator');
      return;
    }

    try {
      // Load saved notification settings
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        set({
          isNotificationsEnabled: parsedSettings.isNotificationsEnabled,
          notificationSettings: parsedSettings.notificationSettings,
        });
      }

      // Configure notification handler
      await configureNotifications();

      // Check for existing permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // If no existing permissions, request them
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // If permissions not granted, exit
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      // Get push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PROJECT_ID,
      });

      // Update store with token
      set({
        pushToken: token.data,
        expoPushToken: token.data,
      });

      // Configure Android channel
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }
    } catch (error) {
      console.error('Error initializing push notifications:', error);
    }
  },

  // Toggle notifications on/off
  toggleNotifications: (enabled) => {
    set({ isNotificationsEnabled: enabled });
    get().saveNotificationSettings();
  },

  // Update a specific notification setting
  updateNotificationSetting: (key, value) => {
    set((state) => ({
      notificationSettings: {
        ...state.notificationSettings,
        [key]: value,
      },
    }));
    get().saveNotificationSettings();
  },

  // Save notification settings to AsyncStorage
  saveNotificationSettings: async () => {
    try {
      const { isNotificationsEnabled, notificationSettings } = get();
      await AsyncStorage.setItem(
        'notificationSettings',
        JSON.stringify({
          isNotificationsEnabled,
          notificationSettings,
        })
      );
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  },
}));

// Configure notification handler
async function configureNotifications() {
  // Set notification handler
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      shouldShowBanner: true,
      shouldShowList: true,
    }),
  });
}
