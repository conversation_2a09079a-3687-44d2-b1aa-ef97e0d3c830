// Test script for Enhanced Notification Grouping
const { 
  sendUnifiedNotification, 
  NotificationType, 
  AppIdentifier,
  getUnifiedNotifications,
  clearAllNotifications
} = require('../services/unifiedNotificationService');

// Clear all notifications before testing
async function clearNotifications() {
  console.log('Clearing all notifications...');
  await clearAllNotifications();
  const notifications = await getUnifiedNotifications();
  console.log(`Notifications after clearing: ${notifications.length}`);
}

// Test sending notifications with similar content
async function testContentBasedGrouping() {
  console.log('\n--- Testing Content-Based Grouping ---');
  
  // Send notifications with similar content
  console.log('Sending notifications with similar content...');
  
  await sendUnifiedNotification({
    type: NotificationType.TASK_ASSIGNMENT,
    title: 'Field inspection required',
    body: 'Please inspect the north wheat field for signs of disease',
    sourceApp: AppIdentifier.FARM_MANAGER,
    actionable: true,
    priority: 'default'
  });
  
  await sendUnifiedNotification({
    type: NotificationType.SYSTEM_ANNOUNCEMENT,
    title: 'Disease alert',
    body: 'Potential disease detected in wheat fields, inspection recommended',
    sourceApp: AppIdentifier.FIELD_OPERATIONS,
    actionable: false,
    priority: 'high'
  });
  
  await sendUnifiedNotification({
    type: NotificationType.TASK_ASSIGNMENT,
    title: 'Equipment maintenance',
    body: 'Tractor needs oil change and filter replacement',
    sourceApp: AppIdentifier.INVENTORY_EQUIPMENT,
    actionable: true,
    priority: 'default'
  });
  
  // Get notifications and check grouping
  const notifications = await getUnifiedNotifications();
  console.log(`Total notifications: ${notifications.length}`);
  
  // Check for grouping metadata
  notifications.forEach(notification => {
    console.log(`\nNotification: ${notification.title}`);
    console.log(`Original priority: ${notification.priority}`);
    console.log(`ML-adjusted priority: ${notification.mlAdjustedPriority || 'None'}`);
    
    if (notification.data && notification.data.groupedNotificationIds) {
      console.log(`Grouped with ${notification.data.groupedNotificationIds.length} other notifications`);
      console.log(`Grouping reason: ${notification.data.groupingReason}`);
    } else {
      console.log('Not grouped with any other notifications');
    }
  });
}

// Test sending notifications from the same app in a short time window
async function testTimeBasedGrouping() {
  console.log('\n--- Testing Time-Based Grouping ---');
  
  // Send notifications from the same app in quick succession
  console.log('Sending notifications from the same app...');
  
  await sendUnifiedNotification({
    type: NotificationType.INVENTORY_ALERT,
    title: 'Low seed inventory',
    body: 'Corn seed inventory is below threshold',
    sourceApp: AppIdentifier.INVENTORY_EQUIPMENT,
    actionable: false,
    priority: 'default'
  });
  
  await sendUnifiedNotification({
    type: NotificationType.MAINTENANCE_REMINDER,
    title: 'Equipment maintenance due',
    body: 'Scheduled maintenance for tractor #5 is due',
    sourceApp: AppIdentifier.INVENTORY_EQUIPMENT,
    actionable: true,
    priority: 'default'
  });
  
  await sendUnifiedNotification({
    type: NotificationType.INVENTORY_ALERT,
    title: 'Parts arrived',
    body: 'Ordered parts for the harvester have arrived',
    sourceApp: AppIdentifier.INVENTORY_EQUIPMENT,
    actionable: false,
    priority: 'low'
  });
  
  // Get notifications and check grouping
  const notifications = await getUnifiedNotifications();
  console.log(`Total notifications: ${notifications.length}`);
  
  // Check for grouping metadata
  notifications.forEach(notification => {
    console.log(`\nNotification: ${notification.title}`);
    console.log(`Source app: ${notification.sourceApp}`);
    
    if (notification.data && notification.data.groupedNotificationIds) {
      console.log(`Grouped with ${notification.data.groupedNotificationIds.length} other notifications`);
      console.log(`Grouping reason: ${notification.data.groupingReason}`);
    } else {
      console.log('Not grouped with any other notifications');
    }
  });
}

// Run the tests
async function runTests() {
  try {
    await clearNotifications();
    await testContentBasedGrouping();
    await clearNotifications();
    await testTimeBasedGrouping();
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

runTests();