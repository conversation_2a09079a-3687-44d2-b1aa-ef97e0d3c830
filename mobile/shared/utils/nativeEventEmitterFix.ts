import { Platform } from 'react-native';

/**
 * Fix for the "EventEmitter.removeListener" warning in React Native
 * This is a known issue with React Native's NativeEventEmitter
 * See: https://github.com/facebook/react-native/issues/30235
 */
export const initializeNativeEventEmitterFix = (): void => {
  // Skip the fix entirely for now to avoid runtime errors
  // This is a non-critical warning fix that can be addressed later
  try {
    console.log('NativeEventEmitter fix skipped - preventing runtime errors');
  } catch (error) {
    console.warn('NativeEventEmitter fix error:', error);
  }
};

/**
 * Utility function to safely add event listeners that will be properly cleaned up
 * @param emitter The event emitter to add the listener to
 * @param eventName The name of the event to listen for
 * @param callback The callback function to call when the event is emitted
 * @returns A function that removes the listener when called
 */
export const safeAddEventListener = (
  emitter: any,
  eventName: string,
  callback: (...args: any[]) => void
): (() => void) => {
  if (!emitter || !emitter.addListener) {
    console.warn(`Cannot add listener for "${eventName}" - emitter is invalid`);
    return () => {};
  }
  
  // Add the listener
  const subscription = emitter.addListener(eventName, callback);
  
  // Return a cleanup function
  return () => {
    if (subscription && subscription.remove) {
      subscription.remove();
    } else if (emitter && emitter.removeListener) {
      emitter.removeListener(eventName, callback);
    }
  };
};