import { Platform } from 'react-native';

/**
 * Fix for the "EventEmitter.removeListener" warning in React Native
 * This is a known issue with React Native's NativeEventEmitter
 * See: https://github.com/facebook/react-native/issues/30235
 */
export const initializeNativeEventEmitterFix = (): void => {
  if (Platform.OS !== 'android') {
    return;
  }

  // Only apply this fix for Android
  try {
    // Try multiple possible module paths for different React Native versions
    let NativeEventEmitter;

    try {
      // Try the newer React Native 0.79+ path
      NativeEventEmitter = require('react-native/Libraries/EventEmitter/NativeEventEmitter');
      // Handle both default export and direct export
      if (NativeEventEmitter.default) {
        NativeEventEmitter = NativeEventEmitter.default;
      }
    } catch (firstError) {
      try {
        // Try alternative import path
        const { NativeEventEmitter: NEE } = require('react-native');
        NativeEventEmitter = NEE;
      } catch (secondError) {
        console.warn('Could not find NativeEventEmitter module:', firstError.message, secondError.message);
        return;
      }
    }

    // Check if NativeEventEmitter exists and has prototype
    if (!NativeEventEmitter || !NativeEventEmitter.prototype) {
      console.warn('NativeEventEmitter or its prototype is undefined');
      return;
    }

    // Save the original removeListener method
    const originalRemoveListener = NativeEventEmitter.prototype.removeListener;

    if (!originalRemoveListener) {
      console.warn('NativeEventEmitter.prototype.removeListener is undefined');
      return;
    }

    // Override the removeListener method to handle null/undefined listeners
    NativeEventEmitter.prototype.removeListener = function(eventType: string, listener: Function) {
      if (listener) {
        // If listener exists, call the original method
        return originalRemoveListener.call(this, eventType, listener);
      }

      // If listener is null/undefined, do nothing
      return this;
    };

    console.log('NativeEventEmitter fix applied successfully');
  } catch (error) {
    console.warn('Failed to apply NativeEventEmitter fix:', error);
  }
};

/**
 * Utility function to safely add event listeners that will be properly cleaned up
 * @param emitter The event emitter to add the listener to
 * @param eventName The name of the event to listen for
 * @param callback The callback function to call when the event is emitted
 * @returns A function that removes the listener when called
 */
export const safeAddEventListener = (
  emitter: any,
  eventName: string,
  callback: (...args: any[]) => void
): (() => void) => {
  if (!emitter || !emitter.addListener) {
    console.warn(`Cannot add listener for "${eventName}" - emitter is invalid`);
    return () => {};
  }
  
  // Add the listener
  const subscription = emitter.addListener(eventName, callback);
  
  // Return a cleanup function
  return () => {
    if (subscription && subscription.remove) {
      subscription.remove();
    } else if (emitter && emitter.removeListener) {
      emitter.removeListener(eventName, callback);
    }
  };
};