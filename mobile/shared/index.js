// Components
export { default as SplashScreen } from './components/SplashScreen';
export { default as HelpTip } from './components/HelpTips';
export { default as UnifiedNotificationCenter } from './components/UnifiedNotificationCenter';
export { default as NotificationButton } from './components/NotificationButton';
export { default as NotificationPreferencesScreen } from './components/NotificationPreferencesScreen';

// Store
export { default as AuthProvider } from './store/AuthProvider';
export { useAuth } from './store/AuthProvider';
export { useNotificationStore } from './store/notificationStore';

// Utils
export * from './utils/nativeEventEmitterFix';

// Hooks
export { default as useSynchronization } from './hooks/useSynchronization';
export { default as useEnhancedSynchronization } from './hooks/useEnhancedSynchronization';

// Services
export * from './services/notificationService';
export * from './services/unifiedNotificationService';
export * from './services/voiceCommandService';
export * from './services/deepLinkingService';
export * from './services/emailService';
export * from './services/etaUpdateService';
export * from './services/expenseService';
export * from './services/financialService';
export * from './services/navigationHistoryService';
export * from './services/navigationService';
export * from './services/offlineMapService';
export * from './services/smsService';

// Add a comment to explain how to use these shared components and utilities
/**
 * NxtAcre Shared Components and Utilities
 * 
 * This package provides shared components and utilities for NxtAcre mobile apps.
 * 
 * Usage:
 * 
 * 1. Import the components and utilities in your app:
 *    ```
 *    import { SplashScreen, HelpTip, useFadeIn, initSentry } from '@nxtacre/shared';
 *    ```
 * 
 * 2. Use the components in your app:
 *    ```
 *    // Custom splash screen with loading indicator
 *    const App = () => {
 *      const [isReady, setIsReady] = useState(false);
 *      
 *      if (!isReady) {
 *        return <SplashScreen onFinish={() => setIsReady(true)} />;
 *      }
 *      
 *      return <MainApp />;
 *    };
 *    
 *    // Help tip
 *    const MyScreen = () => {
 *      return (
 *        <View>
 *          <Text>Some content</Text>
 *          <HelpTip 
 *            title="Help" 
 *            content="This is a help tip that explains how to use this feature."
 *          />
 *        </View>
 *      );
 *    };
 *    
 *    // Animations
 *    const AnimatedComponent = () => {
 *      const { opacity, fadeIn } = useFadeIn();
 *      
 *      useEffect(() => {
 *        fadeIn();
 *      }, []);
 *      
 *      return (
 *        <Animated.View style={{ opacity }}>
 *          <Text>This text fades in</Text>
 *        </Animated.View>
 *      );
 *    };
 *    
 *    // Sentry integration
 *    const initApp = () => {
 *      initSentry('my-app', 'https://your-sentry-dsn');
 *    };
 *    ```
 */
