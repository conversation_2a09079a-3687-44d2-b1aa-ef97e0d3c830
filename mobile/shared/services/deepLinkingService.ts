import { Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppIdentifier } from './unifiedNotificationService';

// Define deep link route storage key
const DEEP_LINK_ROUTES_KEY = 'deepLinkRoutes';

// Define deep link route structure
export interface DeepLinkRoute {
  pattern: string;       // URL pattern (e.g., 'nxtacre://employee/tasks/:taskId')
  appIdentifier: AppIdentifier; // Target app
  screenName: string;    // Screen name in the app's navigation
  paramsMapping?: Record<string, string>; // Map URL params to screen params
}

// Define navigation history entry
export interface NavigationHistoryEntry {
  id: string;            // Unique identifier
  timestamp: number;     // When the navigation occurred
  sourceApp?: AppIdentifier; // Source app (if cross-app navigation)
  targetApp: AppIdentifier; // Target app
  deepLink: string;      // The deep link that was navigated to
  screenName: string;    // The screen that was navigated to
  params?: Record<string, any>; // The parameters passed to the screen
}

// Define navigation history storage key
const NAVIGATION_HISTORY_KEY = 'navigationHistory';
const MAX_HISTORY_ENTRIES = 50;

/**
 * Register a deep link route
 * @param route The route to register
 * @returns Promise that resolves when the route is registered
 */
export const registerDeepLinkRoute = async (route: DeepLinkRoute): Promise<void> => {
  try {
    // Get existing routes
    const routes = await getDeepLinkRoutes();

    // Check if route already exists
    const existingRouteIndex = routes.findIndex(r => r.pattern === route.pattern);
    if (existingRouteIndex >= 0) {
      // Update existing route
      routes[existingRouteIndex] = route;
    } else {
      // Add new route
      routes.push(route);
    }

    // Save updated routes
    await AsyncStorage.setItem(DEEP_LINK_ROUTES_KEY, JSON.stringify(routes));
  } catch (error) {
    console.error('Error registering deep link route:', error);
  }
};

/**
 * Get all registered deep link routes
 * @returns Promise that resolves to an array of deep link routes
 */
export const getDeepLinkRoutes = async (): Promise<DeepLinkRoute[]> => {
  try {
    const storedRoutes = await AsyncStorage.getItem(DEEP_LINK_ROUTES_KEY);
    if (!storedRoutes) {
      return [];
    }

    return JSON.parse(storedRoutes);
  } catch (error) {
    console.error('Error getting deep link routes:', error);
    return [];
  }
};

/**
 * Parse a deep link URL
 * @param url The URL to parse
 * @returns Object with the matched route and extracted parameters, or null if no match
 */
export const parseDeepLink = async (url: string): Promise<{
  route: DeepLinkRoute;
  params: Record<string, string>;
} | null> => {
  try {
    // Get all registered routes
    const routes = await getDeepLinkRoutes();

    // Try to match the URL to a route
    for (const route of routes) {
      const match = matchUrlToPattern(url, route.pattern);
      if (match) {
        return {
          route,
          params: match,
        };
      }
    }

    return null;
  } catch (error) {
    console.error('Error parsing deep link:', error);
    return null;
  }
};

/**
 * Match a URL to a pattern
 * @param url The URL to match
 * @param pattern The pattern to match against
 * @returns Object with extracted parameters, or null if no match
 */
const matchUrlToPattern = (url: string, pattern: string): Record<string, string> | null => {
  // Convert pattern to regex
  const regexPattern = pattern
    .replace(/:[a-zA-Z0-9]+/g, '([^/]+)') // Replace :param with capture group
    .replace(/\//g, '\\/') // Escape slashes
    .replace(/\./g, '\\.'); // Escape dots

  // Extract param names from pattern
  const paramNames: string[] = [];
  const paramMatches = pattern.matchAll(/:([a-zA-Z0-9]+)/g);
  for (const match of paramMatches) {
    paramNames.push(match[1]);
  }

  // Create regex
  const regex = new RegExp(`^${regexPattern}$`);

  // Match URL
  const match = url.match(regex);
  if (!match) {
    return null;
  }

  // Extract params
  const params: Record<string, string> = {};
  for (let i = 0; i < paramNames.length; i++) {
    params[paramNames[i]] = match[i + 1];
  }

  return params;
};

/**
 * Handle a deep link
 * @param url The URL to handle
 * @param currentApp The current app identifier
 * @param navigation The navigation object from React Navigation
 * @returns Promise that resolves to true if the deep link was handled, false otherwise
 */
export const handleDeepLink = async (
  url: string,
  currentApp: AppIdentifier,
  navigation: any
): Promise<boolean> => {
  try {
    // Parse the deep link
    const parsed = await parseDeepLink(url);
    if (!parsed) {
      console.log(`No matching route found for URL: ${url}`);
      return false;
    }

    const { route, params } = parsed;

    // Map URL params to screen params if needed
    const screenParams: Record<string, any> = {};
    if (route.paramsMapping) {
      for (const [screenParam, urlParam] of Object.entries(route.paramsMapping)) {
        screenParams[screenParam] = params[urlParam];
      }
    } else {
      // Use URL params directly
      Object.assign(screenParams, params);
    }

    // Check if the target app is the current app
    if (route.appIdentifier === currentApp) {
      // Navigate within the current app
      navigation.navigate(route.screenName, screenParams);

      // Record navigation history
      await recordNavigationHistory({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        targetApp: route.appIdentifier,
        deepLink: url,
        screenName: route.screenName,
        params: screenParams,
      });

      return true;
    } else {
      // Cross-app navigation
      // Record navigation history with source app
      await recordNavigationHistory({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        sourceApp: currentApp,
        targetApp: route.appIdentifier,
        deepLink: url,
        screenName: route.screenName,
        params: screenParams,
      });

      // Open the target app with the deep link
      const appUrl = `${route.appIdentifier}://deeplink?url=${encodeURIComponent(url)}`;
      const canOpen = await Linking.canOpenURL(appUrl);
      
      if (canOpen) {
        await Linking.openURL(appUrl);
        return true;
      } else {
        console.log(`Cannot open URL: ${appUrl}`);
        return false;
      }
    }
  } catch (error) {
    console.error('Error handling deep link:', error);
    return false;
  }
};

/**
 * Record a navigation history entry
 * @param entry The navigation history entry to record
 */
export const recordNavigationHistory = async (entry: NavigationHistoryEntry): Promise<void> => {
  try {
    // Get existing history
    const history = await getNavigationHistory();

    // Add new entry at the beginning
    const updatedHistory = [entry, ...history];

    // Limit the number of entries
    const limitedHistory = updatedHistory.slice(0, MAX_HISTORY_ENTRIES);

    // Save updated history
    await AsyncStorage.setItem(NAVIGATION_HISTORY_KEY, JSON.stringify(limitedHistory));
  } catch (error) {
    console.error('Error recording navigation history:', error);
  }
};

/**
 * Get navigation history
 * @returns Promise that resolves to an array of navigation history entries
 */
export const getNavigationHistory = async (): Promise<NavigationHistoryEntry[]> => {
  try {
    const storedHistory = await AsyncStorage.getItem(NAVIGATION_HISTORY_KEY);
    if (!storedHistory) {
      return [];
    }

    return JSON.parse(storedHistory);
  } catch (error) {
    console.error('Error getting navigation history:', error);
    return [];
  }
};

/**
 * Get navigation history for a specific app
 * @param appIdentifier The app identifier to filter by
 * @returns Promise that resolves to an array of navigation history entries
 */
export const getAppNavigationHistory = async (
  appIdentifier: AppIdentifier
): Promise<NavigationHistoryEntry[]> => {
  const history = await getNavigationHistory();
  return history.filter(
    entry => entry.sourceApp === appIdentifier || entry.targetApp === appIdentifier
  );
};

/**
 * Clear navigation history
 * @param appIdentifier The app identifier to filter by (optional)
 */
export const clearNavigationHistory = async (
  appIdentifier?: AppIdentifier
): Promise<void> => {
  try {
    if (appIdentifier) {
      // Clear history for a specific app
      const history = await getNavigationHistory();
      const updatedHistory = history.filter(
        entry => entry.sourceApp !== appIdentifier && entry.targetApp !== appIdentifier
      );
      await AsyncStorage.setItem(NAVIGATION_HISTORY_KEY, JSON.stringify(updatedHistory));
    } else {
      // Clear all history
      await AsyncStorage.removeItem(NAVIGATION_HISTORY_KEY);
    }
  } catch (error) {
    console.error('Error clearing navigation history:', error);
  }
};

/**
 * Initialize the deep linking service
 * @param appIdentifier The current app identifier
 * @param navigation The navigation object from React Navigation
 */
export const initializeDeepLinkingService = (
  appIdentifier: AppIdentifier,
  navigation: any
): void => {
  // Set up URL handler
  Linking.addEventListener('url', ({ url }) => {
    handleDeepLink(url, appIdentifier, navigation);
  });

  // Handle initial URL
  Linking.getInitialURL().then(url => {
    if (url) {
      handleDeepLink(url, appIdentifier, navigation);
    }
  });
};

/**
 * Navigate back to the previous app/screen in the navigation history
 * @param currentApp The current app identifier
 * @param navigation The navigation object from React Navigation
 * @returns Promise that resolves to true if navigation was successful, false otherwise
 */
export const navigateBack = async (
  currentApp: AppIdentifier,
  navigation: any
): Promise<boolean> => {
  try {
    // Get navigation history
    const history = await getNavigationHistory();
    
    // Find the most recent entry where the current app was the target
    const currentAppEntryIndex = history.findIndex(entry => entry.targetApp === currentApp);
    
    if (currentAppEntryIndex < 0 || currentAppEntryIndex === history.length - 1) {
      // No history or already at the oldest entry
      return false;
    }
    
    // Get the previous entry
    const previousEntry = history[currentAppEntryIndex + 1];
    
    if (previousEntry.targetApp === currentApp) {
      // Previous entry is in the same app, navigate within the app
      navigation.navigate(previousEntry.screenName, previousEntry.params);
      return true;
    } else {
      // Previous entry is in a different app, open that app
      const appUrl = `${previousEntry.targetApp}://deeplink?url=${encodeURIComponent(previousEntry.deepLink)}`;
      const canOpen = await Linking.canOpenURL(appUrl);
      
      if (canOpen) {
        await Linking.openURL(appUrl);
        return true;
      } else {
        console.log(`Cannot open URL: ${appUrl}`);
        return false;
      }
    }
  } catch (error) {
    console.error('Error navigating back:', error);
    return false;
  }
};

/**
 * Create a deep link URL
 * @param appIdentifier The target app identifier
 * @param screenName The target screen name
 * @param params The parameters to pass to the screen
 * @returns The deep link URL
 */
export const createDeepLink = (
  appIdentifier: AppIdentifier,
  screenName: string,
  params?: Record<string, any>
): string => {
  let url = `nxtacre://${appIdentifier}/${screenName}`;
  
  if (params && Object.keys(params).length > 0) {
    // Add params to URL
    const paramStrings = Object.entries(params).map(([key, value]) => {
      return `${key}=${encodeURIComponent(String(value))}`;
    });
    url += `?${paramStrings.join('&')}`;
  }
  
  return url;
};