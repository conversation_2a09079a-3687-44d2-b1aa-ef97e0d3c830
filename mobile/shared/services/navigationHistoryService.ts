import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppIdentifier } from './unifiedNotificationService';

// Define storage key for navigation history
const NAVIGATION_HISTORY_KEY = 'navigationHistory';
const MAX_HISTORY_ITEMS = 100;

// Define navigation history item interface
export interface NavigationHistoryItem {
  id: string;
  sourceApp: AppIdentifier;
  targetApp: AppIdentifier;
  sourceRoute: string;
  targetRoute: string;
  timestamp: number;
  params?: Record<string, any>;
  title?: string;
  contextId?: string; // For grouping related navigation events
}

// Define navigation context interface
export interface NavigationContext {
  id: string;
  title: string;
  startTimestamp: number;
  lastUpdatedTimestamp: number;
  appSequence: AppIdentifier[];
  active: boolean;
}

// Define navigation history service
class NavigationHistoryService {
  private activeContext: NavigationContext | null = null;
  
  /**
   * Record a navigation event between apps
   * @param sourceApp The app navigating from
   * @param targetApp The app navigating to
   * @param sourceRoute The route in the source app
   * @param targetRoute The route in the target app
   * @param params Optional parameters passed to the target route
   * @param title Optional title for the navigation event
   * @returns The ID of the recorded history item
   */
  async recordNavigation(
    sourceApp: AppIdentifier,
    targetApp: AppIdentifier,
    sourceRoute: string,
    targetRoute: string,
    params?: Record<string, any>,
    title?: string
  ): Promise<string> {
    try {
      // Generate a unique ID
      const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Create the history item
      const historyItem: NavigationHistoryItem = {
        id,
        sourceApp,
        targetApp,
        sourceRoute,
        targetRoute,
        timestamp: Date.now(),
        params,
        title,
        contextId: this.activeContext?.id,
      };
      
      // Get existing history
      const history = await this.getNavigationHistory();
      
      // Add the new item at the beginning
      const updatedHistory = [historyItem, ...history];
      
      // Limit the number of stored items
      const limitedHistory = updatedHistory.slice(0, MAX_HISTORY_ITEMS);
      
      // Store the updated history
      await AsyncStorage.setItem(
        NAVIGATION_HISTORY_KEY,
        JSON.stringify(limitedHistory)
      );
      
      // Update the active context if it exists
      if (this.activeContext) {
        this.activeContext.lastUpdatedTimestamp = Date.now();
        this.activeContext.appSequence.push(targetApp);
        await this.updateNavigationContext(this.activeContext);
      }
      
      console.log(`Recorded navigation from ${sourceApp} to ${targetApp}`);
      return id;
    } catch (error) {
      console.error('Error recording navigation:', error);
      return '';
    }
  }
  
  /**
   * Get the navigation history
   * @param limit Optional limit on the number of items to return
   * @returns Promise that resolves to an array of navigation history items
   */
  async getNavigationHistory(limit?: number): Promise<NavigationHistoryItem[]> {
    try {
      const storedHistory = await AsyncStorage.getItem(NAVIGATION_HISTORY_KEY);
      if (!storedHistory) {
        return [];
      }
      
      const history: NavigationHistoryItem[] = JSON.parse(storedHistory);
      
      if (limit && limit > 0) {
        return history.slice(0, limit);
      }
      
      return history;
    } catch (error) {
      console.error('Error getting navigation history:', error);
      return [];
    }
  }
  
  /**
   * Get navigation history for a specific context
   * @param contextId The context ID
   * @returns Promise that resolves to an array of navigation history items
   */
  async getNavigationHistoryByContext(contextId: string): Promise<NavigationHistoryItem[]> {
    try {
      const history = await this.getNavigationHistory();
      return history.filter(item => item.contextId === contextId);
    } catch (error) {
      console.error('Error getting navigation history by context:', error);
      return [];
    }
  }
  
  /**
   * Clear the navigation history
   */
  async clearNavigationHistory(): Promise<void> {
    try {
      await AsyncStorage.removeItem(NAVIGATION_HISTORY_KEY);
      console.log('Navigation history cleared');
    } catch (error) {
      console.error('Error clearing navigation history:', error);
    }
  }
  
  /**
   * Start a new navigation context
   * @param title The title for the context
   * @param initialApp The app that started the context
   * @returns The ID of the created context
   */
  async startNavigationContext(
    title: string,
    initialApp: AppIdentifier
  ): Promise<string> {
    try {
      // Generate a unique ID
      const id = `context-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Create the context
      const context: NavigationContext = {
        id,
        title,
        startTimestamp: Date.now(),
        lastUpdatedTimestamp: Date.now(),
        appSequence: [initialApp],
        active: true,
      };
      
      // Set as the active context
      this.activeContext = context;
      
      // Store the context
      await this.updateNavigationContext(context);
      
      console.log(`Started navigation context: ${title}`);
      return id;
    } catch (error) {
      console.error('Error starting navigation context:', error);
      return '';
    }
  }
  
  /**
   * End the current navigation context
   */
  async endNavigationContext(): Promise<void> {
    try {
      if (this.activeContext) {
        this.activeContext.active = false;
        await this.updateNavigationContext(this.activeContext);
        this.activeContext = null;
        console.log('Navigation context ended');
      }
    } catch (error) {
      console.error('Error ending navigation context:', error);
    }
  }
  
  /**
   * Get all navigation contexts
   * @returns Promise that resolves to an array of navigation contexts
   */
  async getNavigationContexts(): Promise<NavigationContext[]> {
    try {
      const contexts = await AsyncStorage.getItem('navigationContexts');
      if (!contexts) {
        return [];
      }
      
      return JSON.parse(contexts);
    } catch (error) {
      console.error('Error getting navigation contexts:', error);
      return [];
    }
  }
  
  /**
   * Get a specific navigation context
   * @param contextId The context ID
   * @returns Promise that resolves to the navigation context or null if not found
   */
  async getNavigationContext(contextId: string): Promise<NavigationContext | null> {
    try {
      const contexts = await this.getNavigationContexts();
      return contexts.find(context => context.id === contextId) || null;
    } catch (error) {
      console.error('Error getting navigation context:', error);
      return null;
    }
  }
  
  /**
   * Update a navigation context
   * @param context The context to update
   */
  private async updateNavigationContext(context: NavigationContext): Promise<void> {
    try {
      const contexts = await this.getNavigationContexts();
      
      // Find and update the context if it exists
      const existingIndex = contexts.findIndex(c => c.id === context.id);
      if (existingIndex >= 0) {
        contexts[existingIndex] = context;
      } else {
        contexts.push(context);
      }
      
      // Store the updated contexts
      await AsyncStorage.setItem('navigationContexts', JSON.stringify(contexts));
    } catch (error) {
      console.error('Error updating navigation context:', error);
    }
  }
  
  /**
   * Navigate back to the previous app in the current context
   * @returns Promise that resolves to the previous navigation history item or null if none
   */
  async navigateBack(): Promise<NavigationHistoryItem | null> {
    try {
      if (!this.activeContext) {
        console.log('No active navigation context');
        return null;
      }
      
      // Get history for the current context
      const contextHistory = await this.getNavigationHistoryByContext(this.activeContext.id);
      
      // If there's only one item or no items, we can't go back
      if (contextHistory.length <= 1) {
        console.log('No previous navigation in this context');
        return null;
      }
      
      // Get the most recent item (should be at index 0)
      const currentItem = contextHistory[0];
      
      // Get the previous item
      const previousItem = contextHistory[1];
      
      // Update the app sequence in the context
      if (this.activeContext.appSequence.length > 1) {
        this.activeContext.appSequence.pop();
        await this.updateNavigationContext(this.activeContext);
      }
      
      console.log(`Navigating back from ${currentItem.targetApp} to ${previousItem.targetApp}`);
      return previousItem;
    } catch (error) {
      console.error('Error navigating back:', error);
      return null;
    }
  }
  
  /**
   * Get the active navigation context
   * @returns The active navigation context or null if none
   */
  getActiveContext(): NavigationContext | null {
    return this.activeContext;
  }
  
  /**
   * Set the active navigation context
   * @param contextId The context ID to set as active
   */
  async setActiveContext(contextId: string): Promise<boolean> {
    try {
      const context = await this.getNavigationContext(contextId);
      if (context) {
        // End the current active context if there is one
        if (this.activeContext) {
          await this.endNavigationContext();
        }
        
        // Set the new context as active
        context.active = true;
        this.activeContext = context;
        await this.updateNavigationContext(context);
        console.log(`Set active context: ${context.title}`);
        return true;
      }
      
      console.log(`Context not found: ${contextId}`);
      return false;
    } catch (error) {
      console.error('Error setting active context:', error);
      return false;
    }
  }
}

export default new NavigationHistoryService();