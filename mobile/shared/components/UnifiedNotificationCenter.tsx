import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import * as unifiedNotificationService from '../services/unifiedNotificationService';
import { UnifiedNotification, AppIdentifier } from '../services/unifiedNotificationService';
import NotificationPreferencesScreen from './NotificationPreferencesScreen';

interface UnifiedNotificationCenterProps {
  appIdentifier?: unifiedNotificationService.AppIdentifier;
  onNotificationPress?: (notification: UnifiedNotification) => void;
  onClose?: () => void;
}

const UnifiedNotificationCenter = ({
  appIdentifier,
  onNotificationPress,
  onClose,
}: UnifiedNotificationCenterProps) => {
  const [notifications, setNotifications] = useState<UnifiedNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [preferencesModalVisible, setPreferencesModalVisible] = useState(false);

  const loadNotifications = useCallback(async () => {
    setLoading(true);
    try {
      const notifs = await unifiedNotificationService.getUnifiedNotifications(appIdentifier);
      setNotifications(notifs);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setLoading(false);
    }
  }, [appIdentifier]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  const handleNotificationPress = async (notification: UnifiedNotification) => {
    // Mark as read
    if (!notification.read) {
      await unifiedNotificationService.markNotificationAsRead(notification.id);
      // Update the local state to reflect the change
      setNotifications(
        notifications.map((n) =>
          n.id === notification.id ? { ...n, read: true } : n
        )
      );
    }

    // Call the onNotificationPress callback if provided
    if (onNotificationPress) {
      onNotificationPress(notification);
    }
  };

  const handleClearAll = async () => {
    await unifiedNotificationService.clearAllNotifications(appIdentifier);
    setNotifications([]);
  };

  const handleDeleteNotification = async (id: string) => {
    await unifiedNotificationService.deleteNotification(id);
    setNotifications(notifications.filter((n) => n.id !== id));
  };

  const getAppName = (appId: AppIdentifier): string => {
    const appNames: Record<AppIdentifier, string> = {
      [AppIdentifier.FIELD_OPERATIONS]: 'Field Operations',
      [AppIdentifier.FARM_MANAGER]: 'Farm Manager',
      [AppIdentifier.INVENTORY_EQUIPMENT]: 'Inventory & Equipment',
      [AppIdentifier.FINANCIAL_MANAGER]: 'Financial Manager',
      [AppIdentifier.EMPLOYEE]: 'Employee',
      [AppIdentifier.MARKETPLACE]: 'Marketplace',
      [AppIdentifier.DRIVER]: 'Driver',
      [AppIdentifier.DRIVE_TRACKER]: 'Drive Tracker',
    };
    return appNames[appId] || 'Unknown App';
  };

  const getNotificationIcon = (type: unifiedNotificationService.NotificationType): string => {
    const iconMap: Record<unifiedNotificationService.NotificationType, string> = {
      [unifiedNotificationService.NotificationType.TASK_ASSIGNMENT]: 'checkmark-circle',
      [unifiedNotificationService.NotificationType.DUE_DATE_ALERT]: 'calendar',
      [unifiedNotificationService.NotificationType.STATUS_CHANGE]: 'sync',
      [unifiedNotificationService.NotificationType.SYSTEM_ANNOUNCEMENT]: 'megaphone-outline',
      [unifiedNotificationService.NotificationType.DELIVERY_UPDATE]: 'car',
      [unifiedNotificationService.NotificationType.INVENTORY_ALERT]: 'cube',
      [unifiedNotificationService.NotificationType.MAINTENANCE_REMINDER]: 'construct',
      [unifiedNotificationService.NotificationType.FINANCIAL_ALERT]: 'cash',
      [unifiedNotificationService.NotificationType.WEATHER_ALERT]: 'thunderstorm-outline',
      [unifiedNotificationService.NotificationType.MARKETPLACE_UPDATE]: 'storefront',
      [unifiedNotificationService.NotificationType.EMPLOYEE_UPDATE]: 'people',
      [unifiedNotificationService.NotificationType.FIELD_ALERT]: 'leaf',
    };
    return iconMap[type] || 'notifications';
  };

  const renderNotificationItem = ({ item }: { item: UnifiedNotification }) => (
    <TouchableOpacity
      style={[styles.notificationItem, !item.read && styles.unreadNotification]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={styles.notificationIcon}>
        <Ionicons
          name={getNotificationIcon(item.type) as any}
          size={24}
          color={item.priority === 'high' ? '#e74c3c' : '#3498db'}
        />
      </View>
      <View style={styles.notificationContent}>
        <View style={styles.notificationHeader}>
          <Text style={styles.notificationTitle}>{item.title}</Text>
          <TouchableOpacity
            onPress={() => handleDeleteNotification(item.id)}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <Ionicons name="close" size={16} color="#999" />
          </TouchableOpacity>
        </View>
        <Text style={styles.notificationBody}>{item.body}</Text>
        <View style={styles.notificationFooter}>
          <Text style={styles.notificationSource}>
            {getAppName(item.sourceApp)}
          </Text>
          <Text style={styles.notificationTime}>
            {format(new Date(item.timestamp), 'MMM d, h:mm a')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="notifications-off-outline" size={48} color="#ccc" />
      <Text style={styles.emptyText}>No notifications</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Notifications</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            onPress={() => setPreferencesModalVisible(true)} 
            style={styles.preferencesButton}
            accessibilityLabel="Notification Preferences"
            accessibilityHint="Opens notification preferences settings"
          >
            <Ionicons name="settings-outline" size={20} color="#3498db" />
          </TouchableOpacity>
          {notifications.length > 0 && (
            <TouchableOpacity onPress={handleClearAll} style={styles.clearButton}>
              <Text style={styles.clearButtonText}>Clear All</Text>
            </TouchableOpacity>
          )}
          {onClose && (
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {loading && !refreshing ? (
        <ActivityIndicator size="large" color="#3498db" style={styles.loader} />
      ) : (
        <FlatList
          data={notifications}
          renderItem={renderNotificationItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyComponent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        />
      )}

      {/* Notification Preferences Modal */}
      <Modal
        animationType="slide"
        transparent={false}
        visible={preferencesModalVisible}
        onRequestClose={() => setPreferencesModalVisible(false)}
      >
        <NotificationPreferencesScreen
          onClose={() => setPreferencesModalVisible(false)}
          onSave={(preferences) => {
            // Reload notifications after preferences are saved
            // to apply any filtering based on new preferences
            setPreferencesModalVisible(false);
            loadNotifications();
          }}
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  preferencesButton: {
    padding: 8,
    marginRight: 8,
  },
  clearButton: {
    marginRight: 16,
  },
  clearButtonText: {
    color: '#3498db',
    fontSize: 14,
  },
  closeButton: {
    padding: 4,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  unreadNotification: {
    backgroundColor: '#f0f8ff',
    borderLeftWidth: 4,
    borderLeftColor: '#3498db',
  },
  notificationIcon: {
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  notificationBody: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  notificationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationSource: {
    fontSize: 12,
    color: '#999',
  },
  notificationTime: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: '#999',
  },
  loader: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default UnifiedNotificationCenter;
