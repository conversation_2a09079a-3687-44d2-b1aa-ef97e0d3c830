#!/bin/bash

# Script to setup the shared module for all mobile apps

APPS_DIR="./apps"
SHARED_DIR="./shared"

# Metro config template
METRO_CONFIG='const { getDefaultConfig } = require("expo/metro-config");
const path = require("path");

const config = getDefaultConfig(__dirname);

// Add the shared module to the watchFolders
const sharedPath = path.resolve(__dirname, "../../shared");
config.watchFolders = [sharedPath];

// Configure the resolver to handle the shared module
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, "node_modules"),
  path.resolve(__dirname, "../../shared/node_modules"),
];

// Add platforms if needed
config.resolver.platforms = ["native", "android", "ios", "web"];

// Configure transformer to handle shared module
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

module.exports = config;'

echo "Setting up shared module for all mobile apps..."

# Loop through all app directories
for app_dir in $APPS_DIR/*/; do
    if [ -d "$app_dir" ]; then
        app_name=$(basename "$app_dir")
        echo "Processing app: $app_name"
        
        # Create metro.config.js if it doesn't exist
        if [ ! -f "$app_dir/metro.config.js" ]; then
            echo "$METRO_CONFIG" > "$app_dir/metro.config.js"
            echo "  ✓ Created metro.config.js"
        else
            echo "  - metro.config.js already exists"
        fi
        
        # Install shared module
        cd "$app_dir"
        if npm list nxtacre-shared >/dev/null 2>&1; then
            echo "  - nxtacre-shared already installed"
        else
            echo "  + Installing nxtacre-shared..."
            npm install ../../shared
        fi
        cd - >/dev/null
        
        echo "  ✓ Completed $app_name"
    fi
done

echo "✅ Shared module setup complete for all apps!"
