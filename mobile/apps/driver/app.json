{"expo": {"name": "NxtAcre Driver", "slug": "nxtacre-driver", "version": "1.0.0", "orientation": "portrait", "icon": "../../shared/assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "../../shared/assets/splash.png", "resizeMode": "contain", "backgroundColor": "#0284c7"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nxtacre.driver", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses your location to track deliveries and provide navigation to pickup and delivery locations.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location to track deliveries and provide navigation to pickup and delivery locations.", "NSCameraUsageDescription": "This app uses your camera to scan barcodes and capture delivery confirmation photos.", "UIBackgroundModes": ["location", "fetch"]}}, "android": {"adaptiveIcon": {"foregroundImage": "../../shared/assets/adaptive-icon.png", "backgroundColor": "#0284c7"}, "package": "com.nxtacre.driver", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA"]}, "web": {"favicon": "../../shared/assets/favicon.png"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow NxtAcre Driver to use your location to track deliveries and provide navigation to pickup and delivery locations."}], ["expo-camera", {"cameraPermission": "Allow NxtAcre Driver to access your camera to scan barcodes and capture delivery confirmation photos."}], "expo-asset", "expo-font", "expo-web-browser"], "extra": {"eas": {"projectId": "nxtacre-driver"}}, "description": "Driver app for the NxtAcre Farm Management Platform"}}