{"name": "nxtacre-driver", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/config-plugins": "~10.0.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.0.5", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.1.8", "expo": "~53.0.12", "expo-asset": "~11.1.5", "expo-background-fetch": "^13.1.5", "expo-background-task": "~0.2.7", "expo-barcode-scanner": "~13.0.1", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-notifications": "~0.31.3", "expo-speech": "^13.1.7", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.9", "expo-task-manager": "~13.1.5", "expo-web-browser": "~14.2.0", "nxtacre-shared": "file:../../shared", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-maps-directions": "^1.9.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-signature-canvas": "^4.5.1", "react-native-web": "~0.20.0", "zustand": "^5.0.5", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/react": "^19.0.14", "jest": "^29.7.0", "jest-expo": "~53.0.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true, "description": "Driver App for NxtAcre Farm Management Platform", "author": "NxtAcre Team", "license": "UNLICENSED"}