import React, { useEffect, useRef } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import AppNavigator from './src/navigation/AppNavigator';
import { View, Text } from 'react-native';
import * as Notifications from 'expo-notifications';
import {
  AuthProvider,
  useSynchronization,
  useNotificationStore,
  addNotificationReceivedListener,
  addNotificationResponseReceivedListener
} from 'nxtacre-shared';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

const AppContent: React.FC = () => {
  // Initialize synchronization service
  const { isInitialized, isOnline } = useSynchronization();

  // Notification response listener ref
  const notificationResponseListener = useRef<Notifications.Subscription>();
  const notificationListener = useRef<Notifications.Subscription>();

  // Log initialization status
  useEffect(() => {
    if (isInitialized) {
      console.log('Synchronization service initialized');
    }
  }, [isInitialized]);

  // Set up notification listeners
  useEffect(() => {
    // Listen for notifications received while app is foregrounded
    notificationListener.current = addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
    });

    // Listen for user interactions with notifications
    notificationResponseListener.current = addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
    });

    return () => {
      if (notificationListener.current) {
        notificationListener.current.remove();
      }
      if (notificationResponseListener.current) {
        notificationResponseListener.current.remove();
      }
    };
  }, []);

  // Initialize notification store
  const notificationStore = useNotificationStore();

  // Initialize push notifications when the app starts
  useEffect(() => {
    notificationStore.initializePushNotifications();
  }, []);

  // Optional: Show offline indicator
  const OfflineIndicator = () => {
    if (isOnline) return null;

    return (
      <View style={{
        backgroundColor: '#f8d7da',
        padding: 5,
        alignItems: 'center',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 999
      }}>
        <Text style={{ color: '#721c24' }}>You are offline. Changes will sync when connection is restored.</Text>
      </View>
    );
  };

  return (
    <NavigationContainer>
      <AppNavigator />
      <OfflineIndicator />
      <StatusBar style="auto" />
    </NavigationContainer>
  );
}

export default function App() {
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </SafeAreaProvider>
  );
}
