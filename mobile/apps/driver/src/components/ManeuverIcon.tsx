import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';

type ManeuverType = 
  | 'turn-right' 
  | 'turn-left' 
  | 'straight' 
  | 'merge' 
  | 'exit' 
  | 'roundabout' 
  | 'arrive';

interface ManeuverIconProps {
  type: ManeuverType;
  size?: number;
  color?: string;
}

/**
 * Component that displays an icon representing a navigation maneuver
 */
const ManeuverIcon: React.FC<ManeuverIconProps> = ({ 
  type, 
  size = 24, 
  color = '#8b5cf6' 
}) => {
  const getIcon = () => {
    switch (type) {
      case 'turn-right':
        return <MaterialIcons name="turn-sharp-right" size={size} color={color} />;
      case 'turn-left':
        return <MaterialIcons name="turn-sharp-left" size={size} color={color} />;
      case 'straight':
        return <MaterialIcons name="arrow-upward" size={size} color={color} />;
      case 'merge':
        return <MaterialIcons name="merge-type" size={size} color={color} />;
      case 'exit':
        return <MaterialIcons name="exit-to-app" size={size} color={color} />;
      case 'roundabout':
        return <MaterialIcons name="roundabout-left" size={size} color={color} />;
      case 'arrive':
        return <Ionicons name="location" size={size} color={color} />;
      default:
        return <Ionicons name="help-circle-outline" size={size} color={color} />;
    }
  };

  return (
    <View style={styles.container}>
      {getIcon()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ManeuverIcon;