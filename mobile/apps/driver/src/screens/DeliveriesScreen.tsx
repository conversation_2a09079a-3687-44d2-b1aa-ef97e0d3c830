import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockDeliveries = [
  {
    id: 'del-001',
    customerName: 'Green Valley Farm',
    address: '1234 Farm Road, Greenville, CA',
    time: '10:30 AM',
    status: 'In Progress',
    type: 'Delivery',
    items: '3 pallets of fertilizer',
    notes: 'Call customer upon arrival'
  },
  {
    id: 'del-002',
    customerName: 'Sunrise Orchards',
    address: '567 Orchard Lane, Sunnydale, CA',
    time: '1:15 PM',
    status: 'Scheduled',
    type: 'Delivery',
    items: '2 pallets of pesticide, 1 pallet of tools',
    notes: 'Gate code: 1234'
  },
  {
    id: 'del-003',
    customerName: 'Blue Sky Dairy',
    address: '890 Dairy Road, Milkville, CA',
    time: '3:45 PM',
    status: 'Scheduled',
    type: 'Pickup',
    items: '5 milk containers',
    notes: 'Requires refrigerated transport'
  },
  {
    id: 'del-004',
    customerName: 'Golden Grain Co-op',
    address: '432 Wheat Field Way, Grainville, CA',
    time: '9:00 AM',
    status: 'Completed',
    type: 'Delivery',
    items: '10 bags of seed',
    notes: 'Delivered to warehouse B'
  },
  {
    id: 'del-005',
    customerName: 'Fresh Harvest Farms',
    address: '765 Vegetable Lane, Freshtown, CA',
    time: '11:30 AM',
    status: 'Completed',
    type: 'Pickup',
    items: '8 crates of produce',
    notes: 'Picked up from packing shed'
  }
];

const DeliveriesScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [deliveries, setDeliveries] = useState(mockDeliveries);
  const [filteredDeliveries, setFilteredDeliveries] = useState(mockDeliveries);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');

  // Filter deliveries based on search query and active filter
  useEffect(() => {
    setLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      let filtered = deliveries;

      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter(delivery => 
          delivery.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          delivery.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
          delivery.items.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply status filter
      if (activeFilter !== 'all') {
        filtered = filtered.filter(delivery => {
          if (activeFilter === 'active') {
            return delivery.status === 'In Progress' || delivery.status === 'Scheduled';
          } else if (activeFilter === 'completed') {
            return delivery.status === 'Completed';
          }
          return true;
        });
      }

      setFilteredDeliveries(filtered);
      setLoading(false);
    }, 500);
  }, [searchQuery, activeFilter, deliveries]);

  // Render each delivery item
  const renderDeliveryItem = ({ item }: { item: typeof mockDeliveries[0] }) => {
    // Determine status color
    let statusColor = '#8b5cf6'; // Default purple
    if (item.status === 'Completed') {
      statusColor = '#10b981'; // Green
    } else if (item.status === 'In Progress') {
      statusColor = '#3b82f6'; // Blue
    }

    // Determine icon based on delivery type
    const iconName: keyof typeof Ionicons.glyphMap = item.type === 'Pickup' ? 'arrow-up-circle' : 'arrow-down-circle';

    return (
      <TouchableOpacity 
        style={styles.deliveryCard}
        onPress={() => navigation.navigate('DeliveryDetail', { deliveryId: item.id })}
      >
        <View style={[styles.deliveryTypeIcon, { backgroundColor: `${statusColor}20` }]}>
          <Ionicons name={iconName} size={24} color={statusColor} />
        </View>
        <View style={styles.deliveryInfo}>
          <Text style={styles.deliveryName}>{item.customerName}</Text>
          <Text style={styles.deliveryAddress} numberOfLines={1}>{item.address}</Text>
          <View style={styles.deliveryMeta}>
            <Text style={styles.deliveryTime}>{item.time}</Text>
            <View style={[styles.statusBadge, { backgroundColor: `${statusColor}20` }]}>
              <Text style={[styles.statusText, { color: statusColor }]}>{item.status}</Text>
            </View>
            <Text style={styles.deliveryType}>{item.type}</Text>
          </View>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search deliveries..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'all' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'active' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('active')}
        >
          <Text style={[styles.filterText, activeFilter === 'active' && styles.activeFilterText]}>Active</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterTab, activeFilter === 'completed' && styles.activeFilterTab]}
          onPress={() => setActiveFilter('completed')}
        >
          <Text style={[styles.filterText, activeFilter === 'completed' && styles.activeFilterText]}>Completed</Text>
        </TouchableOpacity>
      </View>

      {/* Deliveries List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#8b5cf6" />
        </View>
      ) : filteredDeliveries.length > 0 ? (
        <FlatList
          data={filteredDeliveries}
          renderItem={renderDeliveryItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="cube-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No deliveries found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  filterContainer: {
    flexDirection: 'row',
    marginHorizontal: 15,
    marginBottom: 10,
  },
  filterTab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#e5e7eb',
  },
  activeFilterTab: {
    backgroundColor: '#8b5cf6',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterText: {
    color: 'white',
    fontWeight: '500',
  },
  listContainer: {
    padding: 15,
    paddingTop: 5,
  },
  deliveryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  deliveryTypeIcon: {
    padding: 10,
    borderRadius: 8,
    marginRight: 15,
  },
  deliveryInfo: {
    flex: 1,
  },
  deliveryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  deliveryAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  deliveryMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  deliveryTime: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  statusBadge: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  deliveryType: {
    fontSize: 12,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
  },
});

export default DeliveriesScreen;
