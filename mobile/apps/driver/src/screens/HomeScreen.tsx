import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for initial development
const mockData = {
  deliveriesToday: 5,
  deliveriesCompleted: 2,
  nextDelivery: {
    id: 'del-001',
    customerName: 'Green Valley Farm',
    address: '1234 Farm Road, Greenville, CA',
    time: '10:30 AM',
    status: 'In Progress'
  },
  vehicle: {
    id: 'veh-001',
    name: 'Truck #103',
    type: 'Delivery Truck',
    status: 'Active'
  },
  recentMessages: [
    {
      id: 'msg-001',
      sender: 'Dispatch',
      preview: 'Please confirm your arrival at Green Valley Farm',
      time: '9:15 AM',
      unread: true
    }
  ]
};

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [refreshing, setRefreshing] = useState(false);
  const [data, setData] = useState(mockData);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate data fetching
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeText}>Welcome, Driver</Text>
        <Text style={styles.dateText}>{new Date().toDateString()}</Text>
      </View>

      {/* Stats Section */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{data.deliveriesToday}</Text>
          <Text style={styles.statLabel}>Today's Deliveries</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{data.deliveriesCompleted}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{data.deliveriesToday - data.deliveriesCompleted}</Text>
          <Text style={styles.statLabel}>Remaining</Text>
        </View>
      </View>

      {/* Next Delivery Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Next Delivery</Text>
          <TouchableOpacity onPress={() => navigation.navigate('MainTabs', { screen: 'Deliveries' })}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity 
          style={styles.deliveryCard}
          onPress={() => navigation.navigate('DeliveryDetail', { deliveryId: data.nextDelivery.id })}
        >
          <View style={styles.deliveryInfo}>
            <Text style={styles.deliveryName}>{data.nextDelivery.customerName}</Text>
            <Text style={styles.deliveryAddress}>{data.nextDelivery.address}</Text>
            <Text style={styles.deliveryTime}>{data.nextDelivery.time}</Text>
          </View>
          <View style={styles.deliveryStatus}>
            <Text style={[styles.statusText, { color: '#8b5cf6' }]}>{data.nextDelivery.status}</Text>
            <Ionicons name="chevron-forward" size={24} color="#8b5cf6" />
          </View>
        </TouchableOpacity>
      </View>

      {/* Current Vehicle Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Current Vehicle</Text>
          <TouchableOpacity onPress={() => navigation.navigate('VehicleSelection')}>
            <Text style={styles.seeAllText}>Change</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.vehicleCard}>
          <View style={styles.vehicleIcon}>
            <Ionicons name="car" size={32} color="#8b5cf6" />
          </View>
          <View style={styles.vehicleInfo}>
            <Text style={styles.vehicleName}>{data.vehicle.name}</Text>
            <Text style={styles.vehicleType}>{data.vehicle.type}</Text>
            <View style={styles.statusBadge}>
              <Text style={styles.statusBadgeText}>{data.vehicle.status}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Recent Messages Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Messages</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Messages')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>

        {data.recentMessages.length > 0 ? (
          data.recentMessages.map(message => (
            <TouchableOpacity 
              key={message.id}
              style={styles.messageCard}
              onPress={() => navigation.navigate('Conversation', { 
                conversationId: message.id, 
                recipientName: message.sender 
              })}
            >
              <View style={styles.messageIcon}>
                <Ionicons name="chatbubble-ellipses" size={24} color="#8b5cf6" />
                {message.unread && <View style={styles.unreadBadge} />}
              </View>
              <View style={styles.messageInfo}>
                <Text style={styles.messageSender}>{message.sender}</Text>
                <Text style={styles.messagePreview} numberOfLines={1}>{message.preview}</Text>
                <Text style={styles.messageTime}>{message.time}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#8b5cf6" />
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No recent messages</Text>
          </View>
        )}
      </View>

      {/* Quick Actions Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('MainTabs', { screen: 'Navigation' })}
          >
            <Ionicons name="navigate" size={24} color="#8b5cf6" />
            <Text style={styles.quickActionText}>Navigate</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('MainTabs', { screen: 'Schedule' })}
          >
            <Ionicons name="calendar" size={24} color="#8b5cf6" />
            <Text style={styles.quickActionText}>Schedule</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Messages')}
          >
            <Ionicons name="chatbubble" size={24} color="#8b5cf6" />
            <Text style={styles.quickActionText}>Message</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.quickActionButton}
            onPress={() => navigation.navigate('Help')}
          >
            <Ionicons name="help-circle" size={24} color="#8b5cf6" />
            <Text style={styles.quickActionText}>Help</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  welcomeSection: {
    backgroundColor: '#8b5cf6',
    padding: 20,
    paddingTop: 10,
    paddingBottom: 30,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  dateText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 5,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    marginTop: -20,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    width: '30%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#8b5cf6',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    margin: 15,
    marginTop: 5,
    marginBottom: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    color: '#8b5cf6',
  },
  deliveryCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 15,
  },
  deliveryInfo: {
    flex: 1,
  },
  deliveryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  deliveryAddress: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  deliveryTime: {
    fontSize: 14,
    color: '#8b5cf6',
    marginTop: 5,
    fontWeight: '500',
  },
  deliveryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    marginRight: 5,
  },
  vehicleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 15,
  },
  vehicleIcon: {
    backgroundColor: 'rgba(139, 92, 246, 0.1)',
    borderRadius: 8,
    padding: 10,
    marginRight: 15,
  },
  vehicleInfo: {
    flex: 1,
  },
  vehicleName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  vehicleType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    backgroundColor: '#e6f7ff',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    alignSelf: 'flex-start',
    marginTop: 5,
  },
  statusBadgeText: {
    fontSize: 12,
    color: '#1890ff',
  },
  messageCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
  },
  messageIcon: {
    position: 'relative',
    marginRight: 15,
  },
  unreadBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'red',
  },
  messageInfo: {
    flex: 1,
  },
  messageSender: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  messagePreview: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  messageTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 5,
  },
  emptyState: {
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#999',
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  quickActionButton: {
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 15,
    width: '22%',
  },
  quickActionText: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
});

export default HomeScreen;
