import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Image,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

// Mock data for seller dashboard
const MOCK_SELLER_DATA = {
  id: '1',
  name: 'Green Valley Farm',
  rating: 4.8,
  reviewCount: 156,
  totalSales: 12580.45,
  pendingOrders: 8,
  activeListings: 24,
  salesData: [
    { date: '2023-06-01', amount: 420.50 },
    { date: '2023-06-02', amount: 350.75 },
    { date: '2023-06-03', amount: 510.25 },
    { date: '2023-06-04', amount: 480.00 },
    { date: '2023-06-05', amount: 620.50 },
    { date: '2023-06-06', amount: 580.75 },
    { date: '2023-06-07', amount: 490.25 },
  ],
  topProducts: [
    { id: '1', name: 'Organic Tomatoes', price: 3.99, sold: 128, image: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80' },
    { id: '2', name: 'Fresh Lettuce', price: 2.49, sold: 96, image: 'https://images.unsplash.com/photo-1556801712-76c8eb07bbc9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80' },
    { id: '3', name: 'Organic Carrots', price: 2.99, sold: 87, image: 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80' },
  ],
  recentOrders: [
    { id: 'ORD-001', customer: 'John Smith', date: '2023-06-07', amount: 45.97, status: 'Pending' },
    { id: 'ORD-002', customer: 'Sarah Johnson', date: '2023-06-06', amount: 32.48, status: 'Shipped' },
    { id: 'ORD-003', customer: 'Michael Brown', date: '2023-06-05', amount: 78.25, status: 'Delivered' },
    { id: 'ORD-004', customer: 'Emily Davis', date: '2023-06-04', amount: 24.99, status: 'Delivered' },
  ]
};

const SellerDashboardScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const [loading, setLoading] = useState(true);
  const [sellerData, setSellerData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('week'); // 'day', 'week', 'month', 'year'
  
  // Fetch seller data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setSellerData(MOCK_SELLER_DATA);
      setLoading(false);
    }, 1000);
  }, []);
  
  // Handle navigation to product detail
  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetail', { productId });
  };
  
  // Handle navigation to order detail
  const handleOrderPress = (orderId: string) => {
    navigation.navigate('OrderDetail', { orderId });
  };
  
  // Handle navigation to seller products
  const handleViewAllProducts = () => {
    navigation.navigate('SellerProducts');
  };
  
  // Handle navigation to seller orders
  const handleViewAllOrders = () => {
    navigation.navigate('SellerOrders');
  };
  
  // Handle navigation to reviews
  const handleViewReviews = () => {
    navigation.navigate('Reviews', { sellerId: sellerData?.id });
  };
  
  // Render sales summary
  const renderSalesSummary = () => {
    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Sales Dashboard</Text>
          <View style={styles.periodSelector}>
            <TouchableOpacity 
              style={[styles.periodButton, selectedPeriod === 'day' && styles.activePeriodButton]} 
              onPress={() => setSelectedPeriod('day')}
            >
              <Text style={[styles.periodButtonText, selectedPeriod === 'day' && styles.activePeriodButtonText]}>Day</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.periodButton, selectedPeriod === 'week' && styles.activePeriodButton]} 
              onPress={() => setSelectedPeriod('week')}
            >
              <Text style={[styles.periodButtonText, selectedPeriod === 'week' && styles.activePeriodButtonText]}>Week</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.periodButton, selectedPeriod === 'month' && styles.activePeriodButton]} 
              onPress={() => setSelectedPeriod('month')}
            >
              <Text style={[styles.periodButtonText, selectedPeriod === 'month' && styles.activePeriodButtonText]}>Month</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.periodButton, selectedPeriod === 'year' && styles.activePeriodButton]} 
              onPress={() => setSelectedPeriod('year')}
            >
              <Text style={[styles.periodButtonText, selectedPeriod === 'year' && styles.activePeriodButtonText]}>Year</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.metricsContainer}>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>${sellerData?.totalSales.toFixed(2)}</Text>
            <Text style={styles.metricLabel}>Total Sales</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{sellerData?.pendingOrders}</Text>
            <Text style={styles.metricLabel}>Pending Orders</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>{sellerData?.activeListings}</Text>
            <Text style={styles.metricLabel}>Active Listings</Text>
          </View>
        </View>
        
        <View style={styles.chartContainer}>
          {/* In a real app, this would be a chart component */}
          <View style={styles.chartPlaceholder}>
            <Text style={styles.chartPlaceholderText}>Sales Chart</Text>
            <View style={styles.barChart}>
              {sellerData?.salesData.map((day, index) => {
                // Calculate bar height based on maximum value
                const maxAmount = Math.max(...sellerData.salesData.map(d => d.amount));
                const barHeight = (day.amount / maxAmount) * 100;
                
                return (
                  <View key={index} style={styles.barContainer}>
                    <View style={[styles.bar, { height: `${barHeight}%` }]} />
                    <Text style={styles.barLabel}>{new Date(day.date).getDate()}</Text>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  // Render top products
  const renderTopProducts = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Top Products</Text>
          <TouchableOpacity onPress={handleViewAllProducts}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {sellerData?.topProducts.map((product, index) => (
          <TouchableOpacity 
            key={product.id} 
            style={styles.productCard}
            onPress={() => handleProductPress(product.id)}
          >
            <Image source={{ uri: product.image }} style={styles.productImage} />
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productPrice}>${product.price.toFixed(2)}</Text>
              <Text style={styles.productSold}>{product.sold} sold</Text>
            </View>
            <View style={styles.productRank}>
              <Text style={styles.productRankText}>#{index + 1}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Render recent orders
  const renderRecentOrders = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Orders</Text>
          <TouchableOpacity onPress={handleViewAllOrders}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {sellerData?.recentOrders.map((order) => (
          <TouchableOpacity 
            key={order.id} 
            style={styles.orderCard}
            onPress={() => handleOrderPress(order.id)}
          >
            <View style={styles.orderInfo}>
              <Text style={styles.orderId}>{order.id}</Text>
              <Text style={styles.orderCustomer}>{order.customer}</Text>
              <Text style={styles.orderDate}>{new Date(order.date).toLocaleDateString()}</Text>
            </View>
            <View style={styles.orderDetails}>
              <Text style={styles.orderAmount}>${order.amount.toFixed(2)}</Text>
              <View style={[
                styles.orderStatusBadge,
                order.status === 'Pending' && styles.pendingBadge,
                order.status === 'Shipped' && styles.shippedBadge,
                order.status === 'Delivered' && styles.deliveredBadge,
              ]}>
                <Text style={styles.orderStatusText}>{order.status}</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Render seller profile
  const renderSellerProfile = () => {
    return (
      <View style={styles.profileContainer}>
        <View style={styles.profileHeader}>
          <Text style={styles.profileName}>{sellerData?.name}</Text>
          <TouchableOpacity style={styles.ratingContainer} onPress={handleViewReviews}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={styles.ratingText}>{sellerData?.rating} ({sellerData?.reviewCount} reviews)</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => navigation.navigate('AddProduct')}
          >
            <Ionicons name="add-circle-outline" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Add Product</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => navigation.navigate('Messages')}
          >
            <Ionicons name="chatbubble-outline" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Messages</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      {renderSellerProfile()}
      {renderSalesSummary()}
      {renderTopProducts()}
      {renderRecentOrders()}
      
      {/* Advanced Analytics Button */}
      <TouchableOpacity style={styles.advancedAnalyticsButton}>
        <Ionicons name="analytics-outline" size={20} color="#3b82f6" />
        <Text style={styles.advancedAnalyticsText}>View Advanced Analytics</Text>
      </TouchableOpacity>
      
      {/* Export Reports Button */}
      <TouchableOpacity style={styles.exportButton}>
        <Ionicons name="download-outline" size={20} color="#3b82f6" />
        <Text style={styles.exportButtonText}>Export Reports</Text>
      </TouchableOpacity>
      
      {/* Bottom padding */}
      <View style={{ height: 20 }} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#757575',
  },
  profileContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  profileHeader: {
    marginBottom: 15,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 5,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  secondaryButton: {
    backgroundColor: '#4CAF50',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 5,
  },
  summaryContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#F0F0F0',
    borderRadius: 20,
    padding: 2,
  },
  periodButton: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 18,
  },
  activePeriodButton: {
    backgroundColor: '#3b82f6',
  },
  periodButtonText: {
    fontSize: 12,
    color: '#757575',
  },
  activePeriodButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  metricCard: {
    flex: 1,
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    marginHorizontal: 5,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
    marginBottom: 5,
  },
  metricLabel: {
    fontSize: 12,
    color: '#757575',
  },
  chartContainer: {
    height: 200,
    marginTop: 10,
  },
  chartPlaceholder: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 10,
  },
  chartPlaceholderText: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 10,
  },
  barChart: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingBottom: 20,
  },
  barContainer: {
    flex: 1,
    alignItems: 'center',
  },
  bar: {
    width: 20,
    backgroundColor: '#3b82f6',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  barLabel: {
    fontSize: 12,
    color: '#757575',
    marginTop: 5,
  },
  sectionContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212121',
  },
  viewAllText: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '500',
  },
  productCard: {
    flexDirection: 'row',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    paddingBottom: 15,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  productInfo: {
    flex: 1,
    marginLeft: 10,
    justifyContent: 'center',
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212121',
    marginBottom: 2,
  },
  productPrice: {
    fontSize: 14,
    color: '#3b82f6',
    marginBottom: 2,
  },
  productSold: {
    fontSize: 12,
    color: '#757575',
  },
  productRank: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  productRankText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#212121',
  },
  orderCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    paddingBottom: 15,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 14,
    fontWeight: '500',
    color: '#212121',
    marginBottom: 2,
  },
  orderCustomer: {
    fontSize: 14,
    color: '#424242',
    marginBottom: 2,
  },
  orderDate: {
    fontSize: 12,
    color: '#757575',
  },
  orderDetails: {
    alignItems: 'flex-end',
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3b82f6',
    marginBottom: 5,
  },
  orderStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
  },
  pendingBadge: {
    backgroundColor: '#FFF3E0',
  },
  shippedBadge: {
    backgroundColor: '#E3F2FD',
  },
  deliveredBadge: {
    backgroundColor: '#E8F5E9',
  },
  orderStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#757575',
  },
  advancedAnalyticsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  advancedAnalyticsText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
    marginLeft: 5,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
  },
  exportButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
    marginLeft: 5,
  },
});

export default SellerDashboardScreen;