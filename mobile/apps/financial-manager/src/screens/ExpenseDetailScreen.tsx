import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput, 
  ActivityIndicator,
  Alert,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { 
  ExpenseDetailScreenNavigationProp, 
  ExpenseDetailScreenRouteProp,
  Expense 
} from '../types/navigation';

// Mock data for initial development
const mockExpense: Expense = {
  id: '1',
  amount: 1250,
  description: 'Equipment repair',
  category: 'Maintenance',
  date: '2023-08-01',
  status: 'pending',
  receiptUrl: 'https://example.com/receipt.jpg',
  createdBy: '<PERSON>',
  createdAt: '2023-08-01T10:30:00Z',
  updatedAt: '2023-08-01T14:45:00Z'
};

const categories = [
  'Maintenance',
  'Operations',
  'Supplies',
  'Administrative',
  'Utilities',
  'Fuel',
  'Labor',
  'Insurance',
  'Taxes',
  'Other'
];

const ExpenseDetailScreen = () => {
  const navigation = useNavigation<ExpenseDetailScreenNavigationProp>();
  const route = useRoute<ExpenseDetailScreenRouteProp>();
  const { expenseId } = route.params;
  const isNewExpense = expenseId === 'new';

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [expense, setExpense] = useState<Expense | null>(null);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [date, setDate] = useState('');
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  useEffect(() => {
    if (isNewExpense) {
      // Initialize with empty values for new expense
      setExpense({
        id: 'new',
        amount: 0,
        description: '',
        category: '',
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        createdBy: 'Current User',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      setAmount('0.00');
      setDescription('');
      setCategory('');
      setDate(new Date().toISOString().split('T')[0]);
      setLoading(false);
    } else {
      // Simulate API call to get expense details
      setTimeout(() => {
        setExpense(mockExpense);
        setAmount(mockExpense.amount.toString());
        setDescription(mockExpense.description);
        setCategory(mockExpense.category);
        setDate(mockExpense.date);
        setLoading(false);
      }, 1000);
    }
  }, [expenseId, isNewExpense]);

  const handleSave = () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (!description) {
      Alert.alert('Error', 'Please enter a description');
      return;
    }

    if (!category) {
      Alert.alert('Error', 'Please select a category');
      return;
    }

    if (!date) {
      Alert.alert('Error', 'Please enter a date');
      return;
    }

    setSaving(true);

    // Simulate API call to save expense
    setTimeout(() => {
      setSaving(false);
      
      if (isNewExpense) {
        Alert.alert(
          'Success',
          'Expense created successfully',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } else {
        Alert.alert(
          'Success',
          'Expense updated successfully',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    }, 1500);
  };

  const handleDelete = () => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this expense?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Simulate API call to delete expense
            setTimeout(() => {
              Alert.alert(
                'Success',
                'Expense deleted successfully',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
              );
            }, 1000);
          }
        }
      ]
    );
  };

  const handleCaptureReceipt = () => {
    navigation.navigate('ReceiptCapture', { expenseId });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return '$0.00';
    return `$${num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'rejected':
        return '#F44336';
      default:
        return '#999';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading expense details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isNewExpense ? 'New Expense' : 'Expense Details'}
        </Text>
        {!isNewExpense && (
          <TouchableOpacity 
            style={styles.deleteButton}
            onPress={handleDelete}
          >
            <Ionicons name="trash-outline" size={24} color="white" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content}>
        {!isNewExpense && expense?.status && (
          <View style={styles.statusContainer}>
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(expense.status) }]} />
            <Text style={styles.statusText}>
              Status: {expense.status.charAt(0).toUpperCase() + expense.status.slice(1)}
            </Text>
          </View>
        )}

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Expense Details</Text>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Amount</Text>
            <View style={styles.amountInputContainer}>
              <Text style={styles.currencySymbol}>$</Text>
              <TextInput
                style={styles.amountInput}
                value={amount}
                onChangeText={setAmount}
                keyboardType="decimal-pad"
                placeholder="0.00"
              />
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={styles.input}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter expense description"
              multiline
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Category</Text>
            <TouchableOpacity 
              style={styles.input}
              onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            >
              <Text style={category ? styles.inputText : styles.placeholderText}>
                {category || 'Select a category'}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
            {showCategoryPicker && (
              <View style={styles.categoryPicker}>
                {categories.map((cat) => (
                  <TouchableOpacity 
                    key={cat}
                    style={[
                      styles.categoryOption,
                      category === cat && styles.categoryOptionSelected
                    ]}
                    onPress={() => {
                      setCategory(cat);
                      setShowCategoryPicker(false);
                    }}
                  >
                    <Text 
                      style={[
                        styles.categoryOptionText,
                        category === cat && styles.categoryOptionTextSelected
                      ]}
                    >
                      {cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Date</Text>
            <TextInput
              style={styles.input}
              value={date}
              onChangeText={setDate}
              placeholder="YYYY-MM-DD"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Receipt</Text>
          
          {expense?.receiptUrl ? (
            <View style={styles.receiptContainer}>
              <Image 
                source={{ uri: 'https://via.placeholder.com/300x400' }} // Placeholder image
                style={styles.receiptImage}
                resizeMode="cover"
              />
              <View style={styles.receiptActions}>
                <TouchableOpacity style={styles.receiptAction}>
                  <Ionicons name="eye-outline" size={20} color="#4CAF50" />
                  <Text style={styles.receiptActionText}>View</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.receiptAction}>
                  <Ionicons name="download-outline" size={20} color="#4CAF50" />
                  <Text style={styles.receiptActionText}>Download</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.receiptAction}>
                  <Ionicons name="trash-outline" size={20} color="#F44336" />
                  <Text style={[styles.receiptActionText, { color: '#F44336' }]}>Remove</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity 
              style={styles.captureReceiptButton}
              onPress={handleCaptureReceipt}
            >
              <Ionicons name="camera-outline" size={24} color="#4CAF50" />
              <Text style={styles.captureReceiptText}>Capture Receipt</Text>
            </TouchableOpacity>
          )}
        </View>

        {!isNewExpense && (
          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Additional Information</Text>
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Created By</Text>
              <Text style={styles.infoValue}>{expense?.createdBy}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Created At</Text>
              <Text style={styles.infoValue}>{formatDate(expense?.createdAt || '')}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Last Updated</Text>
              <Text style={styles.infoValue}>{formatDate(expense?.updatedAt || '')}</Text>
            </View>
          </View>
        )}

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.saveButtonText}>
                {isNewExpense ? 'Create Expense' : 'Save Changes'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: '#4CAF50',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
  },
  deleteButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
  },
  formSection: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  formGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
  },
  input: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
  },
  inputText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    paddingHorizontal: 15,
  },
  currencySymbol: {
    fontSize: 18,
    color: '#333',
    marginRight: 5,
  },
  amountInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 18,
  },
  categoryPicker: {
    marginTop: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    maxHeight: 200,
  },
  categoryOption: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryOptionSelected: {
    backgroundColor: '#e8f5e9',
  },
  categoryOptionText: {
    fontSize: 16,
    color: '#333',
  },
  categoryOptionTextSelected: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  receiptContainer: {
    alignItems: 'center',
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 5,
    marginBottom: 10,
  },
  receiptActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  receiptAction: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  receiptActionText: {
    marginLeft: 5,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  captureReceiptButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#4CAF50',
    borderStyle: 'dashed',
    borderRadius: 5,
    padding: 20,
  },
  captureReceiptText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#4CAF50',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    paddingVertical: 12,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  saveButton: {
    flex: 2,
    backgroundColor: '#4CAF50',
    borderRadius: 5,
    paddingVertical: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: 'bold',
  },
});

export default ExpenseDetailScreen;