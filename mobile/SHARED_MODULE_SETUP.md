# NxtAcre Shared Module Setup

This document explains how the shared module (`nxtacre-shared`) is configured and used across all mobile apps.

## Overview

The `nxtacre-shared` package contains shared components, utilities, hooks, and services that are used across all NxtAcre mobile applications. This setup allows for code reuse and consistency across the entire mobile app ecosystem.

## Directory Structure

```
mobile/
├── shared/                    # Shared module source
│   ├── components/           # Shared React components
│   ├── hooks/               # Custom React hooks
│   ├── services/            # Business logic services
│   ├── store/               # State management (Zustand stores)
│   ├── utils/               # Utility functions
│   ├── index.js             # Main export file
│   ├── index.d.ts           # TypeScript definitions
│   └── package.json         # Package configuration
├── apps/                    # Individual mobile apps
│   ├── driver/
│   ├── employee/
│   ├── farm-manager/
│   └── ...
└── setup-shared-module.sh   # Setup script for all apps
```

## Configuration

### 1. Shared Module (mobile/shared/)

The shared module is configured as a proper npm package:

- **Package name**: `nxtacre-shared`
- **Main entry**: `index.js`
- **TypeScript definitions**: `index.d.ts`

### 2. Metro Configuration

Each mobile app has a `metro.config.js` file that configures Metro bundler to properly resolve the shared module:

```javascript
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add the shared module to the watchFolders
const sharedPath = path.resolve(__dirname, '../../shared');
config.watchFolders = [sharedPath];

// Configure the resolver to handle the shared module
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
  path.resolve(__dirname, '../../shared/node_modules'),
];

config.resolver.platforms = ['native', 'android', 'ios', 'web'];

module.exports = config;
```

### 3. Package Installation

Each app installs the shared module as a local dependency:

```json
{
  "dependencies": {
    "nxtacre-shared": "file:../../shared"
  }
}
```

## Available Exports

The shared module exports the following:

### Components
- `SplashScreen` - Custom splash screen component
- `HelpTip` - Help tooltip component
- `UnifiedNotificationCenter` - Notification management UI
- `NotificationButton` - Notification trigger button
- `AuthProvider` - Authentication context provider

### Hooks
- `useSynchronization` - Data synchronization hook
- `useAuth` - Authentication hook
- `useNotificationStore` - Notification state management

### Services
- Notification services
- Unified notification services

### Utils
- Animation utilities
- Sentry configuration
- Native event emitter fixes

## Usage

Import shared components and utilities in your app:

```javascript
import {
  AuthProvider,
  useSynchronization,
  useNotificationStore,
  addNotificationReceivedListener,
  addNotificationResponseReceivedListener,
  initializeNativeEventEmitterFix
} from 'nxtacre-shared';
```

## Setup for New Apps

To add the shared module to a new mobile app:

1. Run the setup script from the mobile directory:
   ```bash
   cd mobile
   ./setup-shared-module.sh
   ```

2. Or manually:
   ```bash
   cd mobile/apps/your-new-app
   npm install ../../shared
   # Copy metro.config.js from another app
   ```

## Troubleshooting

### Module Resolution Issues

If you encounter "Unable to resolve nxtacre-shared" errors:

1. **Clear Metro cache**:
   ```bash
   npx expo start --clear
   ```

2. **Reinstall the shared module**:
   ```bash
   npm uninstall nxtacre-shared
   npm install ../../shared
   ```

3. **Verify symlink exists**:
   ```bash
   ls -la node_modules/ | grep nxtacre
   # Should show: nxtacre-shared -> ../../../shared
   ```

### TypeScript Issues

If TypeScript can't find types:

1. Ensure `index.d.ts` exists in the shared module
2. Check that the shared module exports are properly typed
3. Restart your TypeScript language server

## Development Workflow

When making changes to the shared module:

1. Make changes in `mobile/shared/`
2. The changes are automatically available to all apps (via symlinks)
3. Restart Metro bundler with `--clear` if needed
4. Test changes across multiple apps

## Best Practices

1. **Keep exports minimal** - Only export what's truly shared
2. **Use TypeScript** - Ensure all exports are properly typed
3. **Test across apps** - Verify changes work in multiple apps
4. **Document changes** - Update this file when adding new exports
5. **Version carefully** - Consider impact on all apps when making breaking changes
