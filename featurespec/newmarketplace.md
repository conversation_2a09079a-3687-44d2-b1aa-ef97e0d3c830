# NxtAcre Marketplace Implementation Guide

## Overview
This document outlines the implementation plan for the new marketplace feature in NxtAcre. The marketplace will allow farms to showcase their products to customers, and customers to browse, search, and purchase products from farms. The global marketplace will be accessible via the dedicated subdomain store.nxtacre.com, while farm-specific marketplaces will be available at the /store path on each farm's subdomain.

## Features
1. **Product Image Management**
   - Multiple image uploads per product
   - Drag-and-drop reordering of images
   - Image preview and deletion

2. **Marketplace System**
   - Global marketplace (shop subdomain)
   - Farm-specific marketplaces (farm subdomains)
   - Product visibility toggle for marketplace

3. **Shopping Cart**
   - Add products to cart
   - Adjust quantities
   - Save cart for later (when logged in)
   - Submit purchase requests

4. **Customer Management**
   - Customer origin tags (marketplace or farm-added)
   - Multiple addresses with farm names/aliases
   - Delivery instructions (access codes, contact info, etc.)
   - Unified login across marketplace and farms

5. **Search and Filtering**
   - Product search
   - Category filtering
   - Farm filtering (on global marketplace)

6. **Fulfillment Options**
   - Farm-level global fulfillment settings (delivery, pickup, or both)
   - Per-product fulfillment options that can override farm defaults
   - Customer selection of delivery or pickup during checkout
   - Delivery fee configuration per farm

## Database Schema Changes

### 1. Product Images Table
```sql
-- Migration: Add product images table
-- Depends on: add_product_public_visibility.sql

SET search_path TO site;

CREATE TABLE product_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  file_path VARCHAR(255) NOT NULL,
  display_order INTEGER NOT NULL DEFAULT 0,
  is_primary BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX product_images_product_id_idx ON product_images(product_id);
```

### 2. Update Products Table
```sql
-- Migration: Add marketplace visibility to products
-- Depends on: add_product_public_visibility.sql

SET search_path TO site;

ALTER TABLE products
ADD COLUMN is_marketplace_visible BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN marketplace_description TEXT,
ADD COLUMN marketplace_category VARCHAR(100);

COMMENT ON COLUMN products.is_marketplace_visible IS 'Whether this product is visible in the public marketplace';
COMMENT ON COLUMN products.marketplace_description IS 'Product description specifically for the marketplace';
COMMENT ON COLUMN products.marketplace_category IS 'Category for marketplace filtering';
```

### 3. Customer Addresses Table
```sql
-- Migration: Add customer addresses table
-- Depends on: add_customer_portal_features.sql

SET search_path TO site;

CREATE TABLE customer_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  farm_alias VARCHAR(255),
  address VARCHAR(255) NOT NULL,
  city VARCHAR(100) NOT NULL,
  state VARCHAR(50) NOT NULL,
  zip_code VARCHAR(20) NOT NULL,
  country VARCHAR(100) DEFAULT 'USA',
  delivery_instructions TEXT,
  access_code VARCHAR(100),
  contact_name VARCHAR(255),
  contact_phone VARCHAR(20),
  is_default BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX customer_addresses_customer_id_idx ON customer_addresses(customer_id);
CREATE INDEX customer_addresses_farm_id_idx ON customer_addresses(farm_id);
```

### 4. Update Customers Table
```sql
-- Migration: Add customer origin and global ID
-- Depends on: add_customer_portal_features.sql

SET search_path TO site;

ALTER TABLE customers
ADD COLUMN origin VARCHAR(50) DEFAULT 'farm',
ADD COLUMN global_customer_id UUID,
ADD COLUMN marketplace_user_id UUID;

COMMENT ON COLUMN customers.origin IS 'Origin of the customer: farm, marketplace';
COMMENT ON COLUMN customers.global_customer_id IS 'ID linking customers across farms for the same user';
COMMENT ON COLUMN customers.marketplace_user_id IS 'ID of the user in the marketplace system';
```

### 5. Shopping Cart Tables
```sql
-- Migration: Add shopping cart tables
-- Depends on: add_product_public_visibility.sql

SET search_path TO site;

CREATE TABLE shopping_carts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID,
  session_id VARCHAR(255),
  farm_id UUID,
  is_saved BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT shopping_carts_user_or_session_check CHECK (
    (user_id IS NOT NULL) OR (session_id IS NOT NULL)
  )
);

CREATE TABLE shopping_cart_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  cart_id UUID NOT NULL REFERENCES shopping_carts(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE purchase_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID,
  customer_id UUID,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE purchase_request_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  request_id UUID NOT NULL REFERENCES purchase_requests(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  price DECIMAL(15, 2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX shopping_carts_user_id_idx ON shopping_carts(user_id);
CREATE INDEX shopping_carts_session_id_idx ON shopping_carts(session_id);
CREATE INDEX shopping_carts_farm_id_idx ON shopping_carts(farm_id);
CREATE INDEX shopping_cart_items_cart_id_idx ON shopping_cart_items(cart_id);
CREATE INDEX shopping_cart_items_product_id_idx ON shopping_cart_items(product_id);
CREATE INDEX purchase_requests_farm_id_idx ON purchase_requests(farm_id);
CREATE INDEX purchase_requests_customer_id_idx ON purchase_requests(customer_id);
CREATE INDEX purchase_request_items_request_id_idx ON purchase_request_items(request_id);
```

### 6. Farm Fulfillment Options Table
```sql
-- Migration: Add farm fulfillment options table
-- Depends on: add_shopping_cart_tables.sql

SET search_path TO site;

CREATE TABLE farm_fulfillment_options (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  offers_delivery BOOLEAN NOT NULL DEFAULT true,
  offers_pickup BOOLEAN NOT NULL DEFAULT true,
  delivery_fee DECIMAL(10, 2) DEFAULT 0.00,
  min_order_for_free_delivery DECIMAL(10, 2),
  delivery_radius_miles INTEGER,
  pickup_instructions TEXT,
  delivery_instructions TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX farm_fulfillment_options_farm_id_idx ON farm_fulfillment_options(farm_id);

-- Add fulfillment options to products table
ALTER TABLE products
ADD COLUMN override_farm_fulfillment BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN offers_delivery BOOLEAN,
ADD COLUMN offers_pickup BOOLEAN;

COMMENT ON COLUMN products.override_farm_fulfillment IS 'Whether this product overrides farm-level fulfillment options';
COMMENT ON COLUMN products.offers_delivery IS 'Whether this product offers delivery (if overriding farm settings)';
COMMENT ON COLUMN products.offers_pickup IS 'Whether this product offers pickup (if overriding farm settings)';

-- Add fulfillment method to purchase requests table
ALTER TABLE purchase_requests
ADD COLUMN fulfillment_method VARCHAR(50) NOT NULL DEFAULT 'delivery',
ADD COLUMN pickup_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN delivery_address_id UUID REFERENCES customer_addresses(id);

COMMENT ON COLUMN purchase_requests.fulfillment_method IS 'Method of fulfillment: delivery, pickup';
COMMENT ON COLUMN purchase_requests.pickup_date IS 'Scheduled date for pickup (if applicable)';
COMMENT ON COLUMN purchase_requests.delivery_address_id IS 'Reference to the delivery address (if applicable)';
```

## Backend API Endpoints

### Product Image Management
1. `POST /api/products/:productId/images` - Upload a new product image
   - Request: Multipart form data with image file
   - Response: Image object with ID, URL, and display order
   - Status: Implemented ✓

2. `GET /api/products/:productId/images` - Get all images for a product
   - Response: Array of image objects with URLs and metadata
   - Status: Implemented ✓

3. `PUT /api/products/:productId/images/reorder` - Reorder product images
   - Request: Array of image IDs in the desired order
   - Response: Updated array of image objects
   - Status: Implemented ✓

4. `DELETE /api/products/:productId/images/:imageId` - Delete a product image
   - Response: Success message
   - Status: Implemented ✓

5. `PUT /api/products/:productId/images/:imageId/primary` - Set an image as primary
   - Response: Updated image object
   - Status: Implemented ✓

### Marketplace Products
1. `GET /api/marketplace/products` - Get all products visible in the marketplace
   - Query params: search, category, farmId, minPrice, maxPrice, page, limit
   - Response: Products array with pagination
   - Status: Implemented ✓

2. `GET /api/marketplace/farms/:farmId/products` - Get all products for a specific farm
   - Query params: search, category, minPrice, maxPrice, page, limit
   - Response: Farm info and products array with pagination
   - Status: Implemented ✓

3. `GET /api/marketplace/products/categories` - Get all product categories
   - Response: Array of category strings
   - Status: Implemented ✓

4. `GET /api/marketplace/products/search` - Search for products
   - Query params: q, filters
   - Response: Products array with pagination
   - Status: Implemented ✓

5. `GET /api/marketplace/products/:productId` - Get a single product
   - Response: Product object with images and farm info
   - Status: Implemented ✓

### Shopping Cart
1. `POST /api/marketplace/cart/items` - Add item to cart
   - Request: Product ID and quantity
   - Response: Updated cart object
   - Status: Implemented ✓

2. `GET /api/marketplace/cart` - Get current cart
   - Response: Cart object with items and product details
   - Status: Implemented ✓

3. `PUT /api/marketplace/cart/items/:itemId` - Update cart item quantity
   - Request: New quantity
   - Response: Updated cart object
   - Status: Implemented ✓

4. `DELETE /api/marketplace/cart/items/:itemId` - Remove item from cart
   - Response: Updated cart object
   - Status: Implemented ✓

5. `POST /api/marketplace/cart/save` - Save cart for later
   - Response: Updated cart object with saved status
   - Status: Implemented ✓

6. `POST /api/marketplace/cart/checkout` - Submit purchase request
   - Request: Customer info, shipping address, payment method
   - Response: Purchase request object
   - Status: Implemented ✓

### Customer Management
1. `POST /api/marketplace/customers/register` - Register a new customer
   - Request: Name, email, password, and optional profile info
   - Response: Customer object and authentication token
   - Status: Implemented ✓

2. `POST /api/marketplace/customers/login` - Login as a customer
   - Request: Email and password
   - Response: Customer object and authentication token
   - Status: Implemented ✓

3. `GET /api/marketplace/customers/profile` - Get customer profile
   - Response: Customer profile object
   - Status: Implemented ✓

4. `PUT /api/marketplace/customers/profile` - Update customer profile
   - Request: Updated profile fields
   - Response: Updated customer object
   - Status: Implemented ✓

5. `POST /api/marketplace/customers/addresses` - Add a new address
   - Request: Address details, farm alias, delivery instructions
   - Response: New address object
   - Status: Implemented ✓

6. `PUT /api/marketplace/customers/addresses/:addressId` - Update an address
   - Request: Updated address fields
   - Response: Updated address object
   - Status: Implemented ✓

7. `DELETE /api/marketplace/customers/addresses/:addressId` - Delete an address
   - Response: Success message
   - Status: Implemented ✓

8. `GET /api/marketplace/customers/orders` - Get customer order history
   - Response: Array of purchase requests
   - Status: Implemented ✓

### Fulfillment Options
1. `GET /api/farms/:farmId/fulfillment-options` - Get farm fulfillment options
   - Response: Farm fulfillment options object
   - Status: Implemented ✓

2. `PUT /api/farms/:farmId/fulfillment-options` - Update farm fulfillment options
   - Request: Updated fulfillment options (delivery/pickup availability, fees, etc.)
   - Response: Updated farm fulfillment options object
   - Status: Implemented ✓

3. `GET /api/products/:productId/fulfillment-options` - Get product-specific fulfillment options
   - Response: Product fulfillment options object
   - Status: Implemented ✓

4. `PUT /api/products/:productId/fulfillment-options` - Update product-specific fulfillment options
   - Request: Override flag and fulfillment options
   - Response: Updated product with fulfillment options
   - Status: Implemented ✓

5. `GET /api/marketplace/farms/:farmId/fulfillment-options` - Get farm fulfillment options for marketplace
   - Response: Farm fulfillment options for customer view
   - Status: Implemented ✓

6. `POST /api/marketplace/cart/fulfillment-method` - Set fulfillment method for cart
   - Request: Fulfillment method (delivery/pickup), address ID or pickup date
   - Response: Updated cart with fulfillment details
   - Status: Implemented ✓

## Frontend Components

### Product Management Page Enhancements
1. Image upload component with drag-and-drop
   - Allow multiple image selection
   - Show upload progress
   - Preview images before upload
   - Status: Implemented ✓

2. Image gallery with reordering capability
   - Drag-and-drop reordering
   - Primary image selection
   - Image deletion
   - Status: Implemented ✓

3. Marketplace visibility toggle and settings
   - Toggle for marketplace visibility
   - Marketplace-specific description editor
   - Category selection dropdown
   - Status: Implemented ✓

4. Product fulfillment options settings
   - Toggle to override farm-level fulfillment settings
   - Delivery availability toggle (when overriding)
   - Pickup availability toggle (when overriding)
   - Status: Implemented ✓

### Marketplace Pages
1. Global marketplace page (`store.nxtacre.com`)
   - Product grid with pagination
   - Filtering sidebar (categories, price range, farms)
   - Search functionality
   - Status: Implemented ✓

2. Farm-specific marketplace page (`{farmSubdomain}.nxtacre.com/store`)
   - Farm header with logo and description
   - Product grid with pagination
   - Filtering sidebar (categories, price range)
   - Status: Implemented ✓

3. Product detail page
   - Image gallery with primary image
   - Product details (name, price, description)
   - Farm information
   - Fulfillment options information
     - Available delivery/pickup options
     - Delivery fee information
     - Delivery radius (if applicable)
   - Add to cart button
   - Quantity selector
   - Status: Implemented ✓

4. Category browsing page
   - Product grid filtered by category
   - Subcategory navigation (if applicable)
   - Status: Implemented ✓

5. Search results page
   - Product grid showing search results
   - Filter refinement options
   - "No results" state with suggestions
   - Status: Implemented ✓

### Shopping Cart Components
1. Cart sidebar/modal
   - Product list with images
   - Quantity adjusters
   - Remove item buttons
   - Cart subtotal
   - Checkout button
   - "Save for later" option
   - Status: Implemented ✓

2. Checkout form
  - Customer information fields
  - Fulfillment method selection (delivery or pickup)
    - Option to choose delivery or pickup based on farm's available options
    - Display delivery fee information
    - Show minimum order amount for free delivery
  - Address selection/entry (for delivery)
    - Select from saved addresses
    - Option to add a new address
    - Delivery instructions field
  - Pickup date/time selection (for pickup)
    - Calendar interface for date selection
    - Time slot selection based on farm's availability
    - Pickup instructions display
  - Payment method selection
  - Order summary with fulfillment details
  - Submit button
  - Status: Implemented ✓

3. Order confirmation page
   - Order summary
   - Order ID and tracking information
   - Estimated delivery information
   - Multiple order requests for multi-farm orders
   - Status: Implemented ✓

### Customer Profile Components
1. Address management interface
   - Address list with edit/delete options
   - Add new address form
   - Farm alias field
   - Delivery instructions field
   - Default address selection
   - Status: Implemented ✓

2. Order history and tracking view
   - List of past orders with status
   - Order details expansion
   - Reorder functionality
   - Real-time order status updates
   - View messages from farm concerning order
   - Track driver location for in-route deliveries
   - Status: Implemented ✓

3. Saved carts view
   - List of saved carts
   - "Add to cart" button for each saved cart
   - Delete saved cart option
   - Status: Implemented ✓

### Delivery Scheduling Components
1. Farm delivery schedule management
   - Create delivery schedule options
   - Assign drivers to delivery routes
   - Link schedules to approved orders
   - Status: Implemented ✓

2. Customer delivery date selection
   - View available delivery dates/times offered by farm
   - Select preferred delivery option
   - Receive confirmation of selected delivery time
   - Status: Implemented ✓

3. Delivery tracking interface
   - Real-time driver location on map
   - Estimated arrival time updates
   - Delivery status notifications
   - Status: Implemented ✓

### Farm Feature Management
1. Customer feature toggle interface
   - Enable/disable marketplace visibility
   - Enable/disable customer order requests
   - Enable/disable delivery scheduling
   - Enable/disable driver location tracking
   - Enable/disable customer messaging
   - Status: Implemented ✓

2. Feature settings configuration
   - Customize feature behavior per farm
   - Set default preferences for new features
   - Status: Implemented ✓

3. Fulfillment options management
   - Configure delivery availability
   - Configure pickup availability
   - Set delivery fees
   - Set minimum order for free delivery
   - Define delivery radius
   - Add pickup instructions
   - Add delivery instructions
   - Status: Implemented ✓

### Email Notification Templates
1. Customer notification templates
   - Order request confirmation
   - Order request approved
   - Order request declined
   - Delivery schedule options available
   - Delivery scheduled confirmation
   - Delivery in progress with tracking link
   - Delivery completed
   - Status: Implemented ✓

2. Farm notification templates
   - New order request received
   - Customer selected delivery date
   - Delivery reminder for farm staff
   - Customer message received
   - Status: Implemented ✓

### Multi-Farm Order Handling
1. Cart segregation by farm
   - Automatic grouping of cart items by farm
   - Individual subtotals per farm
   - Status: Implemented ✓

2. Multiple order request creation
   - Automatic creation of separate order requests per farm
   - Unified checkout experience for customer
   - Individual tracking for each farm's order
   - Status: Implemented ✓

3. Consolidated order history view
   - Group related orders from same checkout
   - Individual status tracking per farm order
   - Combined and individual order details
   - Status: Implemented ✓

## Implementation Plan

### Phase 1: Database Schema Changes
- [x] Create product images table
- [x] Update products table with marketplace fields
- [x] Create customer addresses table
- [x] Update customers table with origin and global ID
- [x] Create shopping cart and purchase request tables

### Phase 2: Backend API Implementation
- [x] Implement product image upload and management endpoints
- [x] Implement marketplace product listing and search endpoints
- [x] Implement shopping cart management endpoints
- [x] Implement customer profile and address management endpoints
- [x] Implement purchase request submission
- [x] Implement purchase request notification
- [x] Implement fulfillment options endpoints

### Phase 3: Frontend Implementation
- [x] Enhance product management page with image upload and marketplace settings
  - [x] Create image upload component with drag-and-drop
  - [x] Implement image gallery with reordering capability
  - [x] Add marketplace visibility toggle and settings form
  - [x] Add product fulfillment options settings
- [x] Create marketplace browsing pages with filtering and search
  - [x] Implement global marketplace page on store.nxtacre.com
  - [x] Create farm-specific marketplace page on farm subdomains
  - [x] Build product detail page
  - [x] Develop category browsing functionality
  - [x] Implement search results page with filters
- [x] Implement shopping cart functionality
  - [x] Create cart sidebar/modal component
  - [x] Implement add to cart functionality
  - [x] Add quantity adjustment controls
  - [x] Implement save cart for later feature
  - [x] Implement multi-farm order segregation
- [x] Create customer profile and address management pages
  - [x] Build customer registration and login forms
  - [x] Create profile management interface
  - [x] Implement address management with farm aliases
  - [x] Add delivery instructions functionality
- [x] Implement purchase request submission flow
  - [x] Create checkout form
  - [x] Implement fulfillment method selection (delivery/pickup)
  - [x] Implement order confirmation page
  - [x] Add order history view with tracking features
  - [x] Implement driver location tracking for deliveries
- [x] Implement farm feature management
  - [x] Create feature toggle interface
  - [x] Implement feature settings configuration
  - [x] Create fulfillment options management interface
    - [x] Configure delivery availability
    - [x] Configure pickup availability
    - [x] Set delivery fees
    - [x] Set minimum order for free delivery
    - [x] Define delivery radius
    - [x] Add pickup instructions
    - [x] Add delivery instructions
  - [x] Implement product-level fulfillment options override
- [x] Implement delivery scheduling system
  - [x] Create farm delivery schedule management
  - [x] Build customer delivery date selection interface
  - [x] Implement delivery tracking interface
- [x] Create email notification system
  - [x] Implement customer notification templates
  - [x] Implement farm notification templates

### Phase 4: Testing and Refinement
- [x] Test all features in development environment
  - [x] Test product image management
  - [x] Test marketplace product listings on store.nxtacre.com
  - [x] Test farm-specific marketplace on farm subdomains
  - [x] Test shopping cart functionality
  - [x] Test multi-farm order handling
  - [x] Test customer profile and address management
  - [x] Test purchase request submission
  - [x] Test order tracking and status updates
  - [x] Test farm messaging system
  - [x] Test driver location tracking
  - [x] Test delivery scheduling system
  - [x] Test farm feature toggles
  - [x] Test fulfillment options (delivery/pickup)
    - [x] Test farm fulfillment options API endpoints
    - [x] Test product fulfillment options API endpoints
    - [x] Test cart fulfillment method API endpoint
    - [x] Test farm-level delivery/pickup settings
    - [x] Test delivery fee calculations
    - [x] Test minimum order for free delivery
  - [x] Test farm-level fulfillment settings
  - [x] Test product-level fulfillment overrides
  - [x] Test email notifications
- [x] Fix bugs and refine user experience
- [x] Conduct user acceptance testing
- [x] Prepare for production deployment

## Conclusion
This implementation plan outlines the steps needed to add the marketplace functionality to NxtAcre. By following this guide, we will create a robust marketplace system that allows farms to showcase their products and customers to easily find and purchase them.
