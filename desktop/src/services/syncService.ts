import authService from './authService';

/**
 * Data synchronization service for handling data synchronization between local and remote databases
 * This service provides methods for syncing data, checking sync status, and managing offline data
 */
class SyncService {
  private static instance: SyncService;
  private isSyncing: boolean = false;
  private lastSyncTime: Date | null = null;
  private isOnline: boolean = navigator.onLine;
  private syncQueue: Array<SyncOperation> = [];

  private constructor() {
    // Initialize online status listener
    window.addEventListener('online', this.handleOnlineStatusChange.bind(this));
    window.addEventListener('offline', this.handleOnlineStatusChange.bind(this));
    
    // Load sync queue from localStorage if available
    this.loadSyncQueueFromStorage();
    
    // Load last sync time from localStorage if available
    const lastSyncTimeStr = localStorage.getItem('lastSyncTime');
    if (lastSyncTimeStr) {
      this.lastSyncTime = new Date(lastSyncTimeStr);
    }
  }

  /**
   * Get the singleton instance of SyncService
   */
  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  /**
   * Handle online/offline status change
   */
  private handleOnlineStatusChange(): void {
    this.isOnline = navigator.onLine;
    
    // If we're back online and have items in the sync queue, attempt to sync
    if (this.isOnline && this.syncQueue.length > 0) {
      this.syncData();
    }
  }

  /**
   * Load sync queue from localStorage
   */
  private loadSyncQueueFromStorage(): void {
    try {
      const queueData = localStorage.getItem('syncQueue');
      if (queueData) {
        this.syncQueue = JSON.parse(queueData);
      }
    } catch (error) {
      console.error('Failed to load sync queue from storage:', error);
    }
  }

  /**
   * Save sync queue to localStorage
   */
  private saveSyncQueueToStorage(): void {
    try {
      localStorage.setItem('syncQueue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Failed to save sync queue to storage:', error);
    }
  }

  /**
   * Add an operation to the sync queue
   * @param operation The operation to add to the queue
   */
  public addToSyncQueue(operation: SyncOperation): void {
    this.syncQueue.push(operation);
    this.saveSyncQueueToStorage();
    
    // If we're online, attempt to sync immediately
    if (this.isOnline) {
      this.syncData();
    }
  }

  /**
   * Sync data between local and remote databases
   * @returns Promise resolving to a boolean indicating success
   */
  public async syncData(): Promise<boolean> {
    // If already syncing or offline, don't start another sync
    if (this.isSyncing || !this.isOnline) {
      return false;
    }
    
    try {
      this.isSyncing = true;
      
      // Check if user is authenticated
      if (!authService.isLoggedIn()) {
        throw new Error('User not authenticated');
      }
      
      // Process sync queue
      if (this.syncQueue.length > 0) {
        await this.processSyncQueue();
      }
      
      // Fetch updates from server
      await this.fetchUpdatesFromServer();
      
      // Update last sync time
      this.lastSyncTime = new Date();
      localStorage.setItem('lastSyncTime', this.lastSyncTime.toISOString());
      
      return true;
    } catch (error) {
      console.error('Sync failed:', error);
      return false;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Process the sync queue
   */
  private async processSyncQueue(): Promise<void> {
    // In a real implementation, this would send the operations to the server
    // For now, we'll just simulate success and clear the queue
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Clear the queue
    this.syncQueue = [];
    this.saveSyncQueueToStorage();
  }

  /**
   * Fetch updates from the server
   */
  private async fetchUpdatesFromServer(): Promise<void> {
    // In a real implementation, this would fetch updates from the server
    // For now, we'll just simulate a delay
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  /**
   * Get the current sync status
   * @returns Object with sync status information
   */
  public getSyncStatus(): SyncStatus {
    return {
      isSyncing: this.isSyncing,
      lastSyncTime: this.lastSyncTime,
      isOnline: this.isOnline,
      pendingOperations: this.syncQueue.length,
    };
  }

  /**
   * Check if the device is online
   * @returns True if online, false otherwise
   */
  public isDeviceOnline(): boolean {
    return this.isOnline;
  }

  /**
   * Get the last sync time
   * @returns The last sync time or null if never synced
   */
  public getLastSyncTime(): Date | null {
    return this.lastSyncTime;
  }

  /**
   * Get the number of pending operations in the sync queue
   * @returns The number of pending operations
   */
  public getPendingOperationsCount(): number {
    return this.syncQueue.length;
  }
}

/**
 * Sync operation interface
 */
export interface SyncOperation {
  /**
   * Type of operation (create, update, delete)
   */
  type: 'create' | 'update' | 'delete';
  
  /**
   * Entity type (e.g., 'task', 'farm', 'equipment')
   */
  entity: string;
  
  /**
   * Entity ID (for update and delete operations)
   */
  id?: number;
  
  /**
   * Entity data (for create and update operations)
   */
  data?: any;
  
  /**
   * Timestamp when the operation was created
   */
  timestamp: number;
}

/**
 * Sync status interface
 */
export interface SyncStatus {
  /**
   * Whether a sync is currently in progress
   */
  isSyncing: boolean;
  
  /**
   * The last time a sync was completed
   */
  lastSyncTime: Date | null;
  
  /**
   * Whether the device is currently online
   */
  isOnline: boolean;
  
  /**
   * Number of operations pending in the sync queue
   */
  pendingOperations: number;
}

export default SyncService.getInstance();